<resources>																			
    <string name="cast_skill">释放了战技</string>																			
    <string name="gain_permanent_debuff">获得了永久减益</string>																			
    <string name="gain_permanent_buff">获得了永久增益</string>																			
    <string name="execute_failed">释放了斩杀，不过目标免死，斩杀无效</string>																			
    <string name="execute_done">释放了斩杀，成功击杀了</string>																			
    <string name="gain_avoid_death">获得了免死</string>																			
    <string name="avoid_death_failed">获得了免死,但是对方无视免死,免死无效</string>																			
    <string name="direct_win">被释放，你直接获得了战斗胜利</string>																			
    <string name="gain_enhancement">获得了战技强化</string>																			
    <string name="enhancement_skip">但是强化目标战技未学习</string>																			
    <string name="damage_record">伤害记录: </string>																			
    <string name="attacker_with_comma">进攻方:</string>																			
    <string name="defender_with_comma">防御方:</string>																			
    <string name="initial_damage_with_comma">初始伤害:</string>																			
    <string name="fatal_with_comma">暴击:</string>																			
    <string name="fatal_damage_with_comma">暴伤:</string>																			
    <string name="race_damage_with_comma">兵种增伤:</string>																			
    <string name="normal_attack_with_comma">普攻增伤:</string>																			
    <string name="skill_damage_with_comma">其他增伤:</string>																			
    <string name="attacker_damage_inc">伤害增减(进攻方):</string>																			
    <string name="attacker_damage_inc_all">所有伤害增减(进攻方):</string>																			
    <string name="pierce_attacker">穿透(进攻方):</string>																			
    <string name="damage_value">伤害值:</string>																			
    <string name="defender_init_defense">防御方初始防御</string>																			
    <string name="defender_real_defense">防御方实际防御</string>																			
    <string name="defender_reduce_damage">防御减伤效果(防守方):</string>																			
    <string name="defender_reduce_value">减伤(防御方):</string>																			
    <string name="defender_race_reduce">防御方兵种减伤:</string>																			
    <string name="defender_all_reduce">防御方所有伤害减伤:</string>																			
    <string name="defender_immune">防御方免疫:</string>																			
    <string name="defender_holy_shield">防御方星盾:</string>																			
    <string name="defender_dodge">格挡:</string>																			
    <string name="single_damage">单次伤害上限:</string>																			
    <string name="final_damage">最终伤害:</string>																			
    <string name="single_damage_protect">单次伤害保护:</string>																			
    <string name="yes">是</string>																			
    <string name="no">否</string>																			
    <string name="skill_typ1">物理系</string>																			
    <string name="skill_type2">法术系</string>																			
    <string name="skill_type3">火系</string>																			
    <string name="skill_type4">毒系</string>																			
    <string name="skill_type5">真实系</string>																			
    <string name="ones_turn">的回合:</string>																			
    <string name="over_turn">你没能在限定回合内击败对手，战斗失败</string>																			
    <string name="release">释放</string>																			
    <string name="gain">获得</string>																			
    <string name="you_release">你释放</string>																			
    <string name="let">使</string>																			
    <string name="lost">损失</string>																			
    <string name="control_immune">控制免疫，无法被</string>																			
    <string name="trigger_multi_cast">触发了士气高涨</string>																			
    <string name="forbid_heal_tip">受到【禁疗】效果，无法恢复兵力。</string>																			
    <string name="frenzy_tips">受到【暴走】影响，随机选择目标。</string>																			
    <string name="disarm_tips">受到【缴械】效果，无法发动普通攻击。</string>																			
    <string name="silent_tips">受到【沉默】效果，无法释放主动战技。</string>																			
    <string name="palsy_tips">受到【眩晕】效果，无法释放触发战技。</string>																			
    <string name="ban_skill_tip1">受到【封印</string>																			
    <string name="ban_skill_tip2">】效果，无法释放该战技.</string>																			
    <string name="heal">恢复</string>																			
    <string name="layer">层</string>																			
    <string name="unstack">不可堆叠</string>																			
    <string name="infinite">无限</string>																			
    <string name="rage">暴走</string>																			
    <string name="unbreakable">无敌</string>																			
    <string name="holy_shield">星盾</string>																			
    <string name="prop1">武勇</string>																			
    <string name="prop2">智略</string>																			
    <string name="prop3">统帅</string>																			
    <string name="prop4">内政</string>																			
    <string name="prop5">魅力</string>																			
    <string name="prop6">空一个</string>																			
    <string name="race1">盾兵</string>																			
    <string name="race2">枪兵</string>																			
    <string name="race3">弓兵</string>																			
    <string name="race4">骑兵</string>																			
    <string name="race5">刺客</string>																			
    <string name="race6">炮兵</string>																			
    <string name="race7">谋士</string>																			
    <string name="race8">道士</string>																			
    <string name="race9">药师</string>																			
    <string name="dispelled">破解了</string>																			
    <string name="cant_dispel">免疫减益破解，无法破解</string>																			
    <string name="cant_dispel2">免疫增益破解，无法破解</string>																			
    <string name="defeated_the_enemy">你击败了敌人</string>																			
    <string name="level">级</string>																			
    <string name="level1">级</string>																			
    <string name="death">死亡</string>																			
    <string name="continuous_damage">持续伤害</string>																			
    <string name="continued_treatment">持续治疗</string>																			
    <string name="drink">吸取</string>																			
    <string name="i_achieved_positive_results">我获得增益</string>																			
    <string name="i_achieved_negative_effects">我获得减益</string>																			
    <string name="dispel_the_opponent_buff">破解对方增益</string>																			
    <string name="dispel_your_own_buff">破解自己增益</string>																			
    <string name="damage_self">伤害自己</string>																			
    <string name="cure_yourself">恢复自己兵力</string>																			
    <string name="free_sb_from_death">释放免死</string>																			
    <string name="release_a_skill">释放战技</string>																			
    <string name="got_damage">受到伤害</string>																			
    <string name="got_hurt">造成伤害</string>																			
    <string name="successful_block">成功格挡</string>																			
    <string name="start_of_the_round">回合开始</string>																			
    <string name="end_of_the_round">回合结束</string>																			
    <string name="call_upon_a_servant">召唤增援</string>																			
    <string name="no_dispelling_of_target_buffs">无可破解目标效果</string>																			
    <string name="minion_already_exists">已存在增援,无法继续召唤</string>																			
    <string name="summoned">召唤了</string>																			
    <string name="designated_skill_strike_rate_increase">暴击率提升</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">释放次数提升</string>																			
    <string name="immunity_to_blocking_for_specified_skills">免疫格挡</string>																			
    <string name="designated_skills_ignore_stance">无视护盾</string>																			
    <string name="designated_skills_ignore_invincibility">无视无敌/免死</string>																			
    <string name="designated_skills_ignore_taunts">无视嘲讽</string>																			
    <string name="cannot_be_dispersed">增益/减益无法被破解</string>																			
    <string name="increased_damage_dealt_by_designated_skills">伤害提高</string>																			
    <string name="designated_skills_deal_less_damage">伤害降低</string>																			
    <string name="designated_skill_cd_increase">冷却提高</string>																			
    <string name="designated_skill_cd_decrease">冷却降低</string>																			
    <string name="increased_number_of_rounds">增益/减益持续回合数提高</string>																			
    <string name="decreased_number_of_rounds">增益/减益持续回合数减少</string>																			
    <string name="increased_probability_of_releasing_designated_skills">释放概率提高</string>																			
    <string name="reduced_probability_of_releasing_designated_skills">释放概率降低</string>																			
    <string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">每回合限制释放次数提高</string>																			
    <string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">每回合限制释放次数降低</string>																			
    <string name="increase_in_the_number_of_restricted_releases">整场战斗限制释放次数提高</string>																			
    <string name="reduced_limit_on_number_of_releases">整场战斗限制释放次数降低</string>																			
    <string name="increased_blood_absorption_rate_of_skills">吸取提高</string>																			
    <string name="designated_skills_ignore_enemies">忽略敌方防御</string>																			
    <string name="skill_damage_type_changes_to">伤害类型变为</string>																			
    <string name="designated_skills_vs_race">对兵种</string>																			
    <string name="damage_increase">伤害提高</string>																			
    <string name="damage_decrease">伤害降低</string>																			
    <string name="gain_split_effect">获得【溅射】</string>																			
    <string name="fatal">暴击</string>																			
    <string name="damage1">物理</string>																			
    <string name="damage2">法术</string>																			
    <string name="damage3">火</string>																			
    <string name="damage4">毒</string>																			
    <string name="damage5">真实</string>																			
    <string name="defense1">防御</string>																			
    <string name="defense2">抗性</string>																			
    <string name="attack">攻击</string>																			
    <string name="attack_tips">决定了各类伤害的强度值</string>																			
    <string name="defense1_tips">受到物理伤害时起到减伤的作用</string>																			
    <string name="defense2_tips">受到法术伤害时起到减伤的作用</string>																			
    <string name="defense3_tips">受到火伤害时起到减伤的作用</string>																			
    <string name="defense4_tips">受到毒伤害时起到减伤的作用</string>																			
    <string name="hp">兵力</string>																			
    <string name="hp_tips">兵力为零时，英雄阵亡</string>																			
    <string name="fatal_rate">暴击</string>																			
    <string name="fatal_rate_tips">造成额外大量伤害的概率</string>																			
    <string name="fatal_damage">暴伤</string>																			
    <string name="fatal_damage_tips">触发暴击时造成的额外伤害比例</string>																			
    <string name="dodge">格挡</string>																			
    <string name="dodge_tips">成功格挡时，将完全避免本次伤害</string>																			
    <string name="speed">速度</string>																			
    <string name="speed_tips">决定了英雄行动的优先顺序，速度越高越早行动</string>																			
    <string name="skill_tree">系</string>																			
</resources>																			
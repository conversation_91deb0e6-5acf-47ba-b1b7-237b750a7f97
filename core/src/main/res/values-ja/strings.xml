<resources>																			
<string name="cast_skill">戦闘スキルを解き放つ</string>																			
<string name="gain_permanent_debuff">永続的なデバフを受けました</string>																			
<string name="gain_permanent_buff">永続的な利益を受け取りました</string>																			
<string name="execute_failed">斬首は解除されたが、標的は死を免れ、斬首は効果がなかった。</string>																			
<string name="execute_done">斬首が解除され、殺害に成功した</string>																			
<string name="gain_avoid_death">死からの免疫を獲得</string>																			
<string name="avoid_death_failed">不死特権を獲得したが、相手が不死特権を無視したため不死特効は無効となった。</string>																			
<string name="direct_win">解放されると、戦いに直接勝つことができます</string>																			
<string name="gain_enhancement">戦闘スキル強化を獲得</string>																			
<string name="enhancement_skip">ただし強化対象の戦闘スキルは習得していない</string>																			
<string name="damage_record">怪我の記録:</string>																			
<string name="attacker_with_comma">アタッカー：</string>																			
<string name="defender_with_comma">ディフェンダー:</string>																			
<string name="initial_damage_with_comma">初期ダメ：</string>																			
<string name="fatal_with_comma">暴撃率:</string>																			
<string name="fatal_damage_with_comma">暴撃ダメ:</string>																			
<string name="race_damage_with_comma">ユニットダメの増加:</string>																			
<string name="normal_attack_with_comma">基本攻撃ダメ増加:</string>																			
<string name="skill_damage_with_comma">その他のダメ増加:</string>																			
<string name="attacker_damage_inc">ダメ増減（攻撃側）：</string>																			
<string name="attacker_damage_inc_all">全ダメ増減（攻撃側）：</string>																			
<string name="pierce_attacker">ペネトレーション（攻撃側）：</string>																			
<string name="damage_value">ダメ値:</string>																			
<string name="defender_init_defense">ディフェンダーの初期ディフェンス</string>																			
<string name="defender_real_defense">ディフェンダーの実際のディフェンス</string>																			
<string name="defender_reduce_damage">防御ダメ軽減効果（防御側）：</string>																			
<string name="defender_reduce_value">ダメ軽減(防御側):</string>																			
<string name="defender_race_reduce">防御側の部隊のダメ軽減:</string>																			
<string name="defender_all_reduce">防御側のすべてのダメ軽減:</string>																			
<string name="defender_immune">防御側の免疫:</string>																			
<string name="defender_holy_shield">防御用スターシールド:</string>																			
<string name="defender_dodge">グレード:</string>																			
<string name="single_damage">単一ダメの制限:</string>																			
<string name="final_damage">最終ダメ:</string>																			
<string name="single_damage_protect">単一ダメ保護:</string>																			
<string name="yes">はい</string>																			
<string name="no">いいえ</string>																			
<string name="skill_typ1">物理</string>																			
<string name="skill_type2">呪文</string>																			
<string name="skill_type3">火系</string>																			
<string name="skill_type4">毒</string>																			
<string name="skill_type5">リアル</string>																			
<string name="ones_turn">ラウンド：</string>																			
<string name="over_turn">制限ラウンド内に相手を倒すことができず、戦闘は失敗しました。</string>																			
<string name="release">リリース</string>																			
<string name="gain">得る</string>																			
<string name="you_release">あなたは解放します</string>																			
<string name="let">作る</string>																			
<string name="lost">損失</string>																			
<string name="control_immune">免疫をコントロールすることはできません</string>																			
<string name="trigger_multi_cast">士気の高揚を引き起こした</string>																			
<string name="forbid_heal_tip">【治癒の禁止】の効果中は兵力を回復できません。</string>																			
<string name="frenzy_tips">[激怒]の影響を受け、ターゲットをランダムに選択します。 </string>																			
<string name="disarm_tips">[武装解除]効果中は通常の攻撃を行うことができません。 </string>																			
<string name="silent_tips">[沈黙]効果中はアクティブな戦闘スキルが解放できなくなります。</string>																			
<string name="palsy_tips">[めまい]の影響では、発動した戦闘スキルを解放できなくなります。</string>																			
<string name="ban_skill_tip1">[シールによる]</string>																			
<string name="ban_skill_tip2">』の効果がある場合、この戦闘スキルは解放できない。</string>																			
<string name="heal">回復する</string>																			
<string name="layer">層</string>																			
<string name="unstack">積み重ねることはできません</string>																			
<string name="infinite">無制限</string>																			
<string name="rage">暴走</string>																			
<string name="unbreakable">無敵</string>																			
<string name="holy_shield">スターシールド</string>																			
<string name="prop1">強さ</string>																			
<string name="prop2">知恵</string>																			
<string name="prop3">指揮</string>																			
<string name="prop4">内政</string>																			
<string name="prop5">魅力</string>																			
<string name="prop6">空のもの</string>																			
<string name="race1">盾兵</string>																			
<string name="race2">槍兵</string>																			
<string name="race3">弓兵</string>																			
<string name="race4">騎兵</string>																			
<string name="race5">アサシン</string>																			
<string name="race6">砲兵</string>																			
<string name="race7">戦略家</string>																			
<string name="race8">道士</string>																			
<string name="race9">薬剤師</string>																			
<string name="dispelled">ひび割れた</string>																			
<string name="cant_dispel">免疫デバフクラック、クラック不可</string>																			
<string name="cant_dispel2">免疫獲得クラック、クラックできない</string>																			
<string name="defeated_the_enemy">あなたは敵を倒しました</string>																			
<string name="level">クラス</string>																			
<string name="level1">クラス</string>																			
<string name="death">死ぬ</string>																			
<string name="continuous_damage">継続的なダメ</string>																			
<string name="continued_treatment">継続的な治療</string>																			
<string name="drink">描く</string>																			
<string name="i_achieved_positive_results">バフを獲得します</string>																			
<string name="i_achieved_negative_effects">デバフを受けます</string>																			
<string name="dispel_the_opponent_buff">相手のゲインを崩す</string>																			
<string name="dispel_your_own_buff">自分自身の利益をハックする</string>																			
<string name="damage_self">自分を傷つける</string>																			
<string name="cure_yourself">自分自身の力を回復する</string>																			
<string name="free_sb_from_death">死から自由になる</string>																			
<string name="release_a_skill">戦闘スキルを解き放つ</string>																			
<string name="got_damage">傷つく</string>																			
<string name="got_hurt">損害を与える</string>																			
<string name="successful_block">ブロック成功</string>																			
<string name="start_of_the_round">ラウンド開始</string>																			
<string name="end_of_the_round">ラウンドの終わり</string>																			
<string name="call_upon_a_servant">援軍を呼ぶ</string>																			
<string name="no_dispelling_of_target_buffs">ターゲット効果をクラックできません</string>																			
<string name="minion_already_exists">増援はすでに存在するため、召喚できません</string>																			
<string name="summoned">召喚された</string>																			
<string name="designated_skill_strike_rate_increase">クリティカル率上昇</string>																			
<string name="increase_in_the_number_of_times_a_given_skill_can_be_cast">リリース時間の増加</string>																			
<string name="immunity_to_blocking_for_specified_skills">ブロックする免疫</string>																			
<string name="designated_skills_ignore_stance">シールドを無視する</string>																			
<string name="designated_skills_ignore_invincibility">無敵無視/死を回避</string>																			
<string name="designated_skills_ignore_taunts">挑発を無視する</string>																			
<string name="cannot_be_dispersed">バフ/デバフはハッキングできません</string>																			
<string name="increased_damage_dealt_by_designated_skills">ダメ増加</string>																			
<string name="designated_skills_deal_less_damage">ダメ軽減</string>																			
<string name="designated_skill_cd_increase">冷却の向上</string>																			
<string name="designated_skill_cd_decrease">クールダウンの減少</string>																			
<string name="increased_number_of_rounds">バフ/デバフ持続時間ラウンドの増加</string>																			
<string name="decreased_number_of_rounds">バフ/デバフの持続時間が短縮されます。</string>																			
<string name="increased_probability_of_releasing_designated_skills">排出確率アップ</string>																			
<string name="reduced_probability_of_releasing_designated_skills">リリースの確率を下げる</string>																			
<string name="increase_in_the_number_of_times_a_given_skill_can_be_released_per_round">ラウンドごとの解放制限の増加</string>																			
<string name="reduced_limit_on_the_number_of_times_a_given_skill_can_be_released_per_round">ラウンドごとのリリース制限の減少</string>																			
<string name="increase_in_the_number_of_restricted_releases">バトル全体の解放数が増加しました。</string>																			
<string name="reduced_limit_on_number_of_releases">戦闘全体での解放数が減少しました。</string>																			
<string name="increased_blood_absorption_rate_of_skills">吸収して改善する</string>																			
<string name="designated_skills_ignore_enemies">敵の防御を無視する</string>																			
<string name="skill_damage_type_changes_to">ダメタイプは次のように変化します。</string>																			
<string name="designated_skills_vs_race">腕に対して</string>																			
<string name="damage_increase">ダメ増加</string>																			
<string name="damage_decrease">ダメ軽減</string>																			
<string name="gain_split_effect">【スプラッシュ】を入手</string>																			
<string name="fatal">暴撃率</string>																			
<string name="damage1">物理</string>																			
<string name="damage2">スペル</string>																			
<string name="damage3">火</string>																			
<string name="damage4">毒</string>																			
<string name="damage5">現実</string>																			
<string name="defense1">防衛</string>																			
<string name="defense2">抵抗</string>																			
<string name="attack">攻撃</string>																			
<string name="attack_tips">さまざまなタイプのダメの強度値を決定します</string>																			
<string name="defense1_tips">物理ダメを受けた際にダメを軽減する役割を担う</string>																			
<string name="defense2_tips">魔法ダメを受けた際にダメを軽減する役割を担う</string>																			
<string name="defense3_tips">火ダメを受けた際にダメを軽減する役割を担う</string>																			
<string name="defense4_tips">毒ダメを受けた際にダメを軽減する役割を担う</string>																			
<string name="hp">兵力</string>																			
<string name="hp_tips">兵力がゼロになると主人公は戦闘で死亡します</string>																			
<string name="fatal_rate">暴撃率</string>																			
<string name="fatal_rate_tips">追加で大ダメを与える確率</string>																			
<string name="fatal_damage">暴撃ダメ</string>																			
<string name="fatal_damage_tips">クリティカルヒット発動時の追加ダメ率</string>																			
<string name="dodge">ガード</string>																			
<string name="dodge_tips">ガードに成功すると、このダメは完全に回避されます</string>																			
<string name="speed">スピード</string>																			
<string name="speed_tips">ヒーローの行動の優先順位を決定します。速度が高いほど、ヒーローは早く行動します。</string>																			
<string name="skill_tree">系</string>																			
</resources>																			
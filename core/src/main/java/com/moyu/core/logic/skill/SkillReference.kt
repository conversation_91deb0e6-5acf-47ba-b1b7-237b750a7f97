package com.moyu.core.logic.skill

import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlin.math.absoluteValue


fun getReference(reference: String, field: BattleField, skillOwner: Role, target: Role? = null, triggerSkill: Skill? = null): Double {
    val peer = target?: field.getDirectPeer(skillOwner) ?: return 100.0
    val listOfIndex = reference.split("*", "-", "+", "/").map { it.toInt()
    }
    val listOfCalMark = reference.filter { it in "+-*/" }.toMutableList().apply {
        this.add(0,'+')
    }
    val listValue = listOfIndex.map {
        when (it) {
            in 1..15 -> skillOwner.getCurrentProperty().getPropertyByTarget(it)
            in 1001..1015 -> skillOwner.getDefaultProperty().getPropertyByTarget(it - 1000)
            16 -> triggerSkill?.takeIf { trigger-> trigger.isWounded() }?.damageResult?.damageValue?.finalDamage?: 100
            17 -> triggerSkill?.takeIf { trigger-> trigger.isCastDamage() }?.damageResult?.damageValue?.finalDamage?: 100
            18 -> triggerSkill?.takeIf { trigger-> trigger.isHealing() }?.healResult?.healValue?: 100
            19 -> skillOwner.getShowGoodBuff().size
            20 -> skillOwner.getShowBadBuff().size
            21 -> skillOwner.getDispelableGoodBuff().size
            22 -> skillOwner.getDispelableBadBuff().size
            30 -> skillOwner.allShield()
            in 31..40 -> skillOwner.shield(DamageType.fromTypeValue(it - 30)!!)
            41 -> skillOwner.allTypeShield()
            in 10000..19999-> {
                val buffId = it.absoluteValue - 10000
                skillOwner.getBuffLayer(buffId)
            }
            in 101..115 -> peer.getCurrentProperty().getPropertyByTarget(it - 100)
            in 1101..1115 -> peer.getOriginProperty().getPropertyByTarget(it - 1100)
            116 -> triggerSkill?.takeIf { trigger-> trigger.isWounded() }?.damageResult?.damageValue?.finalDamage?: 100
            117 -> triggerSkill?.takeIf { trigger-> trigger.isCastDamage() }?.damageResult?.damageValue?.finalDamage?: 100
            118 -> triggerSkill?.takeIf { trigger-> trigger.isHealing() }?.healResult?.healValue?: 100
            119 -> peer.getShowGoodBuff().size
            120 -> peer.getShowBadBuff().size
            121 -> peer.getDispelableGoodBuff().size
            122 -> peer.getDispelableBadBuff().size
            130 -> peer.allShield()
            in 131..140 -> peer.shield(DamageType.fromTypeValue(it - 130)!!)
            141 -> peer.allTypeShield()
            in 20000..29999-> {
                val buffId = it.absoluteValue - 20000
                peer.getBuffLayer(buffId)
            }
            else -> 100
        }
    }
    var result = 0.0
    listValue.forEachIndexed { index, value ->
        result = when(listOfCalMark[index]) {
            '+'-> result + value.toDouble()
            '-'-> result - value.toDouble()
            '*'-> result * value.toDouble()
            else -> result / value.toDouble()
        }
    }
    return result
}
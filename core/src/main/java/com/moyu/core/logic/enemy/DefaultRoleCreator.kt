package com.moyu.core.logic.enemy

import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Race
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

object DefaultRoleCreator : RoleCreator {
    override fun create(race: Race, diffProperty: Property, extraSkills: List<Int>, identifier: Identifier): Role {
        return createDetailed(race, diffProperty, identifier, extraSkills)
    }
}
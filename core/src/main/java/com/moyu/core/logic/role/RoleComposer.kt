package com.moyu.core.logic.role

import com.moyu.core.logic.buff.BuffCarrier
import com.moyu.core.logic.property.PropertyHolder
import com.moyu.core.logic.skill.TriggerType
import com.moyu.core.logic.skill.isDot
import com.moyu.core.model.Buff
import com.moyu.core.model.Race
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.enhancement10CDInc
import com.moyu.core.model.skill.enhancement11CDDec
import com.moyu.core.model.skill.enhancement12SkillBuffInc
import com.moyu.core.model.skill.enhancement13SkillBuffDec
import com.moyu.core.model.skill.enhancement14SkillRateInc
import com.moyu.core.model.skill.enhancement15SkillRateDec
import com.moyu.core.model.skill.enhancement1Fatal
import com.moyu.core.model.skill.enhancement20SuckBlood
import com.moyu.core.model.skill.enhancement2DoubleSkill
import com.moyu.core.model.skill.enhancement3ImmuneDodge
import com.moyu.core.model.skill.enhancement4IgnoreShield
import com.moyu.core.model.skill.enhancement5IgnoreImmune
import com.moyu.core.model.skill.enhancement61Divide
import com.moyu.core.model.skill.enhancement6IgnoreTaunt
import com.moyu.core.model.skill.enhancement7DispelImmune
import com.moyu.core.model.skill.enhancement8SkillBoost
import com.moyu.core.model.skill.enhancement9SkillWeaken
import com.moyu.core.model.skill.isRaceDamageDecEnhancement
import com.moyu.core.model.skill.isRaceDamageIncEnhancement
import com.moyu.core.model.skill.matchDamage
import com.moyu.core.util.chance
import com.moyu.core.util.chance100
import kotlin.math.max
import kotlin.math.roundToInt

/**
 * 有一些判定，但从buff无法得到结论，需要综合属性，buff和技能本身
 */
class RoleComposer(
    private val propertyHolder: PropertyHolder,
    private val buffCarrier: BuffCarrier,
) : BattlePower {

    override fun isFatal(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        val extraRate =
            targetSkill.getAllEnhancements().filter { it.enhancementId == enhancement1Fatal }
                .sumOf { it.enhancementValue }
        return (propertyHolder.getCurrentProperty().fatalRate + extraRate).chance()
    }

    override fun isImmuneAvoid(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        return targetSkill.getAllEnhancements()
            .any { it.enhancementId == enhancement5IgnoreImmune } || buffCarrier.isAvoidDeathImmune()
    }

    override fun skillTimes(skill: Skill): Int {
        val extraTimes =
            skill.getAllEnhancements().filter { it.enhancementId == enhancement2DoubleSkill }
                .sumOf { it.enhancementValue }
        val propertyTimes = if (skill.isNormalAttackType()) propertyHolder.getCurrentProperty().normalAttackTimes else propertyHolder.getCurrentProperty().activeSkillTimes
        return max(0, (propertyTimes + extraTimes).roundToInt())
    }

    override fun isDodgeImmune(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        if (targetSkill.getAllEnhancements()
                .any { it.enhancementId == enhancement3ImmuneDodge }
        ) return true
        return buffCarrier.isDodgeImmune()
    }

    override fun isShieldIgnore(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        if (targetSkill.getAllEnhancements()
                .any { it.enhancementId == enhancement4IgnoreShield }
        ) return true
        return (buffCarrier.isShieldIgnore())
    }

    override fun skillDamageInc(skill: Skill, damageType: DamageType): Double {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        return (targetSkill.getAllEnhancements()
            .filter { it.enhancementId == enhancement8SkillBoost }
            .sumOf { it.enhancementValue } - targetSkill.getAllEnhancements()
            .filter { it.enhancementId == enhancement9SkillWeaken }
            .sumOf { it.enhancementValue }) / 100
    }

    override fun raceDamageInc(skill: Skill, race: Race): Double {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        val rawInc = propertyHolder.getCurrentProperty().getDamageIncreaseByRaceType(race.raceType)
        return rawInc + (targetSkill.getAllEnhancements()
            .filter { it.enhancementId.type.isRaceDamageIncEnhancement(race.raceType) }
            .sumOf { it.enhancementValue } - targetSkill.getAllEnhancements()
            .filter { it.enhancementId.type.isRaceDamageDecEnhancement(race.raceType) }
            .sumOf { it.enhancementValue }) / 100
    }

    override fun getPierceByType(skill: Skill, damageType: DamageType): Double {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        val extraPierce =
            targetSkill.getAllEnhancements().filter { it.enhancementId.matchDamage(damageType) }
                .sumOf { it.enhancementValue }
        return (propertyHolder.getCurrentProperty().getPierceByType(damageType) + extraPierce) / 100
    }

    override fun suckBloodRate(skill: Skill): Double {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        val extraSuckBlood =
            targetSkill.getAllEnhancements().filter { it.enhancementId == enhancement20SuckBlood }
                .sumOf { it.enhancementValue }
        return extraSuckBlood + propertyHolder.getCurrentProperty().getRealSuckBloodRate()
    }

    override fun getChance(skill: Skill, triggerType: Int, rawRate: Double): Double {
        val extraRate =
            skill.getAllEnhancements().filter { it.enhancementId == enhancement14SkillRateInc }
                .sumOf { it.enhancementValue } - skill.getAllEnhancements()
                .filter { it.enhancementId == enhancement15SkillRateDec }
                .sumOf { it.enhancementValue }
        return (when (triggerType) {
            TriggerType.ACTIVE.value -> {
                propertyHolder.getCurrentProperty().activeSkillRate + rawRate
            }

            TriggerType.TRIGGER.value -> {
                propertyHolder.getCurrentProperty().triggerSkillRate + rawRate
            }

            else -> rawRate
        } + extraRate)
    }

    override fun getRealCoolDown(skill: Skill): Int {
        val rawCoolDown = skill.coolDown
        val extraCoolDown =
            skill.getAllEnhancements().filter { it.enhancementId == enhancement10CDInc }
                .sumOf { it.enhancementValue } - skill.getAllEnhancements()
                .filter { it.enhancementId == enhancement11CDDec }.sumOf { it.enhancementValue }
        return (rawCoolDown + propertyHolder.getCurrentProperty().coolDownEffect + extraCoolDown.toInt())
    }

    override fun getHealRate(): Double {
        return propertyHolder.getCurrentProperty().healEffect / 100
    }

    override fun isDodge(): Boolean {
        return propertyHolder.getCurrentProperty().getRealDodgeRate().chance100()
    }

    override fun getBuffContinueIncrease(buff: Buff): Int {
        val enhancement = (buff.skill?.getAllEnhancements()
            ?.filter { it.enhancementId == enhancement12SkillBuffInc }
            ?.sumOf { it.enhancementValue } ?: 0).toInt() - (buff.skill?.getAllEnhancements()
            ?.filter { it.enhancementId == enhancement13SkillBuffDec }
            ?.sumOf { it.enhancementValue } ?: 0).toInt()
        val controlEffects = (if (buff.id in 4001..4005) propertyHolder.getCurrentProperty().controlEffects[buff.id - 4001] else 0)
        val controlAllEffects = if (buff.buffType == 1) {
            // 增益延长缩短
            propertyHolder.getCurrentProperty().buffEffect
        } else {
            // 减益延长缩短
            propertyHolder.getCurrentProperty().debuffEffect
        }
        return controlEffects + controlAllEffects + enhancement
    }

    override fun cantBeDispelBuff(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        if (targetSkill.getAllEnhancements()
                .any { it.enhancementId == enhancement7DispelImmune }
        ) return false
        return buffCarrier.cantBeDispelBuff()
    }

    override fun cantBeDispelDebuff(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        if (targetSkill.getAllEnhancements()
                .any { it.enhancementId == enhancement7DispelImmune }
        ) return false
        return buffCarrier.cantBeDispelDebuff()
    }

    override fun isTauntImmune(skill: Skill): Boolean {
        val targetSkill = if (skill.isDot()) {
            skill.buffInfo!!.skill!!
        } else skill
        if (targetSkill.getAllEnhancements()
                .any { it.enhancementId == enhancement6IgnoreTaunt }
        ) return true
        return buffCarrier.isTauntImmune()
    }

    override fun isDivideAttack(skill: Skill): Boolean {
        if (skill.getAllEnhancements().any { it.enhancementId == enhancement61Divide }) return true
        return buffCarrier.isDivideAttack()
    }
}
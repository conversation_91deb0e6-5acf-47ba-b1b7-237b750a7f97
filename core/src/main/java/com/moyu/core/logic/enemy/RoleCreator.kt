package com.moyu.core.logic.enemy

import com.moyu.core.GameCore
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.level.DefaultLevelController
import com.moyu.core.model.Race
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.util.RANDOM

interface RoleCreator {
    fun create(race: Race, diffProperty: Property, extraSkills: List<Int> = emptyList(), identifier: Identifier): Role
    fun createDetailed(
        race: Race,
        diffProperty: Property,
        roleIdentifier: Identifier,
        extraSkills: List<Int>
    ): Role {
        return Role(
            roleIdentifier = roleIdentifier,
            updateId = RANDOM.nextLong(),
            roleLevel = DefaultLevelController().apply {
                setLevel(1)
            }).apply {
            this.setRace(race)
            this.createSolidSkills(race)
            if (!roleIdentifier.isPlayer()) {
                this.createRandomSkills(race, extraSkills)
            }
            this.setProperty(race, diffProperty)
        }
    }
}

fun Role.setProperty(race: Race, diffProperty: Property) {
    val targetProperty = (race.getProperty() + diffProperty).ensureNotNegative()
    setInitProperty(targetProperty)
    setPropertyToDefault()
}

fun Role.createSolidSkills(race: Race) {
    race.skillId.filter { it != 0 }.forEach { id ->
        val skill = GameCore.instance.getSkillPool().first { it.id == id }
        this.learnSkill(skill, this.roleIdentifier)
    }
}

fun Role.createRandomSkills(race: Race, extraSkills: List<Int>) {
    race.randomSkillId.shuffled(RANDOM).take(race.randomSkillNum.first()).forEach { skillId ->
        val skill = GameCore.instance.getSkillPool().first { it.id == skillId }
        this.learnSkill(skill, this.roleIdentifier)
    }
    extraSkills.forEach { skillId ->
        val skill = GameCore.instance.getSkillPool().first { it.id == skillId }
        this.learnSkill(skill, this.roleIdentifier)
    }
}
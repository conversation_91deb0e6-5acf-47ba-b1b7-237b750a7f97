package com.moyu.core.config

import com.moyu.core.model.Event

class EventConfigParser : ConfigParser<Event> {
    override fun parse(line: String): Event {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val age = words[i++].trim().split(",").map { it.toInt() }
        val appear = words[i++].trim().toInt()
        val same = words[i++].trim().toInt()
        val front = words[i++].trim().split(",").map { it.toInt() }
        val disappear = words[i++].trim().split(",").map { it.toInt() }
        val weight = words[i++].trim().toInt()
        val condition = words[i++].trim().toInt()
        val play = words[i++].trim().toInt()
        val playPara1 = words[i++].trim().split(",").map { it.toInt() }
        val playPara2 = words[i++].trim().split(",").map { it.toDouble() }
        val winReward = words[i++].trim().split(",").map { it.toInt() }
        val loseReward = words[i++].trim().split(",").map { it.toInt() }
        val dialogId = words[i++].trim().toInt()
        val dialogType = words[i++].trim().toInt()
        val showText = words[i++].trim()
        val startText = words[i++].trim()
        val winText = words[i++].trim()
        val loseText = words[i++].trim()
        val isEnd = words[i++].trim().toInt() == 1
        val endType = words[i++].trim().toInt()
        val storyDesc1 = words[i++].trim()
        val storyDesc2 = words[i++].trim()
        val storyBag = words[i++].trim().toInt()
        val eventAttribute1 = words[i++].trim().toInt()
        val eventAttribute2 = words[i++].trim().toInt()
        val eventAttribute3 = words[i++].trim().toInt()
        val eventAttribute4 = words[i++].trim().toInt()
        val eventAttribute5 = words[i++].trim().toInt()
        val eventAttribute6 = words[i++].trim().toInt()
        val eventAttribute7 = words[i++].trim().toDouble()
        val eventAttribute8 = words[i++].trim().toDouble()
        val eventAttribute9 = words[i++].trim().toDouble()
        val eventAttribute10 = words[i++].trim().toInt()
        val pic = words[i++].trim()
        val regularDialog = words[i++].trim()
        val regularNPCName = words[i++].trim()
        val regularNPCPic = words[i++].trim()
        val endTitle = words[i++].trim()
        val bgPic = words[i++].trim()
        val tag = words[i++].trim()
        val storyBagLimit = words[i++].trim().toInt()
        val isMainLine = words[i++].trim().toInt()
        val isHard = words[i++].trim().toInt()
        val isRepeat = words[i].trim().toInt()
        return Event(
            id,
            name,
            age,
            if (appear == 0) -1 else if (appear == 12) 0 else appear, // 1-12表示每年1-12月固定出现，0表示不固定
            same,
            front,
            disappear,
            weight,
            condition,
            play,
            playPara1,
            playPara2,
            winReward,
            loseReward,
            dialogId,
            dialogType,
            showText,
            startText,
            winText,
            loseText,
            isEnd,
            endType,
            storyDesc1,
            storyDesc2,
            storyBag,
            eventAttribute1,
            eventAttribute2,
            eventAttribute3,
            eventAttribute4,
            eventAttribute5,
            eventAttribute6,
            eventAttribute7,
            eventAttribute8,
            eventAttribute9,
            eventAttribute10,
            pic,
            regularDialog,
            regularNPCName,
            regularNPCPic,
            endTitle,
            bgPic,
            tag,
            storyBagLimit,
            isMainLine,
            isHard,
            isRepeat
        )
    }
}
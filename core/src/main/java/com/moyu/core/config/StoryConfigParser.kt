package com.moyu.core.config

import com.moyu.core.model.Story

class StoryConfigParser : ConfigParser<Story> {
    override fun parse(line: String): Story {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val unlockId = words[i++].trim().toInt()
        val desc = words[i++].trim()
        val pic = words[i++].trim()
        val showCard = words[i].trim().split(",").map { it.toInt() }
        return Story(
            id,
            name,
            unlockId,
            desc,
            pic,
            showCard,
        )
    }
}
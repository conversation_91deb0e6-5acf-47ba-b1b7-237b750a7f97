package com.moyu.core.config

import com.moyu.core.model.Tower

class TowerConfigParser : ConfigParser<Tower> {
    override fun parse(line: String): Tower {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val layer = words[i++].trim().toInt()
        val type = words[i++].trim().split(",").map { it.toInt() }
        val playPara1 = words[i++].trim().split(",").map { it.toInt() }
        val playPara2 = words[i++].trim().toInt()
        val eventAttribute1 = words[i++].trim().toInt()
        val eventAttribute2 = words[i++].trim().toInt()
        val eventAttribute3 = words[i++].trim().toInt()
        val eventAttribute4 = words[i++].trim().toInt()
        val eventAttribute5 = words[i++].trim().toInt()
        val eventAttribute6 = words[i++].trim().toInt()
        val eventAttribute7 = words[i++].trim().toDouble()
        val eventAttribute8 = words[i++].trim().toDouble()
        val eventAttribute9 = words[i++].trim().toDouble()
        val eventAttribute10 = words[i++].trim().toInt()
        val reward = words[i++].trim().toInt()
        return Tower(
            id,
            layer,
            type,
            playPara1,
            playPara2,
            eventAttribute1,
            eventAttribute2,
            eventAttribute3,
            eventAttribute4,
            eventAttribute5,
            eventAttribute6,
            eventAttribute7,
            eventAttribute8,
            eventAttribute9,
            eventAttribute10,
            reward,
        )
    }
}
package com.moyu.core.config

import com.moyu.core.model.Sell

class SellConfigParser : ConfigParser<Sell> {
    override fun parse(line: String): Sell {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val type = words[i++].trim().toInt()
        val regularType = words[i++].trim().toInt()
        val discount = words[i++].trim()
        val priceType = words[i++].trim().toInt()
        val price = words[i++].trim().toInt()
        val priceDollar = words[i++].trim().toDouble()
        val storage = words[i++].trim().toInt()
        val storageType = words[i++].trim().toInt()
        val itemId = words[i++].trim().toInt()
        val num = words[i++].trim().toInt()
        val pic = words[i++].trim()
        val condition = words[i++].trim().split(",").map { it.toInt() }
        val desc = words[i++].trim()
        val unlock = words[i++].trim().toInt()
        val itemType = words[i++].trim().toInt()
        val priority = words[i++].trim().toInt()
        val title = words[i++].trim()
        val canRefresh = words[i++].trim().toInt() != 1
        val showCondition = words[i++].trim().toInt()
        val googleItemId = words[i++].trim()
        val itemId2 = words[i++].trim().toInt()
        return Sell(
            id,
            name,
            type,
            regularType,
            discount,
            priceType,
            price,
            priceDollar,
            storage,
            storageType,
            itemId,
            num,
            pic,
            condition,
            desc,
            unlock,
            itemType,
            priority,
            title,
            canRefresh,
            showCondition,
            googleItemId,
            itemId2
        )
    }
}
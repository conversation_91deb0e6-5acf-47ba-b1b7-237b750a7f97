package com.moyu.core.config

import com.moyu.core.model.Race

class RaceConfigParser : ConfigParser<Race> {
    override fun parse(line: String): Race {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val roleId = words[i++].trim().toInt()
        val name = words[i++].trim()
        val raceType = words[i++].trim().toInt()
        val level = words[i++].trim().toInt()
        val star = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val attribute1 = words[i++].trim().toInt()
        val attribute2 = words[i++].trim().toInt()
        val attribute3 = words[i++].trim().toInt()
        val attribute4 = words[i++].trim().toInt()
        val attribute5 = words[i++].trim().toInt()
        val attribute6 = words[i++].trim().toInt()
        val attribute7 = words[i++].trim().toDouble()
        val attribute8 = words[i++].trim().toDouble()
        val attribute9 = words[i++].trim().toDouble()
        val attribute10 = words[i++].trim().toInt()
        val skillId = words[i++].trim().split(",").map { it.toInt() }
        val randomSkillId = words[i++].trim().split(",").map { it.toInt() }
        val randomSkillNum = words[i++].trim().split(",").map { it.toInt() }
        val pic = words[i++].trim()
        val story = words[i].trim()
        return Race(
            id,
            roleId,
            name,
            raceType,
            level,
            star,
            quality,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            attribute8,
            attribute9,
            attribute10,
            skillId,
            randomSkillId,
            randomSkillNum,
            pic,
            story,
        )
    }
}
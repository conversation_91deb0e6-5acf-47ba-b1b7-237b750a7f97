package com.moyu.core.config

import com.moyu.core.model.Skin
class SkinConfigParser : ConfigParser<Skin> {
    override fun parse(line: String): Skin {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val type = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val effectType = words[i++].trim().split(",").map { it.toInt() }
        val effectNum = words[i++].trim().split(",").map { it.toDouble() }
        val pic = words[i++].trim()
        val tips = words[i].trim()
        return Skin(
            id,
            name,
            type,
            quality,
            effectType,
            effectNum,
            pic,
            tips
        )
    }
}
package com.moyu.core.config

import com.moyu.core.model.Ally
import com.moyu.core.model.Badge
import com.moyu.core.model.BattlePass
import com.moyu.core.model.Buff
import com.moyu.core.model.DayReward
import com.moyu.core.model.Dialog
import com.moyu.core.model.Difficult
import com.moyu.core.model.DrawItem
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Gift
import com.moyu.core.model.Mission
import com.moyu.core.model.Pool
import com.moyu.core.model.Pvp
import com.moyu.core.model.Quest
import com.moyu.core.model.Race
import com.moyu.core.model.Sell
import com.moyu.core.model.Sign
import com.moyu.core.model.Skin
import com.moyu.core.model.Story
import com.moyu.core.model.Talent
import com.moyu.core.model.Title
import com.moyu.core.model.Tower
import com.moyu.core.model.TurnTable
import com.moyu.core.model.Unlock
import com.moyu.core.model.Vip
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.skill.Scroll
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.model.tcg.TcgCardType

const val BUFF_FILE_NAME = "buff.txt"
const val ALLY_FILE_NAME = "ally.txt"
const val DIFFICULT_FILE_NAME = "difficult.txt"
const val COMMON_FILE_NAME = "common.txt"
const val SELL_FILE_NAME = "sell.txt"
const val REPUTATION_LEVEL_FILE_NAME = "prestige.txt"
const val RACE_FILE_NAME = "race.txt"
const val SKILL_FILE_NAME = "skill.txt"
const val EQUIPMENT_FILE_NAME = "equipment.txt"
const val SKIN_FILE_NAME = "skin.txt"
const val POSITION_FILE_NAME = "position.txt"
const val GIFT_FILE_NAME = "gift.txt"
const val TASK_FILE_NAME = "task.txt"
const val UNLOCK_FILE_NAME = "unlock.txt"
const val DIALOG_FILE_NAME = "dialog.txt"
const val TALENT_FILE_NAME = "talent.txt"
const val TCG_CARD_FILE_NAME = "tcgname.txt"
const val TCG_AWARD_FILE_NAME = "tcgreward.txt"
const val TCG_CARD_TYPE_FILE_NAME = "tcg.txt"
const val EVENT_FILE_NAME = "event.txt"
const val SCROLL_FILE_NAME = "scroll.txt"
const val POOL_FILE_NAME = "pool.txt"
const val VIP_FILE_NAME = "vip.txt"
const val COMBINEDBUFF_FILE_NAME = "combinedbuff.txt"
const val STORY_FILE_NAME = "story.txt"
const val WAR_PASS_FILE_NAME = "battlepass.txt"
const val WAR_PASS2_FILE_NAME = "battlepass2.txt"
const val SIGN_FILE_NAME = "sign.txt"
const val BADGE_FILE_NAME = "item.txt"
const val MISSION_FILE_NAME = "mission.txt"
const val PVP_FILE_NAME = "pvp.txt"
const val DAY_REWARD_FILE_NAME = "dayreward.txt"
const val LUCKY_FILE_NAME = "lucky.txt"
const val TURNTABLE_FILE_NAME = "turntable.txt"
const val DRAW_FILE_NAME = "draw.txt"
const val TOWER_FILE_NAME = "tower.txt"



interface ConfigHolder {
    fun setGameConfig(key: String, pool: List<Any>)

    // basic
    fun getSkillPool(): List<Skill>
    fun getBuffPool(): List<Buff>
    fun getTitlePool(): List<Title>
    fun getEquipPool(): List<Equipment>
    fun getReputationLevelPool(): List<ReputationLevel>
    fun getRacePool(): List<Race>
    fun getEventPool(): List<Event>
    fun getAllyPool(): List<Ally>
    fun getDifficultPool(): List<Difficult>
    fun getDialogPool(): List<Dialog>
    fun getTalentPool(): List<Talent>
    fun getSellPool(): List<Sell>
    fun getGameTaskPool(): List<Quest>
    fun getUnlockPool(): List<Unlock>
    fun getTcgCardTypePool(): List<TcgCardType>
    fun getTcgCardPool(): List<TcgCard>
    fun getTcgAwardPool(): List<TcgAward>
    fun getScrollPool(): List<Scroll>
    fun getGiftPool(): List<Gift>
    fun getPoolPool(): List<Pool>
    fun getVipPool(): List<Vip>
    fun getCombinedBuffPool(): List<Buff>
    fun getStoryPool(): List<Story>
    fun getBattlePassPool(): List<BattlePass>
    fun getBattlePass2Pool(): List<BattlePass>
    fun getSignPool(): List<Sign>
    fun getSkinPool(): List<Skin>
    fun getBadgePool(): List<Badge>
    fun getMissionPool(): List<Mission>
    fun getPvpPool(): List<Pvp>
    fun getDayRewardPool(): List<DayReward>
    fun getTurnTablePool(): List<TurnTable>
    fun getDrawPool(): List<DrawItem>
    fun getTowerPool(): List<Tower>

    // common
    fun getConstA(): Double
    fun getConstB(): Double
    fun getInitGold(): Int
    fun getInitResource(): Int
    fun getEndingAwardLevel(age: Int): Int
    fun getEndingAwardDiamond(difficult: Int, level: Int): Int
    fun getRefreshShopCost(): Int
    fun getKeyToDiamondRate(): Int
    fun getDailyShopRefreshCount(): Int

    // extra
    fun getSkillById(skillId: Int): Skill
    fun getEquipById(equipId: Int): Equipment
    fun getGameTaskById(id: Int): Quest
    fun getAllyById(id: Int): Ally
    fun getEventById(id: Int): Event
    fun getScrollById(id: Int): Scroll
    fun getBuffById(buffId: Int): Buff
    fun getRaceById(raceId: Int): Race
    fun getTalentById(id: Int): Talent
    fun getUnlockById(id: Int): Unlock
    fun getTcgCardById(cardId: Int): TcgCard
    fun getFirstAllyIds(): List<Int>
    fun getFirstEndingAwardPoolId(): Int
    fun getPoolById(id: Int): Pool
    fun getPoolByKeyAny(key: String): List<Any>
    fun getWarPassQuestCount(): Int
    fun getNewQuestCount(): Int
    fun getDailyQuestCount(): Int
    fun getShopDataByIndex(index: Int): List<Int>
    fun getUnlockTalentPageLevel(): Int

    fun getMaxOtherUseYourCount(): Int
    fun getMaxUseOtherCount(): Int
    fun getShareCodeAwardKeyNum(): Int
    fun getMaxOneDayDiamondLimit(): Int
    fun getTextShareAwardNum(): Int
    fun getImageShareAwardNum(): Int
    fun getGreenAllyNum(difficult: Int, level: Int): Int
    fun getBlueAllyNum(difficult: Int, level: Int): Int
    fun getOrangeAllyNum(difficult: Int, level: Int): Int
    fun getGreenAllyPool(): List<Int>
    fun getBlueAllyPool(): List<Int>
    fun getOrangeAllyPool(): List<Int>
    fun getFirstChargeAllyId(): Int
    fun getFamousDiamond(): Int
    fun getGiftById(value: Int): Gift?
    fun getCheapLotteryCosts(): List<Int>
    fun getExpensiveLotteryCosts(): List<Int>
    fun getAllyCouponRate(): Int
    fun getDrawEnsurePool(): Int
    fun getTowerAwardKey(towerLevel: Int): Int
}
package com.moyu.core.config

import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEffectType

class SkillConfigParser : ConfigParser<Skill> {
    override fun parse(line: String): Skill {
        var i = 0
        val words = line.split("\t")
        val id = words[i++].trim().toInt()
        val skillType = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val level = words[i++].trim().toInt()
        val elementType = words[i++].trim().toInt()
        val name = words[i++].trim()
        val desc = words[i++].trim()
        val triggerType = words[i++].trim().toInt()
        val rate = words[i++].trim().toDouble()
        val activeCondition = words[i++].trim().split(",").map { it.toInt() }
        val activeConditionNum = words[i++].trim().split(",").map { it.toInt() }
        val conditionLogic = words[i++].trim().toInt()
        val effectType = words[i++].trim().split(",").map { SkillEffectType(it.toInt()) }
        val subType = words[i++].trim().split(",").map { it.toInt() }
        val target = words[i++].trim().split(",").map { it.toInt() }
        val effectNum = words[i++].trim().split(",").map {
            it.toDouble()
        }
        val effectReference = words[i++].trim().split(",").map { it }
        val buffContinue = words[i++].trim().toInt()
        val buffLayer = words[i++].trim().toInt()
        val priority = words[i++].trim().toInt()
        val icon = words[i++].trim()
        val special = words[i++].trim().toInt()
        val grave = words[i++].trim().toInt()
        val coolDown = words[i++].trim().toInt()
        val skillTagIds = words[i++].trim().split(",").map { it.toInt() }
        val isDispel = words[i++].trim().toInt()
        val combinedBuffId = words[i++].trim().split(",").map { it.toInt() }
        val skillEffect = words.getOrNull(i++)?.trim()?.takeIf { it != "" }?.split(",")?: emptyList()
        val skillEffectNum = words.getOrNull(i)?.trim()?.takeIf { it != "" }?.split(",")?.map { it.toInt() }?: emptyList()

//        if (effectType.size != effectNum.size || effectType.size != effectReference.size || effectType.size != subType.size || effectType.size != target.size) {
//            Log.e("", "SkillConfigParser 技能子效果配置错误：$name")
//        }
//        (1..10).toList().forEach { index->
//            if (desc.contains("%d${index}") && effectNum.size < index) {
//                Log.e("", "SkillConfigParser 技能描述d${index}错误：$name")
//            }
//        }
//        if (activeCondition.size != activeConditionNum.size) {
//            Log.e("", "SkillConfigParser 技能条件配置错误：$name")
//        }
        return Skill(
            id = id,
            skillType = skillType,
            mainId = mainId,
            level = level,
            elementType = elementType,
            name = name,
            desc = desc,
            triggerType = triggerType,
            rate = rate,
            activeCondition = activeCondition,
            activeConditionNum = activeConditionNum,
            conditionLogic = conditionLogic,
            effectType = effectType,
            subType = subType,
            target = target,
            effectNum = effectNum,
            effectReference = effectReference,
            buffContinue = buffContinue,
            buffLayer = buffLayer,
            priority = priority,
            icon = icon,
            special = special,
            grave = grave,
            coolDown = coolDown,
            skillTagIds = skillTagIds,
            isDispel = isDispel == 0,
            combinedBuffId = combinedBuffId,
            skillEffect = skillEffect,
            skillEffectNum = skillEffectNum
        )
    }
}
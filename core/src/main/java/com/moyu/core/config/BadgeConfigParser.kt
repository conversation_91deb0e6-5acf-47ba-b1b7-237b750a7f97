package com.moyu.core.config

import com.moyu.core.model.Badge

class BadgeConfigParser : ConfigParser<Badge> {
    override fun parse(line: String): Badge {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val mainId = words[i++].trim().toInt()
        val level = words[i++].trim().toInt()
        val pic = words[i++].trim()
        val desc = words[i].trim()
        return Badge(
            id,
            name,
            mainId,
            level,
            pic,
            desc
        )
    }
}
package com.moyu.core.config

import com.moyu.core.model.skill.Scroll

class ScrollConfigParser : ConfigParser<Scroll> {
    override fun parse(line: String): Scroll {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val mainId = words[i++].trim().toInt()
        val name = words[i++].trim()
        val star = words[i++].trim().toInt()
        val starLimit = words[i++].trim().toInt()
        val quality = words[i++].trim().toInt()
        val type = words[i++].trim().toInt()
        val starUpNum = words[i++].trim().toInt()
        val dropLimit = words[i++].trim().toInt()
        val story = words[i++].trim()
        val belong = words[i++].trim().toInt()
        val position = words[i++].trim().split(",").map { it.toInt() }
        val cost = words[i++].trim().toInt()
        val conditionType = words[i++].trim().toInt()
        val conditionNum = words[i++].trim().toInt()
        val skillTreeId = words[i++].trim().toInt()
        val mainName = words[i].trim()
        return Scroll(
            id,
            mainId,
            name,
            star,
            starLimit,
            quality,
            type,
            starUpNum,
            dropLimit,
            story,
            belong,
            position,
            cost,
            conditionType,
            conditionNum,
            skillTreeId,
            mainName
        )
    }
}
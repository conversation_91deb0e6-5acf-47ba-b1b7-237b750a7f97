package com.moyu.core.config

import com.moyu.core.model.level.ReputationLevel

class ReputationConfigParser : ConfigParser<ReputationLevel> {
    override fun parse(line: String): ReputationLevel {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val level = words[i++].trim().toInt()
        val exp = words[i++].trim().toInt()
        val expTotal = words[i++].trim().toInt()
        val reward1 = words[i++].trim().toInt()
        val reward2 = words[i++].trim().toInt()
        val reward3 = words[i++].trim().toInt()
        val reward4 = words[i++].trim().toInt()
        val reward5 = words[i++].trim().toInt()
        val reward6 = words[i++].trim().toInt()
        val reward7 = words[i++].trim().toInt()
        val reward8 = words[i].trim().toInt()
        return ReputationLevel(
            id,
            name,
            level,
            exp,
            expTotal,
            reward1,
            reward2,
            reward3,
            reward4,
            reward5,
            reward6,
            reward7,
            reward8,
        )
    }
}
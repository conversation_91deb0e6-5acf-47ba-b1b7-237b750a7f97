package com.moyu.core.model.skill

/**
"1=角色专属战斗技能
2=战斗技能树技能
3=兵种技能
4=战场技能
5=装备技能
6=锦囊冒险技能
7=兵符技能树技能
8=天赋技能
9=附魔技能
10=政令技能
11=阵法技能
12=阵营技能"
 */
fun Skill.isAllySpecial() = skillType == 1
fun Skill.isBattleTree() = skillType == 2
fun Skill.isTroopSkill() = skillType == 3
fun Skill.isHalo() = skillType == 4
fun Skill.isEquip() = skillType == 5
fun Skill.isJinNang() = skillType == 6
fun Skill.isBinFuTree() = skillType == 7
fun Skill.isTalent() = skillType == 8
fun Skill.isEnchant() = skillType == 9
fun Skill.isZhenLing() = skillType == 10
fun Skill.isZhenFa() = skillType == 11
fun Skill.isZhenYing() = skillType == 12

fun Skill.isAdventure() = isZhenLing() || isJinNang() || isBinFuTree()

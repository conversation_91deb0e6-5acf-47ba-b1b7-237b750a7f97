package com.moyu.core.model

import com.moyu.core.GameCore
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Quest(
    @SerialName("a")
    override val id: Int = 0,
    @Transient
    val taskType: Int = 1,
    @Transient
    val name: String = "",
    @Transient
    val desc: String = "",
    @Transient
    val type: Int = 0,
    @Transient
    val subType: List<Int> = emptyList(),
    @Transient
    val num: Int = 0,
    @Transient
    val reward: Int = 0,
    @Transient
    val order: Int = 0,
    @Transient
    val talent: List<Int> = emptyList(),
    @Transient
    val pic: String = "",
    @Transient
    val dialogId: Int = 0,
    @SerialName("i")
    val done: Boolean = false,
    @SerialName("z")
    val opened: Boolean = false,
    @SerialName("xx")
    val needRemoveCount: Int = 0,
) : ConfigData {
    fun isNewTask(): Boolean {
        return taskType == 4
    }

    fun isDailyTask(): Boolean {
        return taskType == 1
    }

    fun isPvpTask(): Boolean {
        return taskType == 5
    }

    fun isOneTimeTask(): Boolean {
        return taskType == 2
    }

    fun isWarPassTask(): Boolean {
        return taskType == 3
    }

    fun isWarPass2Task(): Boolean {
        return taskType == 7
    }

    /**
     * 如果是最大数值的记录任务，则不用减去当前记录
     * 比如，如果是持有5个盟友，那么不用减去当前记录
     * 但是如果是杀敌xx个，需要减去之前记录
     * 再细化下说：
     * 如果是已经杀了100个敌人，刷到了类似任务，要死是杀50个，那么需要减去100，否则一进来就完成了
     * 但是如果是持有5个盟友，那么不需要减去之前的记录，没法做减法
     */
    fun isMaxRecordQuest(): Boolean {
        return (type == 2) // 最大关卡
                || type == 10 // 拥有盟友
                || type == 11 // 拥有星级盟友
                || type == 12 // 结局
                || type == 13 // 名望
                || type == 17 // 城池等级
    }

    fun isEndingTask(): Boolean {
        return type == 12
    }

    fun storyId(): Int {
        if (type == 12) {
            // todo 三国其他语言任务闪退修复了，因为之前简单处理，直接通过任务名字来匹配对应哪个故事包的结局任务，改为根据subType来区分
            val stories = GameCore.instance.getStoryPool()
            return when (subType.first() / 10000) {
                12 -> stories[2].id
                11 -> stories[1].id
                10 -> stories[0].id
                else -> return 0
            }
        }
        return 0
    }

    fun isForever(): Boolean {
        return taskType == 2 || taskType == 4 || taskType == 7 || isDrawTask()
    }

    fun isCollectTask(): Boolean {
        return taskType == 8
    }

    fun isCostTask(): Boolean {
        return taskType == 9
    }

    fun isChargeTask(): Boolean {
        return taskType == 10
    }

    fun isDrawTask(): Boolean {
        return taskType == 11
    }
}
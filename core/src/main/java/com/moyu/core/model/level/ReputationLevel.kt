package com.moyu.core.model.level

import com.moyu.core.GameCore
import com.moyu.core.model.ConfigData

data class ReputationLevel(
    override val id: Int,
    val name: String = "",
    val level: Int = 0,
    val exp: Int = 0,
    val expTotal: Int = 0,
    val reward1: Int = 0,
    val reward2: Int = 0,
    val reward3: Int = 0,
    val reward4: Int = 0,
    val reward5: Int = 0,
    val reward6: Int = 0,
    val reward7: Int = 0,
    val reward8: Int = 0,
): ConfigData {
    fun storeId(typeIndex: Int): Int {
        return id * 100 + typeIndex
    }

    fun awardId(typeIndex: Int): Int {
        return when (typeIndex + 1) {
            1 -> reward1
            2 -> reward2
            3 -> reward3
            4 -> reward4
            5 -> reward5
            6 -> reward6
            7 -> reward7
            8 -> reward8
            else -> 0
        }
    }

    companion object {
        fun levelToValue(exp: Int): Int {
            return GameCore.instance.getReputationLevelPool().first {
                it.level >= exp
            }.expTotal
        }
    }
}
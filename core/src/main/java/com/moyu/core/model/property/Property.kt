package com.moyu.core.model.property

import com.moyu.core.AppWrapper
import com.moyu.core.R
import com.moyu.core.logic.buff.DAMAGE_PIERCE_BASE
import com.moyu.core.logic.buff.DOUBLE_SKILL
import com.moyu.core.model.damage.DamageType
import com.moyu.core.util.perMinusD
import com.moyu.core.util.perMinusI
import com.moyu.core.util.perMultiD
import com.moyu.core.util.perPlusD
import com.moyu.core.util.perPlusI
import kotlinx.serialization.Serializable
import kotlin.math.roundToInt


val emptyDoubleList = listOf(0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0)
val emptyIntList = listOf(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
val emptyControlList = listOf(0, 0, 0, 0, 0, 0)

val EMPTY_PROPERTY = Property()

@Serializable
data class Property(
    val speed: Int = 0,
    val hp: Int = 0,
    val attack: Int = 0,
    val fatalRate: Double = 0.0,
    val fatalDamage: Double = 0.0,
    val dodgeRate: Double = 0.0,
    val suckBloodRate: Double = 0.0,
    val activeSkillTimes: Int = 0,
    val activeSkillRate: Double = 0.0,
    val triggerSkillRate: Double = 0.0,
    val normalAttackTimes: Int = 0,
    val normalAttackDamage: Double = 0.0,
    val defenses: List<Int> = emptyIntList,
    val pierces: List<Double> = emptyDoubleList,
    val damageIncs: List<Double> = emptyDoubleList,
    val allDamage: Double = 0.0,
    val reduceDamageIncs: List<Double> = emptyDoubleList,
    val allReduceDamage: Double = 0.0,
    val raceDamageIncs: List<Double> = emptyDoubleList,
    val raceReduceDamageInc: List<Double> = emptyDoubleList,
    val controlEffects: List<Int> = emptyControlList,
    val buffEffect: Int = 0,
    val debuffEffect: Int = 0,
    val coolDownEffect: Int = 0,
    val healEffect: Double = 0.0,
    val shieldEffect: Double = 0.0, // 立场增减在这里仅限展示用，不参与实际计算
) {
    companion object {
        fun getDiffPropertyByBuffId(buffId: Int, diffDouble: Double): Property {
            val diff = diffDouble.roundToInt()
            return when (buffId) {
                // 属性增减
                in 2001..2015 -> propByIndex(buffId - 2000, diffDouble)
                in 2101..2115 -> propByIndex(buffId - 2100, -diffDouble)
                // 类型伤害增减
                3000 -> Property(allDamage = diffDouble)
                in 3001..3010 -> typeDamageByIndex(buffId - 3001, diffDouble)
                3100 -> Property(allDamage = -diffDouble)
                in 3101..3110 -> typeDamageByIndex(buffId - 3101, -diffDouble)
                3200 -> Property(allReduceDamage = diffDouble)
                in 3201..3210 -> typeReduceDamageByIndex(buffId - 3201, diffDouble)
                3300 -> Property(allReduceDamage = -diffDouble)
                in 3301..3310 -> typeReduceDamageByIndex(buffId - 3301, -diffDouble)
                //种族伤害增减
                in 3401..3410 -> raceDamageByIndex(buffId - 3401, diffDouble)
                in 3501..3510 -> raceDamageByIndex(buffId - 3501, -diffDouble)
                in 3601..3610 -> raceReduceDamageByIndex(buffId - 3601, diffDouble)
                in 3701..3710 -> raceReduceDamageByIndex(buffId - 3701, -diffDouble)
                // 控制
                in 4301..4305 -> controlEffectByIndex(buffId - 4301, diff)
                in 4311..4315 -> controlEffectByIndex(buffId - 4311, -diff)
                // 穿透
                in DAMAGE_PIERCE_BASE..DAMAGE_PIERCE_BASE + 10 -> damagePierce(
                    buffId - DAMAGE_PIERCE_BASE,
                    diffDouble
                )
                // 治疗效果
                5401 -> Property(healEffect = diffDouble)
                5402 -> Property(healEffect = -diffDouble)
                // 力场效果
                5403 -> Property(shieldEffect = diffDouble)
                5404 -> Property(shieldEffect = -diffDouble)
                // 主动技能概率
                5405 -> Property(activeSkillRate = diffDouble)
                5406 -> Property(activeSkillRate = -diffDouble)
                // 触发技能概率
                5407 -> Property(triggerSkillRate = diffDouble)
                5408 -> Property(triggerSkillRate = -diffDouble)
                // buff/debuff
                5409 -> Property(buffEffect = diff)
                5410 -> Property(buffEffect = -diff)
                5411 -> Property(debuffEffect = diff)
                5412 -> Property(debuffEffect = -diff)
                // cd
                5413 -> Property(coolDownEffect = diff)
                5414 -> Property(coolDownEffect = -diff)
                DOUBLE_SKILL -> Property(activeSkillTimes = diff)
                5601 -> Property(normalAttackTimes = diff)
                5602 -> Property(normalAttackTimes = -diff)
                5603 -> Property(normalAttackDamage = diffDouble)
                5604 -> Property(normalAttackDamage = -diffDouble)
                else -> EMPTY_PROPERTY
            }
        }

        fun damagePierce(toInt: Int, diff: Double): Property {
            return Property(pierces = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        private fun controlEffectByIndex(toInt: Int, diff: Int): Property {
            return Property(controlEffects = emptyControlList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun typeDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(damageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun typeReduceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(reduceDamageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun raceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(raceDamageIncs = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun raceReduceDamageByIndex(toInt: Int, diff: Double): Property {
            return Property(raceReduceDamageInc = emptyDoubleList.toMutableList().apply {
                set(toInt, diff)
            }.toList())
        }

        fun propByIndex(toInt: Int, diff: Double): Property {
            return when (toInt) {
                1 -> {
                    // 攻击
                    Property(attack = diff.roundToInt())
                }

                in 2..5 -> {
                    // 防御
                    val index = toInt - 2
                    Property(defenses = emptyIntList.toMutableList().apply {
                        set(index, diff.roundToInt())
                    })
                }

                6 -> {
                    // 生命上限
                    Property(hp = diff.roundToInt())
                }

                7 -> {
                    // 暴击率
                    Property(fatalRate = diff)
                }

                8 -> {
                    // 暴击伤害
                    Property(fatalDamage = diff)
                }

                9 -> {
                    // 格挡率
                    Property(dodgeRate = diff)
                }

                10 -> {
                    // 速度
                    Property(speed = diff.roundToInt())
                }

                else -> {
//                    error("永久增益，无效effectReference，${toInt}")
                    Property()
                }
            }
        }

        fun getPropertyByEnum(type: Int, value: Double): Property {
            return when (type) {
                1 -> Property(attack = value.roundToInt())
                in 2..5 -> Property(defenses = emptyIntList.toMutableList().apply {
                    set(type - 2, value.roundToInt())
                })
                6 -> Property(hp = value.roundToInt())
                7 -> Property(fatalRate = value)
                8 -> Property(fatalDamage = value)
                9 -> Property(dodgeRate = value)
                else -> Property(speed = value.roundToInt())
            }
        }
    }

    operator fun plus(diffProperty: Property): Property {
        return copy(
            speed = speed + diffProperty.speed,
            hp = hp + diffProperty.hp,
            attack = attack + diffProperty.attack,
            fatalRate = fatalRate + diffProperty.fatalRate,
            fatalDamage = fatalDamage + diffProperty.fatalDamage,
            dodgeRate = dodgeRate + diffProperty.dodgeRate,
            suckBloodRate = suckBloodRate + diffProperty.suckBloodRate,
            activeSkillTimes = activeSkillTimes + diffProperty.activeSkillTimes,
            activeSkillRate = activeSkillRate + diffProperty.activeSkillRate,
            triggerSkillRate = triggerSkillRate + diffProperty.triggerSkillRate,

            defenses = defenses.perPlusI(diffProperty.defenses),
            pierces = pierces.perPlusD(diffProperty.pierces),
            damageIncs = damageIncs.perPlusD(diffProperty.damageIncs),
            allDamage = allDamage + diffProperty.allDamage,
            reduceDamageIncs = reduceDamageIncs.perPlusD(diffProperty.reduceDamageIncs),
            allReduceDamage = allReduceDamage + diffProperty.allReduceDamage,

            raceDamageIncs = raceDamageIncs.perPlusD(diffProperty.raceDamageIncs),
            raceReduceDamageInc = raceReduceDamageInc.perPlusD(diffProperty.raceReduceDamageInc),

            controlEffects = controlEffects.perPlusI(diffProperty.controlEffects),

            healEffect = healEffect + diffProperty.healEffect,
            shieldEffect = shieldEffect + diffProperty.shieldEffect,
            buffEffect = buffEffect + diffProperty.buffEffect,
            debuffEffect = debuffEffect + diffProperty.debuffEffect,
            coolDownEffect = coolDownEffect + diffProperty.coolDownEffect,
            normalAttackDamage = normalAttackDamage + diffProperty.normalAttackDamage,
            normalAttackTimes = normalAttackTimes + diffProperty.normalAttackTimes,
        )
    }

    operator fun minus(diffProperty: Property): Property {
        return copy(
            speed = speed - diffProperty.speed,
            hp = hp - diffProperty.hp,
            attack = attack - diffProperty.attack,
            fatalRate = fatalRate - diffProperty.fatalRate,
            fatalDamage = fatalDamage - diffProperty.fatalDamage,
            dodgeRate = dodgeRate - diffProperty.dodgeRate,
            suckBloodRate = suckBloodRate - diffProperty.suckBloodRate,
            activeSkillTimes = activeSkillTimes - diffProperty.activeSkillTimes,
            activeSkillRate = activeSkillRate - diffProperty.activeSkillRate,
            triggerSkillRate = triggerSkillRate - diffProperty.triggerSkillRate,

            defenses = defenses.perMinusI(diffProperty.defenses),
            pierces = pierces.perMinusD(diffProperty.pierces),
            damageIncs = damageIncs.perMinusD(diffProperty.damageIncs),
            allDamage = allDamage - diffProperty.allDamage,
            reduceDamageIncs = reduceDamageIncs.perMinusD(diffProperty.reduceDamageIncs),
            allReduceDamage = allReduceDamage - diffProperty.allReduceDamage,

            raceDamageIncs = raceDamageIncs.perMinusD(diffProperty.raceDamageIncs),
            raceReduceDamageInc = raceReduceDamageInc.perMinusD(diffProperty.raceReduceDamageInc),

            controlEffects = controlEffects.perMinusI(diffProperty.controlEffects),

            healEffect = healEffect - diffProperty.healEffect,
            shieldEffect = shieldEffect - diffProperty.shieldEffect,
            buffEffect = buffEffect - diffProperty.buffEffect,
            debuffEffect = debuffEffect - diffProperty.debuffEffect,
            coolDownEffect = coolDownEffect - diffProperty.coolDownEffect,
            normalAttackDamage = normalAttackDamage - diffProperty.normalAttackDamage,
            normalAttackTimes = normalAttackTimes - diffProperty.normalAttackTimes,

        )
    }

    fun getRealFatalRate(): Double {
        return fatalRate / 100
    }

    fun getRealFatalDamage(): Double {
        return fatalDamage / 100
    }

    fun getRealDodgeRate(): Double {
        return dodgeRate / 100
    }

    fun getRealSuckBloodRate(): Double {
        return suckBloodRate / 100
    }

    fun getDefenseByType(damageType: DamageType): Int {
        return defenses.getOrElse(damageType.value - 1) { 0 }
    }

    fun getPierceByType(damageType: DamageType): Double {
        return pierces[damageType.value - 1]
    }

    fun getDamageIncreaseByRaceType(raceType: Int): Double {
        return raceDamageIncs[raceType - 1] / 100
    }

    fun getDamageIncreaseByType(damageType: DamageType): Double {
        return damageIncs[damageType.value - 1] / 100
    }

    fun getNormalAttackRate(): Double {
        return normalAttackDamage / 100
    }

    fun getReduceDamageIncreaseByRaceType(raceType: Int): Double {
        return raceReduceDamageInc[raceType - 1] / 100
    }

    fun getReduceDamageIncreaseByType(damageType: DamageType): Double {
        return reduceDamageIncs[damageType.value - 1] / 100
    }

    operator fun times(times: Double): Property {
        return this.copy(
            speed = (speed * times).roundToInt(),
            hp = (hp * times).roundToInt(),
            attack = (attack * times).roundToInt(),
            defenses = defenses.perMultiD(times),
            suckBloodRate = suckBloodRate * times,
            dodgeRate = dodgeRate * times,
            fatalDamage = fatalDamage * times,
            fatalRate = fatalRate * times,
        )
    }

    fun isNotEmpty(): Boolean {
        return this != EMPTY_PROPERTY
    }

    fun isEmpty(): Boolean {
        return this == EMPTY_PROPERTY
    }

    fun getPropertyByTarget(propIndex: Int): Double {
        return when (propIndex) {
            1 -> {
                // 攻击
                attack.toDouble()
            }

            in 2..5 -> {
                // 防御
                defenses[propIndex - 2].toDouble()
            }

            6 -> {
                // 生命上限
                hp.toDouble()
            }

            7 -> {
                // 暴击率
                fatalRate
            }

            8 -> {
                // 暴击伤害
                fatalDamage
            }

            9 -> {
                // 闪避率
                dodgeRate
            }

            10 -> {
                // 速度
                speed.toDouble()
            }

            else -> {
                error("无效effectReference")
            }
        }
    }

    fun getAllDamageRate(): Double {
        return allDamage / 100
    }

    fun getAllReduceDamageRate(): Double {
        return allReduceDamage / 100
    }

    fun ensureNotNegative(): Property {
        return Property(
            speed = speed.coerceAtLeast(0),
            hp = hp.coerceAtLeast(0),
            attack = attack.coerceAtLeast(0),
            fatalRate = fatalRate.coerceAtLeast(0.0),
            fatalDamage = fatalDamage.coerceAtLeast(0.0),
            dodgeRate = dodgeRate.coerceAtLeast(0.0),
            suckBloodRate = suckBloodRate.coerceAtLeast(0.0),
            defenses = defenses.map { it.coerceAtLeast(0) },
        )
    }

    fun getNonZeroString(): String {
        val result = StringBuilder()

        if (attack != 0) {
            result.append("${AppWrapper.getString(R.string.attack)}$attack")
        }
        if (hp != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.hp)}$hp")
        }
        if (speed != 0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.speed)}$speed")
        }
        if (fatalRate != 0.0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.fatal_rate)}${fatalRate.toInt()}%")
        }
        if (fatalDamage != 0.0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.fatal_damage)}${fatalDamage.toInt()}%")
        }
        if (dodgeRate != 0.0) {
            if (result.isNotEmpty()) result.append(", ")
            result.append("${AppWrapper.getString(R.string.dodge)}${dodgeRate.toInt()}%")
        }
        for (i in defenses.indices) {
            if (defenses[i] != 0) {
                if (result.isNotEmpty()) result.append(", ")
                val defenseName = when(i) {
                    0 -> DamageType.DamageType1.getDefenseName()
                    1 -> DamageType.DamageType2.getDefenseName()
                    2 -> DamageType.DamageType3.getDefenseName()
                    3 -> DamageType.DamageType4.getDefenseName()
                    else -> ""
                }
                result.append("$defenseName${defenses[i]}")
            }
        }
        return if (result.isEmpty()) "" else result.toString()
    }
}
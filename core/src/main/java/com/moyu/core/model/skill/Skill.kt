package com.moyu.core.model.skill

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.battle.skillChainHandler
import com.moyu.core.logic.buff.AVOID_DEATH
import com.moyu.core.logic.buff.BuffFactory
import com.moyu.core.logic.buff.HOLY_SHIELD
import com.moyu.core.logic.buff.isShield
import com.moyu.core.logic.damage.consumer.DamageConsumerFactory
import com.moyu.core.logic.damage.processor.DamageProcessorFactory
import com.moyu.core.logic.damage.processor.subTypeToDamageType
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.identifier.NoneRoleIdentifier
import com.moyu.core.logic.identifier.RoleIdentifier
import com.moyu.core.logic.info.addBuffInfo
import com.moyu.core.logic.info.addSkillCastInfo
import com.moyu.core.logic.skill.CastDamage
import com.moyu.core.logic.skill.CastSkill
import com.moyu.core.logic.skill.Dodge
import com.moyu.core.logic.skill.GetBuff
import com.moyu.core.logic.skill.GetDebuff
import com.moyu.core.logic.skill.Healing
import com.moyu.core.logic.skill.ReAlive
import com.moyu.core.logic.skill.Summon
import com.moyu.core.logic.skill.TriggerType
import com.moyu.core.logic.skill.Wounded
import com.moyu.core.logic.skill.clearRoleBuff
import com.moyu.core.logic.skill.clearRoleDebuff
import com.moyu.core.logic.skill.getReference
import com.moyu.core.logic.skill.getTargets
import com.moyu.core.logic.skill.isDot
import com.moyu.core.logic.skill.special
import com.moyu.core.model.Buff
import com.moyu.core.model.ConfigData
import com.moyu.core.model.GameItem
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageStatus
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.environment.Environment
import com.moyu.core.model.heal.HealResult
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID
import kotlin.math.roundToInt

@Serializable
data class Skill(
    @SerialName("a") override val id: Int = 0,
    @Transient val skillType: Int = 0,
    @Transient val mainId: Int = 0,
    @Transient val level: Int = 1,
    @Transient val elementType: Int = 0,
    @Transient val name: String = "",
    @Transient val desc: String = "",
    @Transient val triggerType: Int = 0,
    @Transient val rate: Double = 0.0,
    @Transient val activeCondition: List<Int> = emptyList(),
    @Transient val activeConditionNum: List<Int> = emptyList(),
    @Transient val conditionLogic: Int = 0,
    @Transient val effectType: List<SkillEffectType> = emptyList(),
    @Transient val subType: List<Int> = emptyList(),
    @Transient val target: List<Int> = emptyList(),
    @Transient val effectNum: List<Double> = emptyList(),
    @Transient val effectReference: List<String> = emptyList(),
    @Transient val buffContinue: Int = 0,
    @Transient val buffLayer: Int = 0,
    @Transient val priority: Int = 0,
    @Transient val icon: String = "",
    @Transient val special: Int = 0,
    @Transient val grave: Int = 0,
    @Transient val coolDown: Int = 0,
    @Transient val skillTagIds: List<Int> = listOf(),
    @Transient val isDispel: Boolean = false,
    @Transient val combinedBuffId: List<Int> = emptyList(),
    @Transient val skillEffect: List<String> = emptyList(),
    @Transient val skillEffectNum: List<Int> = emptyList(),
    @Transient val enhancementOneGame: List<SkillEnhancement> = emptyList(), // 技能附魔
    @SerialName("t2") val graveCount: Int = 0,
    @SerialName("t1") val currentCoolDown: Int = 100,
    @Transient val damageResult: DamageResult? = null,
    @Transient val healResult: HealResult? = null,
    @Transient val buffInfo: Buff? = null,
    @Transient val ownerIdentifier: RoleIdentifier = NoneRoleIdentifier,
    @Transient override val new: Boolean = true,
    @SerialName("b") val equipAllyUuid: String = "",
    @SerialName("d") val num: Int = 1,
    @SerialName("h") val selected: Boolean = false,
    @SerialName("pv") val effected: Boolean = true, // 用来标记史诗人物是否已生效
    @SerialName("l") val extraInfo: String = "", // 用来标记特殊文案，比如协助战斗，保护对象
    @SerialName("lo") val life: Int = 0, // 用来标记史诗人物的寿命是否已到
    val gainAge: Int = 0, // 获得该技能时间，用来做占卜卡
    // 商店信息
    @Transient val peek: Boolean = false,
    @SerialName("x") override val uuid: String = ""
) : GameItem, ConfigData {
    private fun getNum(
        index: Int, result: BattleField, skillOwner: Role, triggerSkill: Skill?, peer: Role?
    ): Double {
        val reference = getReference(
            effectReference[index], result, skillOwner, triggerSkill = triggerSkill, target = peer
        )
        return (effectNum[index] * reference / 100)
    }

    suspend fun doDamage(
        skillOwner: Role,
        targets: List<Role>,
        result: BattleField,
        damage: Int,
        damageType: DamageType,
        damageStatus: DamageStatus = DamageStatus(),
        index: Int
    ): MutableList<DamageResult> {
        val results = mutableListOf<DamageResult>()
        // 这个强化如何整合到RoleComposer去;也要处理Dot得case
        val damageChanged = (if (isDot()) buffInfo!!.skill!! else this).getAllEnhancements()
            .lastOrNull { it.isDamageTypeChange() }
        val finalDamageType =
            damageChanged?.damageType() ?: skillOwner.buffCarrier.getDamageTypeChange()
            ?: damageType
//        skillOwner.setDoAttackState(this, DamageResult(type = finalDamageType, damageSkill = this, attacker = skillOwner, victim = NoneRoleIdentifier))
//        result.doUpdateSequence()
        // 群攻出现，不能直接一个一个执行，需要全部执行完，同时动画，再一个一个回调技能链
        targets.forEach { target ->
            val damageProcessor = DamageProcessorFactory.getDamageProcessor(
                finalDamageType, skillOwner, target, this, damage, damageStatus
            )
            val damageConsumer = DamageConsumerFactory.getDamageConsumer(finalDamageType)
            val damageResult = damageProcessor.process()
            // 结算伤害
            results.add(
                damageConsumer.consumeDamage(
                    result, damageResult, skillOwner, target, index
                )
            )
        }
        result.doUpdateSequence()
        results.toList().forEach {
            if (it.damageValue.finalDamage > 0) {
                skillChainHandler.invoke(
                    CastDamage.copy(
                        ownerIdentifier = it.attacker, damageResult = it
                    ), result
                )
                skillChainHandler.invoke(
                    Wounded.copy(
                        ownerIdentifier = it.victim, damageResult = it
                    ), result
                )
            } else {
                if (it.damageStatus.isDodge) {
                    skillChainHandler.invoke(
                        Dodge.copy(
                            ownerIdentifier = it.victim, damageResult = it
                        ), result
                    )
                }
            }
        }
        return results
    }

    suspend fun doHeal(
        skillOwner: Role, targets: List<Role>, result: BattleField, heal: Int, index: Int
    ) {
        // 群攻出现，不能直接一个一个执行，需要全部执行完，同时动画，再一个一个回调技能链
        val healResults = mutableListOf<HealResult>()
        targets.forEach { target ->
            healResults.add(result.healTarget(skillOwner, target, heal, this, index = index))
        }
        result.doUpdateSequence()
        healResults.forEach {
            skillChainHandler.invoke(
                Healing.copy(ownerIdentifier = it.victim, healResult = it), result
            )
        }
    }

    suspend fun doEffect(
        battleField: BattleField, skillOwner: Role, triggerSkill: Skill? = null
    ): Skill {
        // 记录一次技能释放
        battleField.saveCastSkills(skillOwner, this)
        GameCore.instance.onCastSkill(this)
        val range = special()
        for (index in range) {
            if (isNormalAttackType() && index == 0) {
                val targets = getTargets(target[index], battleField, skillOwner, triggerSkill)
                skillOwner.setDoAttackState(this, targets.map { it.playerId() }, index)
                battleField.doUpdateSequence()
            } else if (index == 0 || special != 0) {
                skillEffectNum.getOrNull(index)?.let {
                    skillOwner.doSkillState(this, effectType[index], index)
                    if (!mergeActions()) {
                        // 如果不merge，则每个自效果动画一次，否则整体动画一次
                        battleField.doUpdateSequence()
                    }
                }
            }
            when {
                effectType[index] == directDamage || (effectType[index] == directHeal && skillOwner.isPoisonHeal()) -> {
                    refreshSkill(skillOwner).doDirectDamage(
                        index, battleField, skillOwner, triggerSkill
                    )
                }

                effectType[index] == directHeal && !skillOwner.isPoisonHeal() -> {
                    refreshSkill(skillOwner).doDirectHeal(
                        index, battleField, skillOwner, triggerSkill
                    )
                }

                effectType[index] == normalBuff -> {
                    refreshSkill(skillOwner).doBuff(index, battleField, skillOwner, triggerSkill)
                }

                effectType[index] == permanentBuff -> {
                    refreshSkill(skillOwner).doPermanentBuff(
                        index, battleField, skillOwner, triggerSkill
                    )
                }

                effectType[index] == specialEffect -> {
                    refreshSkill(skillOwner).doSpecialEffect(
                        index, battleField, skillOwner, triggerSkill
                    )
                }

                effectType[index] == genEffect -> {
                    refreshSkill(skillOwner).doGenEffect(index, battleField, skillOwner)
                }

                effectType[index] == enhancementSkill -> {
                    refreshSkill(skillOwner).doEnhancement(
                        index, battleField, skillOwner, triggerSkill
                    )
                }

                else -> {
                }
            }
        }
        skillChainHandler.invoke(this.copy(id = CastSkill.id), battleField)
        return refreshSkill(skillOwner)
    }

    fun refreshSkill(skillOwner: Role): Skill {
        // 技能在一个效果后，可能被强化，需要刷新
        return skillOwner.getSkills().find { it.id == this.id } ?: this
    }

    private fun mergeActions(): Boolean {
        return this.triggerType == TriggerType.PASSIVE.value
    }

    private suspend fun doDirectDamage(
        index: Int, result: BattleField, skillOwner: Role, triggerSkill: Skill?
    ) {
        val targets = getTargets(target[index], result, skillOwner, triggerSkill)
        val damage = getNum(index, result, skillOwner, triggerSkill, peer = targets.firstOrNull())
        doDamage(
            skillOwner,
            targets,
            result,
            damage.roundToInt(),
            subType[index].subTypeToDamageType(),
            index = index
        )
    }

    private suspend fun doDirectHeal(
        index: Int, result: BattleField, skillOwner: Role, triggerSkill: Skill?
    ) {
        if (subType[index] == 1) {
            // 直接治疗 固定取effectNum
            val targets = getTargets(target[index], result, skillOwner, triggerSkill)
            val heal = getNum(index, result, skillOwner, triggerSkill, peer = targets.firstOrNull())
            doHeal(skillOwner, targets, result, heal.roundToInt(), index)
        } else error("不支持的治疗subtype${subType[index]}")
    }

    private suspend fun doBuff(
        index: Int, battleField: BattleField, skillOwner: Role, triggerSkill: Skill?
    ) {
        val targets = getTargets(target[index], battleField, skillOwner, triggerSkill)
        val buff = BuffFactory.create(
            this, skillOwner, index, battleField, triggerSkill, targets.firstOrNull()
        )
        addBuff(buff, target[index], battleField, skillOwner, triggerSkill, index) { innerTarget ->
            // 添加buff，要trigger一个事件
            val triggerBuff = if (combinedBuffId.getOrElse(index) { 0 } != 0) {
                BuffFactory.getCombinedBuff(combinedBuffId[index])
            } else buff
            val lastCombinedId = combinedBuffId.getOrElse(index - 1) { 0 }
            val currentCombinedId = combinedBuffId.getOrElse(index) { 0 }
            if (currentCombinedId == 0 || lastCombinedId != currentCombinedId) {
                // combinedBuff 的实现是：
                // 配置3个子效果buff，但是他们的combinedBuffId是一样的，只需要trigger一次事件，所以需要判定
                if (triggerType != 1) { // 1为开场技能，不触发
                    if (triggerBuff.buffType == 1) {
                        // buff
                        skillChainHandler.invoke(
                            GetBuff.copy(ownerIdentifier = innerTarget, buffInfo = triggerBuff),
                            battleField
                        )
                    } else {
                        // debuff
                        skillChainHandler.invoke(
                            GetDebuff.copy(ownerIdentifier = innerTarget, buffInfo = triggerBuff),
                            battleField
                        )
                    }
                }
            }
        }
    }

    private suspend fun addBuff(
        buff: Buff,
        target: Int,
        result: BattleField,
        skillOwner: Role,
        triggerSkill: Skill?,
        index: Int,
        triggerEvent: suspend (Role) -> Unit
    ) {
        val targets = getTargets(target, result, skillOwner, triggerSkill)
        targets.toList().forEach { innerTarget ->
            innerTarget.setBeingBuffState(buff, this, index)
            GameCore.instance.addBuffInfo(skillOwner, this, innerTarget, buff)
            innerTarget.addBuff(
                result, innerTarget, buff.copy(addOnWhosTurn = result.getAttacker())
            )
            if (buff.isShield() || buff.id == HOLY_SHIELD) {
                GameCore.instance.onBattleEffect(SoundEffect.Shield)
            }
            triggerEvent(innerTarget)
        }
    }

    private fun doPermanentBuff(
        index: Int, battleField: BattleField, owner: Role, triggerSkill: Skill?
    ) {
        val targets = getTargets(target[index], battleField, owner, triggerSkill)
        targets.forEach { target ->
            val diff = effectNum[index].let {
                // 永久增益，如果subtype==2，则是减益
                if (subType[index] == 2) {
                    GameCore.instance.addSkillCastInfo(
                        target,
                        target.getSideName() + AppWrapper.getString(R.string.gain_permanent_debuff),
                        this
                    )
                    -it
                } else {
                    GameCore.instance.addSkillCastInfo(
                        target,
                        target.getSideName() + AppWrapper.getString(R.string.gain_permanent_buff),
                        this
                    )
                    it
                }
            } // 永久增益都是绝对值
            val diffProperty = Property.propByIndex(effectReference[index].toInt(), diff)
            target.changeAllProperty(diffProperty)
            GameCore.instance.onPermanentDiff(target, diffProperty)
        }
    }

    private suspend fun doSpecialEffect(
        index: Int, result: BattleField, skillOwner: Role, triggerSkill: Skill?
    ) {
        when (subType[index]) {
            1 -> {
                //驱散debuff
                val targets = getTargets(target[index], result, skillOwner, triggerSkill)
                targets.forEach { role ->
                    val clearBuffList = if (effectNum[index].toInt() == 0) {
                        role.getDispelableBadBuff()
                    } else {
                        role.getDispelableBadBuff().shuffled(RANDOM).take(effectNum[index].toInt())
                    }
                    clearRoleDebuff(role, skillOwner, result, this, clearBuffList, index)
                }
            }

            2 -> {
                //驱散buff
                val targets = getTargets(target[index], result, skillOwner)
                targets.forEach { role ->
                    val clearBuffList = if (effectNum[index].toInt() == 0) {
                        role.getDispelableGoodBuff()
                    } else {
                        role.getDispelableGoodBuff().shuffled(RANDOM).take(effectNum[index].toInt())
                    }
                    clearRoleBuff(role, skillOwner, result, this, clearBuffList, index)
                }
            }

            3 -> {
                // 斩杀
                val targets = getTargets(target[index], result, skillOwner)
                targets.forEach {
                    if (it.isDeathAvoid() && !skillOwner.isImmuneAvoid(this)) {
                        GameCore.instance.addSkillCastInfo(
                            skillOwner,
                            skillOwner.getSideName() + AppWrapper.getString(R.string.execute_failed),
                            this
                        )
                    } else {
                        GameCore.instance.addSkillCastInfo(
                            skillOwner,
                            skillOwner.getSideName() + AppWrapper.getString(R.string.execute_done) + it.getSideName(),
                            this
                        )
                        GameCore.instance.onBattleEffect(SoundEffect.SkillWinOrDirectDeath)
                        it.setCurrentHp(0)
                    }
                }
            }

            4 -> {
                // 免死
                val attackerDeathAvoid = triggerSkill != null && result.getAllRoles()
                    .firstOrNull { it.playerId() == triggerSkill.ownerIdentifier.playerId() }
                    ?.isImmuneAvoid(triggerSkill) == true
                if (skillOwner.isDeath() && !attackerDeathAvoid) {
                    GameCore.instance.addSkillCastInfo(
                        skillOwner,
                        skillOwner.getSideName() + AppWrapper.getString(R.string.gain_avoid_death),
                        this
                    )
                    skillOwner.setCurrentHp(1)
                } else {
                    GameCore.instance.addSkillCastInfo(
                        skillOwner,
                        skillOwner.getSideName() + AppWrapper.getString(R.string.avoid_death_failed),
                        this
                    )
                }
                skillOwner.addBuff(result,
                    skillOwner,
                    BuffFactory.createSpecificBuff(this@Skill, AVOID_DEATH)
                        .copy(addOnWhosTurn = result.getAttacker()).apply {
                            skillOwner.setBeingBuffState(this, this@Skill, index)
                        })
                skillChainHandler.invoke(ReAlive.copy(ownerIdentifier = skillOwner), result)
            }

            5 -> {
                // 直接胜利
                GameCore.instance.addSkillCastInfo(
                    skillOwner, name + AppWrapper.getString(R.string.direct_win), this
                )
                GameCore.instance.onBattleEffect(SoundEffect.SkillWinOrDirectDeath)
                result.addWinFlag(skillOwner.roleIdentifier)
            }

            6 -> {
                // 驱散所有护盾
                val targets = getTargets(target[index], result, skillOwner)
                targets.forEach { role ->
                    val clearBuffList = role.getDispelableGoodBuff().filter { it.isShield() }
                    clearRoleBuff(role, skillOwner, result, this, clearBuffList, index)
                }
            }

            7 -> {
                // 驱散指定debuff
                val targets = getTargets(target[index], result, skillOwner)
                targets.forEach { role ->
                    val clearBuffList =
                        role.getDispelableBadBuff().filter { it.id == effectNum[index].toInt() }
                    clearRoleDebuff(role, skillOwner, result, this, clearBuffList, index)
                }
            }

            8 -> {
                // 驱散指定buff
                val targets = getTargets(target[index], result, skillOwner)
                targets.forEach { role ->
                    val clearBuffList =
                        role.getDispelableGoodBuff().filter { it.id == effectNum[index].toInt() }
                    clearRoleBuff(role, skillOwner, result, this, clearBuffList, index)
                }
            }

            10 -> {
                if (result.getMinion(skillOwner) != null) {
                    GameCore.instance.addSkillCastInfo(
                        skillOwner,
                        skillOwner.getRace().name + AppWrapper.getString(R.string.minion_already_exists),
                        this
                    )
                } else {
                    //召唤一个仆从（target填怪物ID，effectNum填怪物属性比例）
                    val minion = GameCore.instance.getRacePool().first { it.id == target[index] }
                    GameCore.instance.addSkillCastInfo(
                        skillOwner,
                        skillOwner.getSideName() + AppWrapper.getString(R.string.summoned) + minion.name,
                        this
                    )
                    skillOwner.setSummonState(this, index)
                    result.addMinion(
                        DefaultAllyCreator.create(
                            minion, EMPTY_PROPERTY,// todo , debuff = effectNum[index].toInt(),
                            identifier = skillOwner.roleIdentifier.copy(name = minion.name)
                        ).copy(
                            roleIdentifier = if (skillOwner.isPlayerSide()) Identifier.player(
                                name = minion.name, masterId = ownerIdentifier.playerId()
                            ) else Identifier.enemy(
                                name = minion.name, masterId = ownerIdentifier.playerId()
                            )
                        )
                    )
                    skillChainHandler.invoke(Summon.copy(ownerIdentifier = skillOwner), result)
                }
            }

            else -> {
                error("特殊效果，无效type，${this.name}")
            }
        }
    }

    private suspend fun doGenEffect(index: Int, result: BattleField, skillOwner: Role) {
        when (subType[index]) {
            1 -> {
                // 释放指定技能
                GameCore.instance.getSkillById(effectNum[index].toInt())
                    .copy(ownerIdentifier = skillOwner).apply {
                        GameCore.instance.addSkillCastInfo(
                            skillOwner,
                            skillOwner.getSideName() + AppWrapper.getString(R.string.cast_skill) + name,
                            this@Skill
                        )
                    }.doEffect(result, skillOwner)
            }

            2 -> {
                // 释放普攻
                skillOwner.getSkills().firstOrNull { it.isNormalAttackType() }?.apply {
                    GameCore.instance.addSkillCastInfo(
                        skillOwner,
                        skillOwner.getSideName() + AppWrapper.getString(R.string.cast_skill) + name,
                        this@Skill
                    )
                }?.doEffect(result, skillOwner)
            }

            in 11..20 -> {
                if (result.getEnvironment().isNormal()) {
                    result.changeEnvironment(Environment.create(subType[index] - 10, this.name))
                    skillOwner.setEnvironmentState(this, index)
                }
            }

            in 31..40 -> {
                result.changeEnvironment(Environment.create(subType[index] - 30, this.name))
                skillOwner.setEnvironmentState(this, index)
            }

            else -> {
                error("衍生效果，无效type，${this.name}")
            }
        }
    }

    private fun doEnhancement(
        index: Int, result: BattleField, skillOwner: Role, triggerSkill: Skill?
    ) {
        val skillMainId = target[index]
        val targetSkill = skillOwner.getSkills().find { it.mainId == skillMainId }
        val num = getNum(index, result, skillOwner, triggerSkill, skillOwner)
        targetSkill?.let {
            skillOwner.enhanceSkill(
                it, SkillEnhancementType(subType[index]), num
            )
            GameCore.instance.addSkillCastInfo(
                skillOwner,
                skillOwner.getSideName() + AppWrapper.getString(R.string.gain_enhancement),
                this
            )
        } ?: GameCore.instance.addSkillCastInfo(
            skillOwner, AppWrapper.getString(R.string.enhancement_skip), this
        )
    }

    fun needCoolDown(realCoolDown: Int): Boolean {
        return currentCoolDown < realCoolDown && realCoolDown > 0
    }

    fun getAllEnhancements(): List<SkillEnhancement> {
        return enhancementOneGame
    }

    /**
     * 选中去游戏中，是一个新的实例，带uuid，和局外技能卡无关
     */
    fun copyToGame(): Skill {
        return copy(uuid = UUID.generateUUID().toString())
    }

    fun switchSelect(): Skill {
        return copy(selected = !selected, uuid = uuid.ifEmpty { UUID.generateUUID().toString() })
    }

    fun starUp(): Skill {
        return GameCore.instance.getSkillPool()
            .firstOrNull { it.mainId == mainId && it.level == level + 1 }?.copy(
                num = num,
                selected = selected,
                equipAllyUuid = equipAllyUuid,
                ownerIdentifier = ownerIdentifier,
                enhancementOneGame = enhancementOneGame, // 技能附魔，升星后附魔状态没了
                uuid = uuid
            ) ?: this
    }

    fun isNormalAttackType(): Boolean {
        return triggerType == 4
    }

    override fun create(): Skill? {
        return GameCore.instance.getSkillPool().firstOrNull { it.id == id }?.copy(
            num = this.num,
            ownerIdentifier = Identifier.player(),
            selected = this.selected,
            uuid = this.uuid,
            equipAllyUuid = this.equipAllyUuid,
            new = false,
            effected = this.effected,
            extraInfo = extraInfo,
            currentCoolDown = currentCoolDown,
            graveCount = graveCount,
            life = life
        )?.initYear()
    }

    override fun setUnNew(): GameItem {
        return copy(new = false)
    }

    fun initYear(): Skill {
        return copy(life = life)
    }

    fun nextYear(): Skill {
        return copy(life = life)
    }

    fun isMeleeAttack(): Boolean {
        return true
    }

    fun isSkillSheet(type: Int): Boolean {
        return when (type) {
            1 -> isBattleTree()
            2 -> isJinNang()
            3 -> isBinFuTree()
            else -> isZhenLing()
        }
    }

    fun getPrevSkill(): Skill? {
        return GameCore.instance.getSkillPool()
            .firstOrNull { it.mainId == mainId && it.level == level - 1 }
    }

    fun canShowStar(): Boolean {
        return !isBattleTree() && !isAdventure() && !isHalo() && !isZhenYing() && !isZhenFa()
    }

    fun skillTreeId(): Int {
        return GameCore.instance.getScrollPool().firstOrNull { it.id == id }?.skillTreeId ?: mainId
    }
}
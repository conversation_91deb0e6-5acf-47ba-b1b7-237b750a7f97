package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import com.moyu.core.R

enum class DamageType(val display: String, val defenseDisplay: String, val value: Int) {
    DamageType1(AppWrapper.getString(R.string.damage1), AppWrapper.getString(R.string.defense1), 1),
    DamageType2(AppWrapper.getString(R.string.damage2), AppWrapper.getString(R.string.defense1), 2),
    DamageType3(AppWrapper.getString(R.string.damage3), AppWrapper.getString(R.string.defense2), 3),
    DamageType4(AppWrapper.getString(R.string.damage4), AppWrapper.getString(R.string.defense2), 4),
    DamageType5(AppWrapper.getString(R.string.damage5), AppWrapper.getString(R.string.defense2), 5),;

    companion object {
        fun fromTypeValue(type: Int): DamageType? {
            return entries.firstOrNull { it.value == type }
        }

        fun fromDisplayValue(display: String): DamageType? {
            return entries.firstOrNull { it.display == display }
        }
    }

    fun getDamageName(): String {
        return when (this) {
            DamageType1 -> DamageType1.display
            DamageType2 -> DamageType2.display
            DamageType3 -> DamageType3.display
            DamageType4 -> DamageType4.display
            DamageType5 -> DamageType5.display
        }
    }

    fun getDefenseName(): String {
        return when (this) {
            DamageType1 -> DamageType1.display.take(1) + DamageType1.defenseDisplay.take(1)
            DamageType2 -> DamageType2.display.take(1) + DamageType2.defenseDisplay.take(1)
            DamageType3 -> DamageType3.display.take(1) + DamageType3.defenseDisplay.take(1)
            DamageType4 -> DamageType4.display.take(1) + DamageType4.defenseDisplay.take(1)
            DamageType5 -> DamageType5.display + DamageType5.defenseDisplay
        }
    }
}
package com.moyu.core

// taptap
const val tapClientId = "ngtimzzzwknfjovjfd"
const val tapClientToken = "ixr6OgkF4Eefw8TiERldDETqKdDfj6SX0VA2py7i"
const val tapServerUrl = "https://ngtimzzz.cloud.tds1.tapapis.cn"
const val tapShareCodeUrl = "https://www.taptap.cn/app/618147"

// 好游快爆
const val gameId = "39162"

// bugly
const val buglyId = "d6beea005d"


// 存档
const val DS_NAME = "_error_fatal"

// 加密密钥
const val INT_ENCRYPT = "yzIP*?%&1o"

// todo 正式上线还要注意修改云存档/排行榜的api

// 隐私
const val privacyLink = "https://share.note.youdao.com/s/FxOWhc3y"

// 许可
const val licenseLink = "https://note.youdao.com/s/2nuY7boI"

const val discordLink = "https://discord.gg/JVtQuapDwT"


const val AD_UNIT_ID_TEST = "ca-app-pub-3940256099942544/**********"
const val AD_UNIT_ID = "ca-app-pub-5058022002121914/**********"


const val merchantId = "**********"
const val APPID = "wxffac27d7475f1e00"
const val wcMusic = "battleeffect_16.mp3"

const val appFlyerDevKey = "BerpMdszVXZZsKMmBgkLYm"

const val configPath = "impossible.mp3"
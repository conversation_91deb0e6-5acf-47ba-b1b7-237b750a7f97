plugins {
    id 'com.android.library'
    id 'kotlin-android'
}

android {
    compileSdk 35

    defaultConfig {
        minSdk 21
        targetSdk 35

        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_19
        targetCompatibility JavaVersion.VERSION_19
    }
    kotlinOptions {
        jvmTarget = '19'
    }
    namespace 'com.moyu.job'
}

dependencies {
}
# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os
from opencc import OpenCC
import pathlib


CURRENT_DIRECTORY = os.path.dirname(os.path.abspath(__file__))
# 目标文件放置文件夹
DIR_PATH = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/lite/ko/")
# 压缩后的文件名
COMPRESS_FILE = pathlib.Path(CURRENT_DIRECTORY).joinpath("../game/src/main/assets/ko_impossible.mp3")


def convert_to_traditional_chinese(text):
    # 创建 OpenCC 实例，用于简繁转换
    cc = OpenCC('s2t')  # 使用 s2twp 模式进行简繁转换（台湾标准繁体）
    #cc = OpenCC('t2s')  # 使用 s2twp 模式进行简繁转换（台湾标准繁体）
    return cc.convert(text)

def process_file(file_path):
    # 读取文件内容并进行简繁转换
    print(file_path)
    if (file_path.endswith(".DS_Store") == False):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        converted_content = convert_to_traditional_chinese(content)
    
        # 写入文件（覆盖原文件）
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(converted_content)

def process_files_in_directory(directory):
    # 遍历目录下的所有文件和子目录
    for root, dirs, files in os.walk(directory):
        for file in files:
            file_path = os.path.join(root, file)
            # 只处理文件，忽略子目录
            if os.path.isfile(file_path):
                # 进行简繁转换
                process_file(file_path)
                
                
                

def compress_dir():
    p = "58598744820488376"
    try:
        import pyminizip
    except ImportError:
        print("[-]未发现依赖项pyminizip，正在自动安装........")
        os.system("pip install pyminizip")
        import pyminizip
        print("[+]完成安装依赖项pyminizip\n")

    file_path = []
    all_files = os.listdir(DIR_PATH)
    for item in all_files:
        file_path.append(DIR_PATH.joinpath(item).resolve().as_posix())
    pyminizip.compress_multiple(file_path, [], COMPRESS_FILE.resolve().as_posix(), p, 1)
    print(f"[2]打包压缩文件成功:{COMPRESS_FILE.resolve()}")
    pass


# 处理目录下的所有文件
compress_dir()


// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        mavenCentral()
        jcenter() // Warning: this repository is going to shut down soon
        mavenCentral()
        maven { url 'https://repo1.maven.org/maven2/' }
        maven {
            url = uri("https://dl-maven-android.mintegral.com/repository/mbridge_android_sdk_oversea")
        }
        maven {
            url = uri("https://artifact.bytedance.com/repository/pangle/")
        }
        maven {
            url 'https://artifact.bytedance.com/repository/pangle'
        }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.6.1'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.9.24'
        classpath "org.jetbrains.kotlin:kotlin-serialization:1.9.24"
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.job.JobContent

/**
 * 在lite版本其实是compress和加密
 * 目标路径是filesDir+impossible.mp3
 */
class UncompressTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
//        val path = GameApp.instance.filesDir
//        val compressDir = File(path.absolutePath + File.separator + "impossible.mp3")
//        if (compressDir.exists()) {
//            compressDir.delete()
//        }
//        compressConfigs(compressDir.absolutePath, getVersions())
    }
}
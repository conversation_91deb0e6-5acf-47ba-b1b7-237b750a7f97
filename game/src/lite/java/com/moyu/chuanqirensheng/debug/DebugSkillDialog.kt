package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.SearchView
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.text.SKILL_LABELS
import com.moyu.chuanqirensheng.text.getSkillTypeName
import com.moyu.core.logic.info.getElementTypeName
import com.moyu.core.model.skill.Skill


const val SKILL_TYPE = 8
const val SKILL_ELEMENT = 6
const val SKILL_LABEL = 8

@Composable
fun DebugSkillDialog(callback: MutableState<((Skill) -> Unit)?>) {
    callback.value?.let {
        PanelDialog(onDismissRequest = {
            callback.value = null
        }) {
            Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
                val type = remember {
                    mutableStateOf(0)
                }
                val showType = remember {
                    mutableStateOf(false)
                }
                val element = remember {
                    mutableStateOf(0)
                }
                val showElement = remember {
                    mutableStateOf(false)
                }
                val label = remember {
                    mutableStateOf(0)
                }
                val showLabel = remember {
                    mutableStateOf(false)
                }
                val level = remember {
                    mutableStateOf(-1)
                }
                val showLevel = remember {
                    mutableStateOf(false)
                }
                val search = remember {
                    mutableStateOf("")
                }
                val skills =
                    repo.gameCore.getSkillPool().filter {
                        (type.value == 0 || it.skillType == type.value)
                                && (element.value == 0 || it.elementType == element.value)
                                && (label.value == 0 || label.value in it.skillTagIds)
                                && (level.value == -1 || level.value == it.level)
                    }.filter {
                        if (search.value.isNotEmpty()) {
                            it.name.contains(search.value) || it.desc.contains(search.value)
                        } else true
                    }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    GameButton(
                        buttonSize = ButtonSize.MediumMinus,
                        text = if (type.value == 0) "技能类别" else type.value.getSkillTypeName(),
                        onClick = {
                            showType.value = !showType.value
                        }
                    )
                    GameButton(
                        buttonSize = ButtonSize.MediumMinus,
                        text = if (element.value == 0) "技能类型" else element.value.getElementTypeName(),
                        onClick = {
                            showElement.value = !showElement.value
                        }
                    )
                    GameButton(
                        buttonSize = ButtonSize.MediumMinus,
                        text = if (label.value == 0) "技能标签" else SKILL_LABELS[label.value].toString(),
                        onClick = {
                            showLabel.value = !showLabel.value
                        }
                    )
                    GameButton(
                        buttonSize = ButtonSize.MediumMinus,
                        text = if (level.value == -1) "技能星级" else "${level.value}星",
                        onClick = {
                            showLevel.value = !showLevel.value
                        }
                    )
                }
                SearchView(search)
                if (showType.value) {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        overflow = FlowRowOverflow.Visible,
                    ) {
                        repeat(SKILL_TYPE) {
                            val id = it + 1
                            val text = id.getSkillTypeName()
                            val selected = id == type.value
                            GameButton(
                                text = text,
                                buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Orange,
                                buttonSize = ButtonSize.Small,
                                onClick = {
                                    if (type.value == id) {
                                        type.value = 0
                                    } else {
                                        type.value = id
                                    }
                                    showType.value = false
                                }
                            )
                        }
                    }
                }
                if (showLevel.value) {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        overflow = FlowRowOverflow.Visible,
                    ) {
                        repeat(6) {
                            val text = "${it}星"
                            val selected = it == level.value
                            GameButton(
                                text = text,
                                buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Orange,
                                buttonSize = ButtonSize.Small,
                                onClick = {
                                    if (level.value == it) {
                                        level.value = -1
                                    } else {
                                        level.value = it
                                    }
                                    showLevel.value = false
                                }
                            )
                        }
                    }
                }
                if (showElement.value) {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        overflow = FlowRowOverflow.Visible,
                    ) {
                        repeat(SKILL_ELEMENT) {
                            val id = it + 1
                            val text = id.getElementTypeName()
                            val selected = id == element.value
                            GameButton(
                                text = text,
                                buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Orange,
                                buttonSize = ButtonSize.Small,
                                onClick = {
                                    if (element.value == id) {
                                        element.value = 0
                                    } else {
                                        element.value = id
                                    }
                                    showElement.value = false
                                }
                            )
                        }
                    }
                }
                if (showLabel.value) {
                    FlowRow(
                        modifier = Modifier.fillMaxWidth(),
                        overflow = FlowRowOverflow.Visible,
                    ) {
                        repeat(SKILL_LABEL) {
                            val id = it + 1
                            val text = SKILL_LABELS[id].toString()
                            val selected = id == label.value
                            GameButton(
                                text = text,
                                buttonStyle = if (selected) ButtonStyle.Blue else ButtonStyle.Orange,
                                buttonSize = ButtonSize.Small,
                                onClick = {
                                    if (label.value == id) {
                                        label.value = 0
                                    } else {
                                        label.value = id
                                    }
                                    showLabel.value = false
                                }
                            )
                        }
                    }
                }
                LazyVerticalGrid(
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    modifier = Modifier.fillMaxWidth(),
                    columns = GridCells.Fixed(4)
                ) {
                    items(skills.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = skills[index])
                            GameButton(text = "学习") {
                                callback.value?.invoke(skills[index])
                            }
                        }
                    }
                }
            }
        }
    }
}
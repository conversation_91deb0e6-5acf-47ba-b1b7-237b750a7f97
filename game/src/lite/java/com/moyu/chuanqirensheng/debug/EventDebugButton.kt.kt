package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.skill.SingleEquipView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.model.skill.isBinFuTree
import com.moyu.core.model.skill.isJinNang
import com.moyu.core.model.skill.isZhenLing
import com.moyu.core.model.skill.isTalent
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun EventIdTag(modifier: Modifier, event: Event, cardSize: CardSize) {
    Column(modifier = modifier) {
        if (event.isMainLine == 1) {
            Text(
                text = stringResource(R.string.main_line), style = cardSize.getTextStyle()
            )
        }
        Text(
            text = event.id.toString(), style = cardSize.getTextStyle()
        )
    }
}

@Composable
fun EventDebugButton(modifier: Modifier) {
    val show = remember {
        mutableStateOf(false)
    }
    val showJinNang = remember {
        mutableStateOf(false)
    }
    val showPolicy = remember {
        mutableStateOf(false)
    }
    val showBinFu = remember {
        mutableStateOf(false)
    }
    val showZhanJiShu = remember {
        mutableStateOf(false)
    }
    val showTalent = remember {
        mutableStateOf(false)
    }
    val showEquip = remember {
        mutableStateOf(false)
    }
    FlowRow(
        modifier = modifier
            .background(W50)
            .verticalScroll(rememberScrollState()),
        overflow = FlowRowOverflow.Visible,
    ) {
        val text = remember {
            mutableStateOf("")
        }
        GameButton(text = "局内调试") {
            show.value = !show.value
        }
        if (show.value) {
            GameButton(text = "跳过1天") {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 1)
                }
            }
            GameButton(text = "跳过10天") {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 10)
                }
            }
            GameButton(text = "跳过50天") {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 50)
                }
            }
            GameButton(text = "冒险属性10") {
                Award(
                    advProperty = AdventureProps(
                        science = 10, politics = 10, military = 10, religion = 10, commerce = 10
                    )
                ).let {
                    GameApp.globalScope.launch {
                        AwardManager.gainAward(award = it)
                    }
                }
            }
            repeat(5) {
                GameButton(text = "${it + 1}属性+5") {
                    Award(
                        advProperty = AdventureProps.getRoleProperty(it + 1, 5)
                    ).let {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(award = it)
                        }
                    }
                }
            }

            GameButton(text = "粮草5000") {
                GameApp.globalScope.launch {
                    AwardManager.gainAward(award = Award(resource = 5000))
                }
            }
            GameButton(text = "功勋5100") {
                GameApp.globalScope.launch {
                    AwardManager.gainAward(award = Award(exp = 5100))
                }
            }
            GameButton(text = "功勋10000") {
                GameApp.globalScope.launch {
                    AwardManager.gainAward(award = Award(exp = 10000))
                }
            }
            GameButton(text = "铜钱5000") {
                GameApp.globalScope.launch {
                    AwardManager.gainAward(award = Award(gold = 5000))
                }
            }
            GameButton(text = "所有盟友掉血30%") {
                GameApp.globalScope.launch {
                    BattleManager.getGameAllies().forEach {
                        BattleManager.hurtAllyInGame(it, 30)
                    }
                }
            }
            (0..5).forEach { star ->
                GameButton(text = "${star}星盟友") {
                    Award(allies = repo.gameCore.getAllyPool().filter { it.star == star }).let {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(award = it)
                        }
                    }
                }
            }
            GameButton(text = "获得所有锦囊") {
                repo.gameCore.getSkillPool().filter { it.isJinNang() }
                    .filter { it.name.contains(text.value) }.forEach {
                        GameApp.globalScope.launch {
                            BattleManager.gainSkillInGame(target = it)
                        }
                    }
            }
            GameButton(text = "显示锦囊") {
                showJinNang.value = !showJinNang.value
            }
            if (showJinNang.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isJinNang() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "获得所有政令") {
                repo.gameCore.getSkillPool().filter { it.isZhenLing() }
                    .filter { it.name.contains(text.value) }.forEach {
                        GameApp.globalScope.launch {
                            BattleManager.gainSkillInGame(target = it)
                        }
                    }
            }
            GameButton(text = "显示政令") {
                showPolicy.value = !showPolicy.value
            }
            if (showPolicy.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isZhenLing() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "获得所有兵符") {
                repo.gameCore.getSkillPool().filter { it.isBinFuTree() }
                    .filter { it.name.contains(text.value) }.forEach {
                        GameApp.globalScope.launch {
                            BattleManager.gainSkillInGame(target = it)
                        }
                    }
            }
            GameButton(text = "显示兵符") {
                showBinFu.value = !showBinFu.value
            }
            if (showBinFu.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isBinFuTree() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示战技树") {
                showZhanJiShu.value = !showZhanJiShu.value
            }
            if (showZhanJiShu.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isBattleTree() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示天赋") {
                showTalent.value = !showTalent.value
            }
            if (showTalent.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isTalent() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.you.value =  BattleManager.you.value.apply {
                                            learnSkill(it, roleIdentifier)
                                        }
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示装备") {
                showEquip.value = !showEquip.value
            }
            if (showEquip.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getEquipPool().filter { it.star == 0 }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleEquipView(equipment = it)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                    GameApp.globalScope.launch {
                                        BattleManager.gainEquip(it)
                                    }
                                })
                        }
                    }
            }
        }
    }
}
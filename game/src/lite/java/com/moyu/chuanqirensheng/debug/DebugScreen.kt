package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Checkbox
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Slider
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.story.STORY_3_ZHAOYUN
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.feature.tcg.toTcgCardsObject
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_TCG_CARDS
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setObject
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.bigButtonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.slideHeight
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.ALLY_ROW2_THIRD
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.logic.role.ENEMY_ROW2_FIRST
import com.moyu.core.logic.role.positionList
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_BADGE
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.getRaceTypeName
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.launch

val allRaceIds = (1..9).toList()

@Composable
fun DebugScreen() {
    BackPressHandler {
        DebugManager.dryTest = false
        DebugManager.debugBattle = false
        goto(LOGIN_SCREEN)
    }
    val enemySpeciesDialog = remember {
        mutableStateOf(false)
    }
    val enemySpecies = remember {
        mutableStateOf(1)
    }
    val enemySkills = remember {
        mutableListOf<Skill>()
    }
    val enemyMap = remember {
        mutableStateListOf(ALLY_ROW1_FIRST, ENEMY_ROW1_FIRST)
    }
    val enemyNumDialog = remember {
        mutableStateOf(false)
    }
    val playerSkills = remember {
        mutableListOf<Skill>()
    }
    GameBackground(title = "调试页") {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(top = padding16)
                .verticalScroll(rememberScrollState())
        ) {
            Row {
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(padding16)
                ) {
                    DebugItem(
                        DebugManager.easySkill, "技能概率100%"
                    ) {
                        DebugManager.easySkill = it
                    }
                    DebugItem(
                        DebugManager.dodge100, "格挡率100%"
                    ) {
                        DebugManager.dodge100 = it
                    }
                    DebugItem(
                        DebugManager.fatal100, "暴击率100%"
                    ) {
                        DebugManager.fatal100 = it
                    }
                    DebugItem(
                        DebugManager.attack100, "攻击100倍"
                    ) {
                        DebugManager.attack100 = it
                    }
                    DebugItem(
                        DebugManager.defense100, "防御100倍"
                    ) {
                        DebugManager.defense100 = it
                    }
                    DebugItem(
                        DebugManager.hp100, "100倍血量"
                    ) {
                        DebugManager.hp100 = it
                    }
                    DebugItem(
                        DebugManager.singleStep, "单步调试"
                    ) {
                        DebugManager.singleStep = it
                    }
                    DebugItem(
                        DebugManager.unbreakable, "无敌秒杀"
                    ) {
                        DebugManager.unbreakable = it
                    }
                    DebugItem(
                        DebugManager.uploadRank, "Lite包上传排行榜"
                    ) {
                        DebugManager.uploadRank = it
                    }
                }
                Column(
                    horizontalAlignment = Alignment.Start,
                    modifier = Modifier
                        .weight(1f)
                        .padding(padding16)
                ) {
                    DebugItem(
                        DebugManager.unlockAll, "解锁全部"
                    ) {
                        DebugManager.unlockAll = it
                    }
                    DebugItem(
                        DebugManager.easyEvent, "事件条件满足"
                    ) {
                        DebugManager.easyEvent = it
                    }
                    DebugItem(
                        DebugManager.oneCentShop, "商品1分钱"
                    ) {
                        DebugManager.oneCentShop = it
                    }
                    DebugItem(
                        DebugManager.eventWin, "事件必定胜利"
                    ) {
                        DebugManager.eventWin = it
                    }
                    DebugItem(
                        DebugManager.eventLose, "事件必定失败"
                    ) {
                        DebugManager.eventLose = it
                    }
                    DebugItem(
                        DebugManager.allEvent, "显示所有事件"
                    ) {
                        DebugManager.allEvent = it
                    }
                    DebugItem(
                        DebugManager.repeatEvent, "可重复进入事件"
                    ) {
                        DebugManager.repeatEvent = it
                    }
                    DebugItem(
                        DebugManager.questDone, "任务自动完成"
                    ) {
                        DebugManager.questDone = it
                    }
                    DebugItem(
                        DebugManager.solidEquip, "武器店固定武器，方便测试武器升级"
                    ) {
                        DebugManager.solidEquip = it
                    }
                }
            }
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 5
                            }
                            repo.allyManager.gain(allies)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有5星军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            val allies = repo.gameCore.getAllyPool().filter {
                                it.star == 0
                            }
                            repo.allyManager.gain(allies)
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得所有0星军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            repo.skillManager.data.clear()
                            repo.skillManager.save()
                            repo.allyManager.data.clear()
                            repo.allyManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除所有军团卡", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TalentManager.talents.clear()
                            repo.gameCore.getTalentPool().filter { it.level == it.levelLimit }.forEach {
                                TalentManager.talents[it.mainId] = it.levelLimit
                            }
                            TalentManager.save()
                        }
                        "已学习".toast()
                    }) {
                    Text(
                        text = "学习所有天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TalentManager.talents.clear()
                            TalentManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除所有天赋", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        EndingManager.deleteAll()
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除所有人生记录", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                repeat(20) { star ->
                    EffectButton(modifier = Modifier
                        .height(bigButtonHeight)
                        .background(B50),
                        onClick = {
                            GameApp.globalScope.launch {
                                increaseIntValueByKey(
                                    KEY_GAME_LOGIN_DAY,
                                    star + 1
                                )
                            }
                            "已获得".toast()
                        }) {
                        Text(
                            text = "登录${star + 1}天", style = MaterialTheme.typography.h4
                        )
                    }
                    Spacer(modifier = Modifier.size(padding4))
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                ReputationManager.reputations.forEachIndexed { index, s ->
                    GameButton(buttonSize = ButtonSize.MediumMinus, text = "$s + 100") {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(
                                Award(
                                    reputations = EMPTY_REPUTATION.toMutableList().apply {
                                        set(index, 100)
                                    })
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding8))
            val listState = rememberLazyListState()
            val targetItem = TowerManager.maxLevel.value
            // Scroll to the target item
            LaunchedEffect(targetItem) {
                listState.animateScrollToItem(targetItem)
            }
            LazyRow(state = listState) {
                items(1500) { star ->
                    EffectButton(
                        modifier = Modifier
                            .height(bigButtonHeight)
                            .background(B50)
                            .padding(end = padding4), // 用右边距替代 Spacer
                        onClick = { TowerManager.maxLevel.value = star }
                    ) {
                        Text(
                            text = "爬塔${star}层",
                            style = MaterialTheme.typography.h4
                        )
                    }
                }
            }
            Row(modifier = Modifier.horizontalScroll(rememberScrollState())) {
                ReputationManager.reputations.forEachIndexed { index, s ->
                    GameButton(buttonSize = ButtonSize.MediumMinus, text = "$s + 2000") {
                        GameApp.globalScope.launch {
                            AwardManager.gainAward(
                                Award(
                                    reputations = EMPTY_REPUTATION.toMutableList().apply {
                                        set(index, 2000)
                                    })
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            repo.gameCore.getSkinPool().forEach {
                                SkinManager.gainSkin(it)
                            }
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得全部皮肤", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            SkinManager.gainedSkins.clear()
                            SkinManager.currentSkins.clear()
                            SkinManager.save()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除全部皮肤", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        AwardManager.diamond.value = 0
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除玉石", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            AwardManager.badges.clear()
                        }
                        "已删除".toast()
                    }) {
                    Text(
                        text = "删除全部印章", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TcgManager.tcgCards.clear()
                            TcgManager.tcgCards.addAll(repo.gameCore.getTcgCardPool().map {
                                it.copy(count = 1)
                            })
                            setObject(
                                KEY_TCG_CARDS,
                                TcgManager.tcgCards.toTcgCardsObject(),
                            )
                        }
                        "已获得".toast()
                    }) {
                    Text(
                        text = "获得全部tcg卡牌", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            TcgManager.tcgCards.clear()
                            setObject(
                                KEY_TCG_CARDS,
                                TcgManager.tcgCards.toTcgCardsObject(),
                            )
                        }
                        "已清除".toast()
                    }) {
                    Text(
                        text = "删除所有tcg卡牌", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        TalentManager.talents.clear()
                        TalentManager.save()
                        "已清除".toast()
                    }) {
                    Text(
                        text = "删除所有天赋", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(badges = EMPTY_BADGE.map {
                                10
                            }).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "各种徽章10个", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(pvpDiamond = 100000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "Pvp货币100000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(diamond = 10000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "荣誉点10000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(key = 30000).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "钻石30000", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding8))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value = Award(lotteryMoney = 100).apply {
                                AwardManager.gainAward(this)
                            }
                        }
                    }) {
                    Text(
                        text = "红宝石100", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(KEY_WAR_PASS_UNLOCK_EVIDENCE)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁战令1", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(KEY_WAR_PASS2_UNLOCK_EVIDENCE)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁战令2", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        GameApp.globalScope.launch {
                            Dialogs.awardDialog.value =
                                Award(unlockList = listOf(STORY_3_ZHAOYUN)).apply {
                                    AwardManager.gainAward(this)
                                }
                        }
                    }) {
                    Text(
                        text = "解锁故事包3", style = MaterialTheme.typography.h4
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row {
                Spacer(modifier = Modifier.size(padding16))
                val electric = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (electric.value == 0f) "特权值" else "${electric.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(electric = electric.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }

                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = electric.value,
                        onValueChange = { electric.value = it.toInt().toFloat() },
                        valueRange = 0f..2000f,
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                val warpass = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (warpass.value == 0f) "通行证" else "${warpass.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass = warpass.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = warpass.value,
                        onValueChange = { warpass.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
                val warpass2 = remember {
                    mutableStateOf(0f)
                }
                Column(
                    modifier = Modifier
                        .height(bigButtonHeight)
                        .weight(2f),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        GameButton(
                            text = if (warpass2.value == 0f) "通行证2" else "${warpass2.value}",
                            buttonSize = ButtonSize.MediumMinus
                        ) {
                            GameApp.globalScope.launch {
                                Dialogs.awardDialog.value =
                                    Award(warPass2 = warpass2.value.toInt()).apply {
                                        AwardManager.gainAward(this)
                                    }
                            }
                        }
                    }
                    Slider(
                        modifier = Modifier
                            .height(slideHeight)
                            .fillMaxWidth(),
                        steps = 10,
                        value = warpass2.value,
                        onValueChange = { warpass2.value = it.toInt().toFloat() },
                        valueRange = 0f..100f,
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row {
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySkills.clear()
                        debugSkillDialog.value = {
                            enemySkills.add(it)
                        }
                    }) {
                    Text(
                        text = "敌人技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemySpeciesDialog.value = true
                    }) {
                    Text(
                        text = "敌人种族", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        enemyNumDialog.value = true
                    }) {
                    Text(
                        text = "双方阵型", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        playerSkills.clear()
                        debugSkillDialog.value = {
                            playerSkills.add(it)
                        }
                    }) {
                    Text(
                        text = "玩家技能", style = MaterialTheme.typography.h4
                    )
                }
                Spacer(modifier = Modifier.size(padding16))
                EffectButton(modifier = Modifier
                    .height(bigButtonHeight)
                    .weight(1f)
                    .background(B50),
                    onClick = {
                        DebugManager.debugBattle = true
                        val roleHashMap = mutableMapOf<Int, Role?>()
                        positionList.forEach {
                            roleHashMap[it] = null
                        }
                        enemyMap.forEachIndexed { index, position ->
                            if (position in ALLY_ROW1_FIRST..ALLY_ROW2_THIRD) {
                                DefaultAllyCreator.create(
                                    repo.gameCore.getRacePool().filter { it.star == 0 }[index],
                                    Property(),
                                    identifier = Identifier.player(name = "玩家$index")
                                ).apply {
                                    setSkills(emptyList())
                                    if (roleHashMap.values.mapNotNull { it }
                                            .none { it.isPlayerSide() }) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        playerSkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(40025), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (roleHashMap.values.mapNotNull { it }
                                                .filter { it.isPlayerSide() }.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(40073),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(40032),
                                                this.roleIdentifier
                                            )
                                        }
                                    }
                                    roleHashMap[position] = this
                                }
                            } else {
                                DefaultRoleCreator.create(
                                    repo.gameCore.getRacePool().filter {
                                        it.raceType == enemySpecies.value && it.star == 0
                                    }[index], Property(), identifier =  Identifier.enemy(name = "敌人$index")
                                ).apply {
                                    setSkills(emptyList())
                                    if (roleHashMap.values.mapNotNull { it }
                                            .none { !it.isPlayerSide() }) {
                                        // 设置的技能只有第一个敌人学习，不然有点乱
                                        setSkills(emptyList())
                                        enemySkills.forEach {
                                            learnSkill(it, this.roleIdentifier)
                                        }
                                        learnSkill(
                                            repo.gameCore.getSkillById(40025), this.roleIdentifier
                                        )
                                    } else {
                                        // 其他只有普攻
                                        if (roleHashMap.values.mapNotNull { it }
                                                .filter { !it.isPlayerSide() }.size == 1) {
                                            learnSkill(
                                                repo.gameCore.getSkillById(40054),
                                                this.roleIdentifier
                                            )
                                        } else {
                                            learnSkill(
                                                repo.gameCore.getSkillById(40055),
                                                this.roleIdentifier
                                            )
                                        }
                                    }
                                    roleHashMap[position] = this
                                }
                            }
                        }
                        repo.battleRoles.clear()
                        repo.battleRoles.putAll(roleHashMap)
                        repo.startBattle()
                        goto(DEBUG_BATTLE)
                    }) {
                    Text(
                        text = "开始战斗", style = MaterialTheme.typography.h4
                    )
                }
            }
        }
    }
    if (enemySpeciesDialog.value) {
        PanelDialog(onDismissRequest = {
            enemySpeciesDialog.value = false
        }) {
            LazyVerticalGrid(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                columns = GridCells.Fixed(5)
            ) {
                items(allRaceIds.size) { index ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = (index + 1).getRaceTypeName(),
                            style = MaterialTheme.typography.h4
                        )
                        GameButton(text = "选择") {
                            enemySpecies.value = allRaceIds[index]
                            enemySpeciesDialog.value = false
                        }
                    }
                }
            }
        }
    }
    if (enemyNumDialog.value) {
        PanelDialog(onDismissRequest = {
            enemyNumDialog.value = false
        }) {
            FlowRow(
                horizontalArrangement = Arrangement.SpaceEvenly,
                modifier = Modifier.fillMaxWidth(),
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 3
            ) {
                repeat(3) { index ->
                    val position = ENEMY_ROW2_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有敌人"
                    } else {
                        "无敌人"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
                repeat(3) { index ->
                    val position = ENEMY_ROW1_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有敌人"
                    } else {
                        "无敌人"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
                Spacer(modifier = Modifier
                    .height(gapLarge)
                    .fillMaxWidth())
                repeat(6) { index ->
                    val position = ALLY_ROW1_FIRST + index
                    val text = if (enemyMap.contains(position)) {
                        "有盟友"
                    } else {
                        "无盟友"
                    }
                    GameButton(
                        text = text,
                        buttonSize = ButtonSize.Small,
                        buttonStyle = if (enemyMap.contains(position)) ButtonStyle.Orange else ButtonStyle.Blue
                    ) {
                        if (enemyMap.contains(position)) {
                            enemyMap.remove(position)
                        } else {
                            enemyMap.add(position)
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DebugItem(checked: Boolean, content: String, onChecked: suspend (Boolean) -> Unit) {
    Row(verticalAlignment = Alignment.CenterVertically) {
        Checkbox(
            checked = checked, onCheckedChange = {
                GameApp.globalScope.launch {
                    onChecked(it)
                }
            }, modifier = Modifier.size(
                buttonHeight
            )
        )
        Spacer(modifier = Modifier.size(padding4))
        Text(
            text = content.trimIndent(), style = MaterialTheme.typography.h4
        )
    }
}

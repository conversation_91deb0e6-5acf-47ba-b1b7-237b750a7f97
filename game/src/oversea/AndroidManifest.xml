<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <uses-permission android:name="com.android.vending.BILLING" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
    <application
        android:networkSecurityConfig="@xml/network_security_config"
        android:usesCleartextTraffic="true">
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-5058022002121914~5116960615"/>
        <meta-data android:name="com.google.android.gms.games.APP_ID"
            android:value="331003772156" />
        <meta-data android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version"/>

        <!-- Resolve AD Services config conflict between measurement-api & ads-lite -->
        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource" />
        <meta-data android:name="com.facebook.sdk.ApplicationId" android:value="2524554567914613"/>
        <meta-data android:name="com.facebook.sdk.ClientToken" android:value="********************************"/>
    </application>
</manifest>
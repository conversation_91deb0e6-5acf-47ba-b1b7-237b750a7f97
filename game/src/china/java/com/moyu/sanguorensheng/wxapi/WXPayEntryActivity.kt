package com.moyu.sanguorensheng.wxapi

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.BillingManager.api
import com.moyu.chuanqirensheng.sub.bill.BillingManager.checkIfPayed
import com.moyu.chuanqirensheng.sub.bill.BillingManager.payClientDataList
import com.tencent.mm.opensdk.constants.ConstantsAPI
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbase.BaseResp
import com.tencent.mm.opensdk.openapi.IWXAPIEventHandler

class WXPayEntryActivity : Activity(), IWXAPIEventHandler {

    public override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            api?.handleIntent(intent, this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        api?.handleIntent(intent, this)
    }

    override fun onReq(req: BaseReq) {
        finish()
    }

    override fun onResp(resp: BaseResp) {
        if (resp.type == ConstantsAPI.COMMAND_PAY_BY_WX) {
            when (resp.errCode) {
                0 -> {
                    getString(R.string.pay_ok).toast()
                    checkIfPayed(payClientDataList.last())
                }
                -2 -> {
                    getString(R.string.pay_cancel).toast()
                    BillingManager.removePayClientData(payClientDataList.last())
                }
                else -> {
                    getString(R.string.pay_error).toast()
                    BillingManager.removePayClientData(payClientDataList.last())
                }
            }
        }
        finish()
    }
}
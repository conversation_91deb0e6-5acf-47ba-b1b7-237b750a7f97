package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.job.JobContent
import com.scottyab.rootbeer.RootBeer
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


class RootCheckerTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        if (BuildConfig.FLAVOR.contains("Lite")) { // Lite包不进行root检测
            return
        }
        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
            GameApp.globalScope.launch {
                async(Dispatchers.IO) {
                    while (true) {
                        delay(1 * 1000)
                        val rootBeer = RootBeer(GameApp.instance)
                        if (rootBeer.checkForBinary("su")
                            || rootBeer.checkForDangerousProps()
                            || rootBeer.checkForRWPaths()
                            || rootBeer.detectTestKeys()
                            || rootBeer.checkSuExists()
                            || rootBeer.checkForRootNative()
                            || rootBeer.checkForMagiskBinary()
                        ) {
                            context.getString(R.string.root_tips).toast()
                            delay(2000)
                            killSelf()
                        }
                        delay(5 * 1000)
                    }
                }
            }
        }
    }
}
package com.moyu.chuanqirensheng.sub.share.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.SHARE_WECHAT_CIRCLE
import com.moyu.chuanqirensheng.sub.bill.SHARE_WECHAT_FRIEND
import com.moyu.chuanqirensheng.sub.share.ShareManager
import com.moyu.chuanqirensheng.sub.share.ShareManager.gainShare1Award
import com.moyu.chuanqirensheng.sub.share.ShareManager.gainShare2Award
import com.moyu.chuanqirensheng.sub.share.ShareManager.share1Gained
import com.moyu.chuanqirensheng.ui.theme.W10
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun ShareEndingLayout(ending: Ending) {
    EffectButton(
        onClick = {
            BillingManager.shareToWechat(
                GameApp.instance.getWrapString(R.string.app_name),
                ending.shareTitle + "\n" + ending.endingText,
                SHARE_WECHAT_FRIEND
            )
            GameApp.globalScope.launch(Dispatchers.Main) {
                gainShare1Award(repo.gameCore.getImageShareAwardNum())
            }
        }) {
        Column(
            modifier = Modifier
                .clip(RoundedCornerShape(padding4))
                .background(W10).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    modifier = Modifier
                        .size(imageMedium)
                        .scale(1.2f),
                    painter = painterResource(id = R.drawable.icon_share_wechat),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.size(padding6))
                Text(
                    text = stringResource(R.string.share_target2),
                    style = MaterialTheme.typography.h4
                )
            }
            if (!share1Gained()) {
                Spacer(modifier = Modifier.size(padding4))
                AwardList(
                    award = Award(key = repo.gameCore.getImageShareAwardNum()),
                    param = defaultParam.copy(
                        showName = false,
                        noFrameForItem = true,
                        numInFrame = false
                    ),
                    paddingHorizontalInDp = padding0,
                    paddingVerticalInDp = padding0
                )
                Spacer(modifier = Modifier.size(padding4))
                Text(
                    text = stringResource(R.string.share_platform_tips),
                    style = MaterialTheme.typography.h6
                )
            }
        }
    }

    EffectButton(onClick = {
        BillingManager.shareToWechat(
            GameApp.instance.getWrapString(R.string.app_name),
            ending.shareTitle + "\n" + ending.endingText,
            SHARE_WECHAT_CIRCLE
        )
        GameApp.globalScope.launch(Dispatchers.Main) {
            gainShare2Award(repo.gameCore.getImageShareAwardNum())
        }
    }) {
        Column(
            modifier = Modifier
                .clip(RoundedCornerShape(padding4))
                .background(W10).padding(padding4), horizontalAlignment = Alignment.CenterHorizontally
        ) {

            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    modifier = Modifier
                        .size(imageMedium)
                        .scale(1.2f),
                    painter = painterResource(id = R.drawable.icon_share_circle),
                    contentDescription = null
                )
                Spacer(modifier = Modifier.size(padding6))
                Text(
                    text = stringResource(R.string.share_target3),
                    style = MaterialTheme.typography.h4
                )
            }
            if (!ShareManager.share2Gained()) {
                Spacer(modifier = Modifier.size(padding4))
                AwardList(
                    award = Award(key = repo.gameCore.getImageShareAwardNum()),
                    param = defaultParam.copy(
                        showName = false,
                        noFrameForItem = true,
                        numInFrame = false
                    ),
                    paddingHorizontalInDp = padding0,
                    paddingVerticalInDp = padding0
                )
                Spacer(modifier = Modifier.size(padding4))
                Text(
                    text = stringResource(R.string.share_platform_tips),
                    style = MaterialTheme.typography.h6
                )
            }
        }
    }
}

package com.moyu.chuanqirensheng.sub.report

object ReportManager {

    fun init() {
    }

    fun onLogin() {
    }

    fun onAdCompletedAF(adId: String) {
    }

    fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {

    }

    fun pk(i: Int, value: Int) {

    }

    fun battle(i: Int, i1: Int, age: Int) {

    }

    fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {

    }

    fun onNewGame(i: Int) {

    }

    fun onContinueGame(i: Int) {

    }

    fun onReview(star: Int) {

    }

    fun onPurchaseCompletedAdjust(dollarPrice: Double, orderId: String) {}

}
package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.speed.GameSpeedManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.VideoPlayerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.turnEffect
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_MUSIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_MUTE_SOUND
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEWEST_VERSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.KEY_SPEED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.job.JobContent
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking


class DataStoreTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        runBlocking {
            // todo 非常奇怪的一个闪退，先暂时这么保护下
            // Reading a state that was created after the snapshot was taken or in a snapshot that has not yet been applied
            newTurnEffectState.value = turnEffect

            VideoPlayerManager.init()

            GameApp.newUser = getBooleanFlowByKey(KEY_NEW_USER, true)

            val newestVersion = getIntFlowByKey(KEY_NEWEST_VERSION, 0)
            if (newestVersion > getVersionCode() && !BuildConfig.FLAVOR.contains("Lite")) {
                context.getWrapString(R.string.backward_version).toast()
                delay(2000)
                error("数据兼容错误,请不要退版本")
            }
            setIntValueByKey(KEY_NEWEST_VERSION, getVersionCode())

            // 引导
            GuideManager.guideIndex.intValue = getIntFlowByKey(KEY_GUIDE_INDEX)

            GameApp.instance.initGameSdk()
            PrivacyManager.init()

            // 游戏速度和声音设置
            getIntFlowByKey(KEY_SPEED, 1).let { speed ->
                GameSpeedManager.setSpeed(speed)
            }

            MusicManager.muteMusic = getBooleanFlowByKey(KEY_MUTE_MUSIC)
            MusicManager.muteSound = getBooleanFlowByKey(KEY_MUTE_SOUND)
            MusicManager.doMuteState()

            // 游戏的初始化
            repo.doInit()
        }
    }
}
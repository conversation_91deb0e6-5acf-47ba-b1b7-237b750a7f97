package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.config.ConfigManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.job.JobContent
import kotlinx.coroutines.runBlocking

class ConfigTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
        runBlocking {
            ConfigManager.loadConfigs(repo.gameCore)
        }
    }
}
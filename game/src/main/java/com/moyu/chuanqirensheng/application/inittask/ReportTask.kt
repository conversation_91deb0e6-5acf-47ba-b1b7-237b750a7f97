package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.job.JobContent


class ReportTask : JobContent<GameApp> {
    override fun execute(context: GameApp) {
//        val config = InitConfig("20001508", GameApp.instance.resources.getString(
//            R.string.platform_channel))
//        config.uriConfig = UriConfig.createByDomain("https://gator.volces.com", null);
//        config.isAutoTrackEnabled = false
//        config.isOaidEnabled = false
//        config.isAndroidIdEnabled = false
//        config.isGaidEnabled = false
//        config.isMacEnable = false
//        config.isImeiEnable = false
//        config.isOaidEnabled = false
//        config.isLogEnable = false
//        config.setAutoStart(false)
//        AppLog.setEncryptAndCompress(true)
//        AppLog.init(context, config)
//        if (!Dialogs.showPrivacyDialog.value && !Dialogs.showPermissionDialog.value) {
//            AppLog.start()
//        }
    }
}
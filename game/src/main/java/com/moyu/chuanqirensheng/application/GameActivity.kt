package com.moyu.chuanqirensheng.application

import android.annotation.TargetApi
import android.app.LocaleManager
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import androidx.activity.OnBackPressedDispatcher
import androidx.activity.addCallback
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.material.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.fragment.app.FragmentActivity
import androidx.navigation.NavController
import androidx.navigation.NavDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs.alertDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.allyStarUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.awardDialog
import com.moyu.chuanqirensheng.application.Dialogs.badgeDialog
import com.moyu.chuanqirensheng.application.Dialogs.buffDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugAdvSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.debugSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.detailTalentDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawPoolDialog
import com.moyu.chuanqirensheng.application.Dialogs.drawResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.endingDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.endingDialog
import com.moyu.chuanqirensheng.application.Dialogs.equipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.errorOrderDialog
import com.moyu.chuanqirensheng.application.Dialogs.eventFailDialog
import com.moyu.chuanqirensheng.application.Dialogs.eventPassDialog
import com.moyu.chuanqirensheng.application.Dialogs.fatalEnemyDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameAllyDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameLoseDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameMasterDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameReviewDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameSkillDialog
import com.moyu.chuanqirensheng.application.Dialogs.gameWinDialog
import com.moyu.chuanqirensheng.application.Dialogs.giftDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.infoDialog
import com.moyu.chuanqirensheng.application.Dialogs.levelResultDialog
import com.moyu.chuanqirensheng.application.Dialogs.levelUpDialog
import com.moyu.chuanqirensheng.application.Dialogs.moneyTransferDialog
import com.moyu.chuanqirensheng.application.Dialogs.newQuestAwardDialog
import com.moyu.chuanqirensheng.application.Dialogs.payBlockingDialog
import com.moyu.chuanqirensheng.application.Dialogs.reputationRewardDialog
import com.moyu.chuanqirensheng.application.Dialogs.roleDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.selectAllyToBattleDialog
import com.moyu.chuanqirensheng.application.Dialogs.selectAllyToGameDialog
import com.moyu.chuanqirensheng.application.Dialogs.sellPoolDialog
import com.moyu.chuanqirensheng.application.Dialogs.settingDialog
import com.moyu.chuanqirensheng.application.Dialogs.shareCodeDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPermissionDialog
import com.moyu.chuanqirensheng.application.Dialogs.showPrivacyDialog
import com.moyu.chuanqirensheng.application.Dialogs.skillDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.skillLevelInfoDialog
import com.moyu.chuanqirensheng.application.Dialogs.skinDialog
import com.moyu.chuanqirensheng.application.Dialogs.statisticView
import com.moyu.chuanqirensheng.application.Dialogs.storyDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.storyDialog
import com.moyu.chuanqirensheng.application.Dialogs.tutorDialog
import com.moyu.chuanqirensheng.application.Dialogs.useSaveDialog
import com.moyu.chuanqirensheng.application.Dialogs.vipDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPass2UnlockDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPassDetailDialog
import com.moyu.chuanqirensheng.application.Dialogs.warPassUnlockDialog
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.debug.DebugAdvSkillDialog
import com.moyu.chuanqirensheng.debug.DebugBattleScreen
import com.moyu.chuanqirensheng.debug.DebugScreen
import com.moyu.chuanqirensheng.debug.DebugSkillDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePass2Screen
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.BattlePassScreen
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPass2UnlockDialog
import com.moyu.chuanqirensheng.feature.battlepass.ui.WarPassUnlockDialog
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.draw.ui.DrawAllScreen
import com.moyu.chuanqirensheng.feature.draw.ui.DrawPoolDialog
import com.moyu.chuanqirensheng.feature.draw.ui.DrawResultDialog
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.ending.ui.EndingDetailDialog
import com.moyu.chuanqirensheng.feature.ending.ui.EndingDialog
import com.moyu.chuanqirensheng.feature.ending.ui.EndingScreen
import com.moyu.chuanqirensheng.feature.gift.ui.GiftDetailDialog
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.ui.GuideMask
import com.moyu.chuanqirensheng.feature.illustration.ui.IllustrationScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.CheapLotteryScreen
import com.moyu.chuanqirensheng.feature.lottery.ui.ExpensiveLotteryScreen
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayAllScreen
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpBattleScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpChooseEnemyScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpEntryScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpQuestScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpRankScreen
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpSellScreen
import com.moyu.chuanqirensheng.feature.quest.ui.NewQuestAwardDialog
import com.moyu.chuanqirensheng.feature.quest.ui.NewQuestScreen
import com.moyu.chuanqirensheng.feature.quest.ui.QuestAllScreen
import com.moyu.chuanqirensheng.feature.rank.ui.FamousScreen
import com.moyu.chuanqirensheng.feature.rank.ui.RankScreen
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationAwardDialog
import com.moyu.chuanqirensheng.feature.reputation.ui.ReputationScreen
import com.moyu.chuanqirensheng.feature.router.ADVANCED_TUTOR_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS2_SCREEN
import com.moyu.chuanqirensheng.feature.router.BATTLE_PASS_SCREEN
import com.moyu.chuanqirensheng.feature.router.CREATE_GAME_SCREEN
import com.moyu.chuanqirensheng.feature.router.DEBUG_BATTLE
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.DRAW_SCREEN
import com.moyu.chuanqirensheng.feature.router.ENDING_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.FAMOUS_SCREEN
import com.moyu.chuanqirensheng.feature.router.FORTUNE_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.MORE_SCREEN
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PAGE_PARAM_TAB_INDEX
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.REPUTATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.SELL_SCREEN_PREFIX
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.SIGN_SCREEN
import com.moyu.chuanqirensheng.feature.router.SKILL_ILLUSTRATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.SKIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.STORY_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_SCREEN
import com.moyu.chuanqirensheng.feature.router.TCG_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_BATTLER_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.VIP_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.isCurrentRoute
import com.moyu.chuanqirensheng.feature.router.popTop
import com.moyu.chuanqirensheng.feature.sell.ui.SellAllScreen
import com.moyu.chuanqirensheng.feature.sell.ui.SellPoolDialog
import com.moyu.chuanqirensheng.feature.sign.ui.SignScreen
import com.moyu.chuanqirensheng.feature.skin.ui.SkinDetailDialog
import com.moyu.chuanqirensheng.feature.skin.ui.SkinsScreen
import com.moyu.chuanqirensheng.feature.story.ui.StoryDetailDialog
import com.moyu.chuanqirensheng.feature.story.ui.StoryDialog
import com.moyu.chuanqirensheng.feature.story.ui.StoryScreen
import com.moyu.chuanqirensheng.feature.talent.ui.TalentDetailDialog
import com.moyu.chuanqirensheng.feature.tcg.ui.TcgScreen
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.ui.TowerAllScreen
import com.moyu.chuanqirensheng.feature.tower.ui.TowerBattleScreen
import com.moyu.chuanqirensheng.feature.vip.ui.VipDialog
import com.moyu.chuanqirensheng.feature.vip.ui.VipScreen
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyDetailDialog
import com.moyu.chuanqirensheng.screen.ally.AllyStarUpDialog
import com.moyu.chuanqirensheng.screen.ally.GameAllyDialog
import com.moyu.chuanqirensheng.screen.ally.SelectAllyToBattleDialog
import com.moyu.chuanqirensheng.screen.ally.SelectAllyToGameDialog
import com.moyu.chuanqirensheng.screen.award.AwardDialog
import com.moyu.chuanqirensheng.screen.award.BadgeDialog
import com.moyu.chuanqirensheng.screen.battle.GameLoseDialog
import com.moyu.chuanqirensheng.screen.battle.GameWinDialog
import com.moyu.chuanqirensheng.screen.battle.StatisticDialog
import com.moyu.chuanqirensheng.screen.buff.BuffDetailDialog
import com.moyu.chuanqirensheng.screen.common.AppBackground
import com.moyu.chuanqirensheng.screen.common.GameSnackBar
import com.moyu.chuanqirensheng.screen.common.StatusBarMask
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.dialog.CommonAlertDialog
import com.moyu.chuanqirensheng.screen.equip.EquipDetailDialog
import com.moyu.chuanqirensheng.screen.event.EventDetailScreen
import com.moyu.chuanqirensheng.screen.event.EventFailDialog
import com.moyu.chuanqirensheng.screen.event.EventPassDialog
import com.moyu.chuanqirensheng.screen.event.EventSelectScreen
import com.moyu.chuanqirensheng.screen.event.FatalEnemyDialog
import com.moyu.chuanqirensheng.screen.fortune.OutAllyScreen
import com.moyu.chuanqirensheng.screen.info.InfoDialog
import com.moyu.chuanqirensheng.screen.life.TalentScreen
import com.moyu.chuanqirensheng.screen.login.CreateGameScreen
import com.moyu.chuanqirensheng.screen.login.LoginScreen
import com.moyu.chuanqirensheng.screen.more.MoreScreen
import com.moyu.chuanqirensheng.screen.resource.MoneyTransferDialog
import com.moyu.chuanqirensheng.screen.role.GameMasterDialog
import com.moyu.chuanqirensheng.screen.role.LevelResultDialog
import com.moyu.chuanqirensheng.screen.role.LevelUpDialog
import com.moyu.chuanqirensheng.screen.role.RoleDetailDialog
import com.moyu.chuanqirensheng.screen.setting.SettingDialog
import com.moyu.chuanqirensheng.screen.skill.GameSkillDialog
import com.moyu.chuanqirensheng.screen.skill.SkillDetailDialog
import com.moyu.chuanqirensheng.screen.skill.SkillLevelInfoDialog
import com.moyu.chuanqirensheng.screen.tutor.AdvancedTutorScreen
import com.moyu.chuanqirensheng.screen.tutor.TutorDialog
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.bill.GoogleActivityResultHandler
import com.moyu.chuanqirensheng.sub.bill.pay.ErrorOrderDialog
import com.moyu.chuanqirensheng.sub.bill.pay.PayBlockDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PERMISSION
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.loginsdk.ui.LoginResultDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PermissionAlertDialog
import com.moyu.chuanqirensheng.sub.loginsdk.ui.PrivacyAlertDialog
import com.moyu.chuanqirensheng.sub.review.ui.GameReviewDialog
import com.moyu.chuanqirensheng.sub.saver.ui.UseCloudSaverDialog
import com.moyu.chuanqirensheng.sub.share.ui.ShareCodeDialog
import com.moyu.chuanqirensheng.ui.theme.ComposedTheme
import com.moyu.chuanqirensheng.util.killSelf
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

class GameActivity : FragmentActivity() {

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(updateBaseContext(newBase))
    }

    fun updateBaseContext(context: Context?): Context {
        if (context == null) return context!!

        // 对于 Android 13+ (API 33+)，优先使用系统的 per-app language 设置
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            try {
                val localeManager = context.getSystemService(LocaleManager::class.java)
                val appLocales = localeManager?.applicationLocales
                if (appLocales != null && !appLocales.isEmpty) {
                    val locale = appLocales[0]
                    Locale.setDefault(locale)
                    return updateResourcesLocale(context, locale)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        // 回退到传统方法
        val lang = LanguageManager.selectedLanguage.value
        if (lang.isNotEmpty()) {
            val locale = Locale.forLanguageTag(lang)
            Locale.setDefault(locale)

            return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                updateResourcesLocale(context, locale)
            } else {
                updateResourcesLocaleLegacy(context, locale)
            }
        }

        return context
    }

    @TargetApi(Build.VERSION_CODES.N)
    private fun updateResourcesLocale(context: Context?, locale: Locale): Context {
        val configuration = context?.resources?.configuration
        configuration?.setLocale(locale)
        return context!!.createConfigurationContext(configuration!!)
    }

    private fun updateResourcesLocaleLegacy(context: Context?, locale: Locale): Context {
        val resources = context!!.resources
        val configuration = resources.configuration
        configuration.locale = locale
        resources.updateConfiguration(configuration, resources.displayMetrics)
        return context
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        GameApp.instance.activity = this
        setContent {
            val navController = rememberNavController()
            ComposedTheme {
                Scaffold { padding ->
                    val heathTipsVisible = remember {
                        mutableStateOf(true)
                    }
                    AppBackground()
                    if (!heathTipsVisible.value || GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                        RegisterScreens(navController, padding)
                        RegisterDialogs()
                        RegisterFullScreenEffect()
                        RegisterRouter(navController)
                        RegisterSnackBar()
                        RegisterBackCallback(onBackPressedDispatcher)
                    } else {
                        HeathTipsLayout(heathTipsVisible)
                    }
                    StatusBarMask()
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        BillingManager.queryAsync()
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        GoogleActivityResultHandler.onActivityResult(requestCode, resultCode, data)
    }
}

@Composable
fun RegisterFullScreenEffect() {
    GuideMask()
}

@Composable
fun RegisterScreens(
    navController: NavHostController,
    padding: PaddingValues,
) {
    NavHost(
        navController = navController,
        startDestination = LOGIN_SCREEN,
        modifier = Modifier.padding(padding),
    ) {
        composable(route = LOGIN_SCREEN) { LoginScreen() }
        composable(route = CREATE_GAME_SCREEN) { CreateGameScreen() }
        composable(route = FORTUNE_SCREEN) { OutAllyScreen() }
        composable(route = STORY_SCREEN) { StoryScreen() }
        composable(route = TALENT_SCREEN) { TalentScreen() }
        composable(route = SIGN_SCREEN) { SignScreen() }
        composable(route = SELL_SCREEN_PREFIX + "{${PAGE_PARAM_TAB_INDEX}}") {
            SellAllScreen(
                it.arguments?.getString(
                    PAGE_PARAM_TAB_INDEX
                )?.toInt() ?: 0
            )
        }
        composable(route = QUEST_SCREEN) { QuestAllScreen() }
        composable(route = RANK_SCREEN) { RankScreen() }
        composable(route = MORE_SCREEN) { MoreScreen() }
        composable(route = EVENT_SELECT_SCREEN) { EventSelectScreen() }
        composable(route = EVENT_DETAIL_SCREEN) { EventDetailScreen() }
        composable(route = VIP_SCREEN) { VipScreen() }
        composable(route = SKIN_SCREEN) { SkinsScreen() }
        composable(route = REPUTATION_SCREEN) { ReputationScreen() }
        composable(route = NEW_TASK_SCREEN) { NewQuestScreen() }
        composable(route = ENDING_SCREEN) { EndingScreen() }
        composable(route = TCG_SCREEN) { TcgScreen() }
        composable(route = SKILL_ILLUSTRATION_SCREEN) { IllustrationScreen() }
        composable(route = ADVANCED_TUTOR_SCREEN) { AdvancedTutorScreen() }
        composable(route = FAMOUS_SCREEN) { FamousScreen() }
        composable(route = DRAW_SCREEN) { DrawAllScreen() }
        composable(route = PVP_SCREEN) { PvpEntryScreen() }
        composable(route = PVP_SELL_SCREEN) { PvpSellScreen() }
        composable(route = PVP_CHOOSE_ENEMY_SCREEN) { PvpChooseEnemyScreen() }
        composable(route = PVP_RANK_SCREEN) { PvpRankScreen() }
        composable(route = PVP_QUEST_SCREEN) { PvpQuestScreen() }
        composable(route = PVP_BATTLE_SCREEN) { PvpBattleScreen() }
        composable(route = BATTLE_PASS_SCREEN) { BattlePassScreen() }
        composable(route = BATTLE_PASS2_SCREEN) { BattlePass2Screen() }
        composable(route = LOTTERY_SCREEN1) { CheapLotteryScreen() }
        composable(route = LOTTERY_SCREEN2) { ExpensiveLotteryScreen() }
        composable(route = SEVEN_DAY_SCREEN) { SevenDayAllScreen() }
        composable(route = TOWER_BATTLER_SCREEN) { TowerBattleScreen() }
        composable(route = TOWER_SCREEN) { TowerAllScreen() }
        composable(route = DEBUG_SCREEN) { DebugScreen() }
        composable(route = DEBUG_BATTLE) { DebugBattleScreen() }
    }
}

@Composable
fun RegisterDialogs() {
    ErrorOrderDialog(errorOrderDialog)

    VipDialog(vipDetailDialog)
    BattlePassDialog(warPassDetailDialog)
    WarPassUnlockDialog(warPassUnlockDialog)
    WarPass2UnlockDialog(warPass2UnlockDialog)
    SellPoolDialog(sellPoolDialog)
    ReputationAwardDialog(reputationRewardDialog)
    NewQuestAwardDialog(newQuestAwardDialog)
    RoleDetailDialog(roleDetailDialog)
    SkillDetailDialog(skillDetailDialog)
    EquipDetailDialog(equipDetailDialog)
    AllyDetailDialog(allyDetailDialog)
    AllyStarUpDialog(allyStarUpDialog)
    TalentDetailDialog(detailTalentDialog)
    GameWinDialog(gameWinDialog)
    GameLoseDialog(gameLoseDialog)
    StatisticDialog(statisticView)
    TutorDialog(tutorDialog)
    BuffDetailDialog(buffDetailDialog)
    InfoDialog(infoDialog)
    LevelUpDialog(levelUpDialog)
    LevelResultDialog(levelResultDialog)
    SkinDetailDialog(skinDialog)
    BadgeDialog(badgeDialog)
    GiftDetailDialog(giftDetailDialog)
    DrawResultDialog(drawResultDialog)
    DrawPoolDialog(drawPoolDialog)


    SelectAllyToGameDialog(selectAllyToGameDialog)
    SelectAllyToBattleDialog(selectAllyToBattleDialog)
    SettingDialog(settingDialog)
    ShareCodeDialog(shareCodeDialog)
    GameAllyDialog(gameAllyDialog)
    GameSkillDialog(gameSkillDialog)
    GameMasterDialog(gameMasterDialog)
    EventPassDialog(eventPassDialog)
    EventFailDialog(eventFailDialog)
    FatalEnemyDialog(fatalEnemyDialog)
    EndingDetailDialog(endingDetailDialog)
    EndingDialog(endingDialog)
    StoryDialog(storyDialog)
    StoryDetailDialog(storyDetailDialog)
    SkillLevelInfoDialog(skillLevelInfoDialog)
    PayBlockDialog(payBlockingDialog)
    ErrorOrderDialog(errorOrderDialog)
    GameReviewDialog(gameReviewDialog)

    DebugSkillDialog(debugSkillDialog)
    DebugAdvSkillDialog(debugAdvSkillDialog)
    CommonAlertDialog(alertDialog)
    LoginResultDialog()
    MoneyTransferDialog(moneyTransferDialog)
    AwardDialog(awardDialog)
    UseCloudSaverDialog(useSaveDialog)

    PrivacyAlertDialog(switch = showPrivacyDialog, quit = { killSelf() }, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PRIVACY, false)
        showPermissionDialog.value = true
    })
    PermissionAlertDialog(switch = showPermissionDialog, confirm = {
        setBooleanValueByKey(KEY_NEED_SHOW_PERMISSION, false)
        GameApp.instance.initSDK(GameApp.instance.activity)
        GameApp.globalScope.launch {
            delay(1500) //有时候会拉不起来登录，delay下其他操作
            BuglyTask().execute(GameApp.instance)
            TTRewardAdTask().execute(GameApp.instance)
            RootCheckerTask().execute(GameApp.instance)
            BillingTask().execute(GameApp.instance)
        }
    })
}

@Composable
fun RegisterRouter(navController: NavHostController) {
    LaunchedEffect(Unit) {
        GameApp.instance.navController = navController
        navController.addOnDestinationChangedListener { _: NavController, _: NavDestination, _: Bundle? ->
            GameApp.globalScope.launch {
                // todo 不delay的话，这里判定时候，页面还没有完成退出
                delay(100)
                playerMusicByScreen()
            }
        }
    }
}

@Composable
fun RegisterSnackBar() {
    GameSnackBar()
}

@Composable
fun RegisterBackCallback(onBackPressedDispatcher: OnBackPressedDispatcher) {
    LaunchedEffect(Unit) {
        onBackPressedDispatcher.addCallback(
            GameApp.instance.activity,
        ) {
            if (!GuideManager.canBack()) {
                GameApp.instance.getWrapString(R.string.guide_block_quit).toast() // 引导时候全局block返回按钮
            } else if (isCurrentRoute(PVP_BATTLE_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_pvp_title),
                    content = GameApp.instance.getWrapString(R.string.quit_pvp_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        PvpManager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                    }
                )
            } else if (repo.gameMode.value.isTowerMode() && isCurrentRoute(TOWER_BATTLER_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        TowerManager.failed(
                            emptyList(),
                            repo.battleRoles.values.mapNotNull { it })
                        goto(TOWER_SCREEN)
                    }
                )
            } else if (repo.inBattle.value) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_battle_title),
                    content = GameApp.instance.getWrapString(R.string.quit_battle_content),
                    onConfirm = {
                        val forceKill = repo.inBattle.value
                        repo.battle.value.terminate()
                        repo.inBattle.value = false
                        EventManager.doEventBattleResult(
                            EventManager.selectedEvent.value,
                            result = false,
                            forceQuit = true,
                            forceKill = forceKill,
                        )
                    }
                )
            } else if (isCurrentRoute(EVENT_DETAIL_SCREEN)) {
                alertDialog.value = CommonAlert(
                    title = GameApp.instance.getWrapString(R.string.quit_event_title),
                    content = GameApp.instance.getWrapString(R.string.quit_event_content),
                    onConfirm = {
                        EventManager.selectedEvent.value?.let {
                            EventManager.doEventResult(
                                it,
                                it.isMainLine != 1 && !it.isBattle()
                            )
                        }
                    }
                )
            } else if (isCurrentRoute(EVENT_SELECT_SCREEN)) {
                doQuitGame()
            } else {
                popTop()
            }
        }
    }
}


fun doQuitGame() {
    alertDialog.value = CommonAlert(
        title = GameApp.instance.getWrapString(R.string.quit_life_title),
        content = GameApp.instance.getWrapString(R.string.quit_life_content),
        confirmText = GameApp.instance.getWrapString(R.string.quit_life_title),
        cancelText = GameApp.instance.getWrapString(R.string.temp_save),
        onCancel = {
            repo.inGame.value = false
            goto(LOGIN_SCREEN)
        },
        onConfirm = {
            val story = EndingManager.saveEnding(
                BattleManager.you.value,
                EventManager.eventRecorder.usedEvents,
                false
            )
            EndingManager.ending(story)
            if (story == null) {
                // story为空，需要手动跳转
                repo.inGame.value = false
                goto(LOGIN_SCREEN)
            }
            ContinueManager.clearSave()
        }
    )
}

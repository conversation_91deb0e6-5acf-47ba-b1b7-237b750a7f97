package com.moyu.chuanqirensheng.screen.life

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.screen.common.TextLabel2
import com.moyu.chuanqirensheng.screen.resource.CurrentBadgePoint
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.imageLargeFrame
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.composeDp
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Award
import com.moyu.core.model.toAward
import kotlinx.coroutines.launch
import kotlin.math.max

val talentPositions = listOf(
    Triple(19.composeDp(), 40.composeDp(), 0.7f),
    Triple(287.composeDp(), 51.composeDp(), 0.73f),
    Triple(181.composeDp(), 74.composeDp(), 0.74f),
    Triple(81.composeDp(), 154.composeDp(), 0.77f),
    Triple(267.composeDp(), 182.composeDp(), 0.8f),
    Triple(147.composeDp(), 247.composeDp(), 0.82f),
    Triple(301.composeDp(), 294.composeDp(), 0.84f),
    Triple(25.composeDp(), 339.composeDp(), 0.86f),
    Triple(221.composeDp(), 371.composeDp(), 0.88f),
    Triple(107.composeDp(), 434.composeDp(), 0.9f),
    Triple(290.composeDp(), 483.composeDp(), 0.92f),
    Triple(11.composeDp(), 524.composeDp(), 0.94f),
    Triple(161.composeDp(), 574.composeDp(), 0.97f),
    Triple(280.composeDp(), 604.composeDp(), 0.97f),
    Triple(41.composeDp(), 660.composeDp(), 1f),
)


@Composable
fun TalentScreen() {
    val pagerState = rememberPagerState {
        TalentManager.getUnlockedPageSize()
    }
    val scope = rememberCoroutineScope()
    GameBackground(
        background = getImageResourceDrawable("talent_page_${pagerState.currentPage + 1}"),
        bgMask = B65,
        title = repo.gameCore.getTalentPool()
            .first { it.type == pagerState.currentPage + 1 }.mainName
    ) {
        HorizontalPager(
            modifier = Modifier
                .fillMaxSize(),
            state = pagerState,
        ) { page ->
            Box(modifier = Modifier.fillMaxSize()) {
                TalentPage(page + 1)
            }
        }
        if (pagerState.currentPage > 0) {
            EffectButton(modifier = Modifier.align(Alignment.CenterStart), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage - 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(id = R.drawable.common_arrow_left),
                    contentDescription = stringResource(
                        R.string.prev_page
                    )
                )
            }
        }
        if (pagerState.currentPage < pagerState.pageCount - 1) {
            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                scope.launch {
                    pagerState.animateScrollToPage(pagerState.currentPage + 1)
                }
            }) {
                Image(
                    modifier = Modifier.size(imageLargeFrame),
                    painter = painterResource(id = R.drawable.common_arrow_right),
                    contentDescription = stringResource(
                        R.string.next_page
                    )
                )
            }
        }
        Row(
            Modifier
                .fillMaxWidth()
                .background(W30)
        ) {
            TextLabel2(
                text = stringResource(
                    R.string.talent_total_level,
                    TalentManager.getTotalLevelByType(pagerState.currentPage + 1)
                )
            )
            Spacer(modifier = Modifier.weight(1f))
            CurrentDiamondPoint(
                modifier = Modifier
                    .padding(end = padding10),
                showPlus = true,
                showFrame = true
            )
            CurrentBadgePoint(
                modifier = Modifier
                    .padding(end = padding10),
                showFrame = true
            )
        }
    }
}

@Composable
fun TalentPage(page: Int) {
    val talentTypes = repo.gameCore.getTalentPool().filter { it.level == 1 && it.type == page }
    talentTypes.forEachIndexed { index, talentType ->
        Box(
            modifier = Modifier
                .padding(
                    start = talentPositions[index].first,
                    top = talentPositions[index].second
                )
                .scale(talentPositions[index].third)
        ) {
            SingleTalentTypeView(
                talentMainId = talentType.mainId,
                talentLevel = TalentManager.talents[talentType.mainId] ?: 0
            )
        }
    }
}


@Composable
fun SingleTalentTypeView(
    talentMainId: Int,
    talentLevel: Int,
    itemSize: ItemSize = ItemSize.LargePlus,
) {
    val talentIcon = repo.gameCore.getTalentPool().first { it.mainId == talentMainId }.icon
    val talentName = repo.gameCore.getTalentPool().first { it.mainId == talentMainId }.name
    Box(modifier = Modifier, contentAlignment = Alignment.Center) {
        Column(modifier = Modifier) {
            EffectButton(onClick = {
                Dialogs.detailTalentDialog.value = talentMainId
            }) {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .width(itemSize.frameSize).graphicsLayer {
                            translationX = padding10.toPx()
                            translationY = padding8.toPx()
                        }.scale(1.1f),
                    painter = painterResource(id = R.drawable.talent_base),
                    contentDescription = null
                )
                Image(
                    modifier = Modifier
                        .padding(start = padding8)
                        .size(itemSize.frameSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 10))
                        .graphicsLayer {
                            translationX = padding6.toPx()
                        },
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Crop,
                    painter = painterResource(id = getImageResourceDrawable(talentIcon)),
                    contentDescription = null
                )
            }
            Box {
                TextLabel(
                    text = talentName + if (talentLevel > 0) talentLevel else "",
                    labelSize = LabelSize.Medium
                ) {
                    Dialogs.detailTalentDialog.value = talentMainId
                }
                // todo 这一段特别啰嗦，用的是和TalentDetailDialog以及TalentStarUpView一样的逻辑，暂时没空优化
                val showTalentLevel = max(1, talentLevel)
                val showTalent = repo.gameCore.getTalentPool()
                    .first { it.level == showTalentLevel && it.mainId == talentMainId }
                val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
                val nextLevel = talentLevel + 1
                val scroll = repo.gameCore.getTalentPool().first { it.talentSkill == skill.id }
                val nextScroll = repo.gameCore.getTalentPool()
                    .firstOrNull { it.mainId == scroll.mainId && it.level == nextLevel }
                val award = nextScroll?.let {
                    (if (nextScroll.costPool == 0) Award() else repo.gameCore.getPoolById(nextScroll.costPool)
                        .toAward()) + Award(diamond = nextScroll.cost)
                } ?: Award()
                if (scroll.level != scroll.levelLimit && AwardManager.isAffordable(award)) {
                    Image(
                        modifier = Modifier.align(Alignment.CenterStart)
                            .height(padding30)
                            .graphicsLayer {
                                translationX = -padding16.toPx()
                            },
                        contentScale = ContentScale.FillHeight,
                        painter = painterResource(R.drawable.hero_starup),
                        contentDescription = null
                    )
                }
            }
        }
    }
}
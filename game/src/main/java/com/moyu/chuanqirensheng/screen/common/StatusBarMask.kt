package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import com.moyu.chuanqirensheng.ui.theme.B04
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp

@Composable
fun StatusBarMask() {
    Box(
        modifier = Modifier
            .height(immersionBarHeightInDp)
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(B50, B04)
                )
            )
    )
}
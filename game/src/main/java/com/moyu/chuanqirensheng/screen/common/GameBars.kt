package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.LayoutDirection
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding5


/**
 * 血条UI组件
 */
@Composable
fun HpBar(
    currentHp: Int,
    maxHp: Int,
    totalShield: Int = 0
) {
    val realMax = if (maxHp + totalShield == 0) 100 else maxHp + totalShield
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_blood_empty),
            contentDescription = null
        )
        val hpBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentHp.toFloat() / realMax,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier
                .fillMaxSize().padding(horizontal = padding2, vertical = padding1)
                .clip(GenericShape(hpBuilder))
                .clip(RoundedCornerShape(padding2)),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_blood_line),
            contentDescription = null
        )
        val shieldBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        size.width * currentHp.toFloat() / realMax,
                        0f,
                        size.width * (currentHp + totalShield).toFloat() / realMax,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier
                .fillMaxSize().padding(horizontal = padding2, vertical = padding1)
                .clip(GenericShape(shieldBuilder))
                .clip(RoundedCornerShape(padding2)),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_shield_line),
            contentDescription = null
        )
        Text(
            text = "$currentHp/$maxHp",
            style = MaterialTheme.typography.body1,
        )
    }
}

@Composable
fun CommonBar(
    modifier: Modifier = Modifier,
    currentValue: Int,
    maxValue: Int,
    emptyRes: Int = R.drawable.common_blood_empty,
    fullRes: Int = R.drawable.common_exp_line,
    showNum: Boolean = true,
    textColor: Color = Color.White,
    style: TextStyle = MaterialTheme.typography.h6,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
    ) {
        val expBuilder: Path.(size: Size, layoutDirection: LayoutDirection) -> Unit =
            { size: Size, _: LayoutDirection ->
                this.addRect(
                    Rect(
                        0f,
                        0f,
                        size.width * currentValue.toFloat() / maxValue,
                        size.height
                    )
                )
            }
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = emptyRes),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding5, vertical = padding3)
                .clip(GenericShape(expBuilder))
                .clip(RoundedCornerShape(padding2))
                .align(Alignment.CenterStart),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = fullRes),
            contentDescription = null
        )
        if (showNum) {
            Text(
                text = "$currentValue/$maxValue",
                style = style,
                color = textColor
            )
        }
    }
}

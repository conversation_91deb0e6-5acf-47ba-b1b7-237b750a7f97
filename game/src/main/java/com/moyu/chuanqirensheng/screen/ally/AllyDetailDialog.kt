package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getFrameDrawable
import com.moyu.chuanqirensheng.logic.skillTag
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.TitleLabel
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Ally
import com.moyu.core.model.getRaceTreeTypeName
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.isAllySpecial
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.model.skill.isHalo


@Composable
fun AllyDetailDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        LaunchedEffect(Unit) {
            BattleManager.setAllyUnNew(ally)
            GameIllustrationManager.unlockAlly(ally)
        }
        EmptyDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                val role = BattleManager.getRoleByAlly(ally)
                AllyPanel(ally = ally, role = role) {
                    Dialogs.allyDetailDialog.value = null
                }
                Spacer(modifier = Modifier.size(padding42))
                AllyPropView(newRole = role)
                if (!ally.inGame && !ally.peek) {
                    Spacer(modifier = Modifier.size(padding14))
                    AllyStarUpView(ally = ally)
                }
            }
        }
    }
}

@Composable
fun AllyPanel(modifier: Modifier = Modifier, ally: Ally, role: Role, onClose: () -> Unit) {
    PanelLayout(modifier, PanelSize.Normal, showClose = true, onClose = {
        onClose()
    }) {
        AllyDetailLayout(ally, role)
    }
}

@Composable
fun AllyDetailLayout(ally: Ally, role: Role) {
    val race = ally.getRace()
    Column(
        modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier
                        .size(padding130, padding150)
                        .padding(horizontal = padding4, vertical = padding6),
                    painter = painterResource(id = getImageResourceDrawable(race.pic)),
                    contentScale = ContentScale.Crop,
                    contentDescription = null
                )
                Image(
                    modifier = Modifier.size(padding130, padding150),
                    painter = painterResource(id = R.drawable.common_frame2),
                    contentDescription = null
                )
                Stars(
                    stars = ally.star,
                    starWidth = cardStarBigSize,
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = padding26)
                )
                Box(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = padding6)
                        .size(padding80, padding16)
                ) {
                    RoleHpWithAnim { role }
                }
            }
            Column(
                modifier = Modifier.height(padding150),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                Text(text = ally.name, style = MaterialTheme.typography.h1, color = Color.Black)
                Text(
                    text = ally.getRace().raceType.getRaceTreeTypeName(),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                role.getSkills().firstOrNull()?.skillTagIds?.forEach {
                    TitleLabel(
                        modifier = Modifier.size(padding130, padding48), text = it.skillTag()
                    )
                }
            }
        }
        FlowRow(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalArrangement = Arrangement.SpaceEvenly,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 2
        ) {
            role.getSkills().filterNot { it.isHalo() }.filterNot { it.isBattleTree() }
                .sortedBy { it.skillType }.forEach {
                    if (it.isAllySpecial()) {
                        SingleSkillView(
                            modifier = Modifier.weight(1f),
                            skill = it,
                            frame = ally.getFrameDrawable(),
                            itemSize = ItemSize.Large,
                            showName = true
                        )
                    } else {
                        SingleSkillView(
                            modifier = Modifier.weight(1f),
                            skill = it,
                            itemSize = ItemSize.Large,
                            showName = true
                        )
                    }
                }
        }
    }
}

@Composable
fun AllyPropView(modifier: Modifier = Modifier, newRole: Role, oldRole: Role? = null) {
    PanelLayout(modifier, PanelSize.Small) {
        FlowRow(
            modifier = Modifier.fillMaxSize(),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalArrangement = Arrangement.Center,
            overflow = FlowRowOverflow.Visible,
            maxItemsInEachRow = 2
        ) {
            newRole.getCurrentProperty().MainPropertyLine(
                originProperty = oldRole?.getCurrentProperty() ?: Property(),
                textStyle = MaterialTheme.typography.h4,
                showBoost = oldRole != null
            )
        }
    }
}

@Composable
fun AllyStarUpView(ally: Ally) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceAround
    ) {
        val maxStar = ally.star >= ally.starLimit
        val buttonText =
            if (maxStar) GameApp.instance.getWrapString(R.string.star_max) else GameApp.instance.getWrapString(
                R.string.star_up
            )
        val enabled =
            !ally.peek && ally.starUpNum != 0 && ally.num - 1 >= ally.starUpNum && !maxStar && AwardManager.diamond.value >= ally.starUpRes
        GameButton(text = buttonText,
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                repo.gameCore.getAllyPool()
                    .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }?.let {
                        Dialogs.allyStarUpDialog.value = ally
                    }
            })
    }
}

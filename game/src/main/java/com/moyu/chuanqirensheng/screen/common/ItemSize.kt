package com.moyu.chuanqirensheng.screen.common

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.ui.theme.imageHugeFrame
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding40
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding51
import com.moyu.chuanqirensheng.ui.theme.padding64
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding90

enum class ItemSize(val frameSize: Dp, val itemSize: Dp) {
    Small(imageMediumMinus, imageSmallPlus),
    Medium(imageLargePlus, padding40),
    MediumPlus(padding48, padding40),
    LargeMinus(padding66, padding45),
    Large(padding72, padding51),
    LargePlus(padding90, padding64),
    Huge(padding110, padding90),
    HugeTalent(imageHugeFrame, imageHugeLite),
}

@Composable
fun ItemSize.getTextStyle(): TextStyle {
    return when (this) {
        ItemSize.Small -> MaterialTheme.typography.h3
        ItemSize.Medium -> MaterialTheme.typography.h3
        ItemSize.MediumPlus -> MaterialTheme.typography.body1
        ItemSize.LargeMinus -> MaterialTheme.typography.h4
        ItemSize.Large -> MaterialTheme.typography.h3
        ItemSize.LargePlus -> MaterialTheme.typography.h2
        else -> MaterialTheme.typography.h2
    }
}
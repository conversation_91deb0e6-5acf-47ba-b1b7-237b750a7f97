package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.EventIdTag
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.logic.event.EventConditionLayout
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.logic.event.triggerEvent
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.logic.playToTips
import com.moyu.chuanqirensheng.logic.skill.toAnnotatedEventText
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.ClickExpandLayout
import com.moyu.chuanqirensheng.screen.effect.ShadowedView
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Event
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import com.wajahatkarim.flippable.FlipAnimationType
import com.wajahatkarim.flippable.Flippable
import com.wajahatkarim.flippable.FlippableController
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun SingleEventCard(cardSize: CardSize, event: Event) {
    Row(modifier = Modifier) {
        val flipControl = remember {
            mutableStateOf(FlippableController())
        }
        val visible = remember(event.id) {
            mutableStateOf(false)
        }
        LaunchedEffect(event.id) {
            visible.value = false
            flipControl.value.flipToFront()
            delay(400)
            visible.value = true
            delay(RANDOM.nextIntClosure(100, 700).toLong())
            flipControl.value.flipToBack()
            // todo 有时候翻不过去，再翻一次，保证翻过去了
            delay(400L)
            flipControl.value.flipToBack()
        }
        TopCardView(event, cardSize, flipControl.value, visible.value)
        BottomCardView(
            modifier = Modifier.zIndex(-99f), cardSize = cardSize, event, visible.value
        )
    }
}

@Composable
fun BottomCardView(modifier: Modifier, cardSize: CardSize, event: Event, visible: Boolean) {
    AnimatedVisibility(
        modifier = modifier,
        enter = fadeIn(animationSpec = TweenSpec(durationMillis = 2000)),
        exit = fadeOut(),
        visible = visible
    ) {
        Column(
            modifier = modifier
                .fillMaxWidth()
                .height(cardSize.height),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(cardSize.height / 1.6f),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.FillBounds,
                    painter = painterResource(id = R.drawable.common_frame_long),
                    contentDescription = null
                )
                Text(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = padding8, horizontal = padding10)
                        .verticalScroll(rememberScrollState()),
                    text = event.showText.toAnnotatedEventText(),
                    style = MaterialTheme.typography.h4
                )
            }

            val buttonText = if (event.condition == 0) {
                stringResource(id = R.string.do_select)
            } else ""
            Box(contentAlignment = Alignment.Center) {
                GameButton(
                    text = buttonText,
                    enabled = triggerEvent(event, false),
                    buttonStyle = ButtonStyle.Orange
                ) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        EventManager.selectEvent(event)
                        GuideManager.showGuide.value = false
                        if (GuideManager.guideIndex.intValue <= BATTLE_GUIDE_START) {
                            GuideManager.guideIndex.intValue = BATTLE_GUIDE_START
                            if (event.isBattle()) {
                                GuideManager.showGuide.value = true
                            }
                        }
                    }
                }
                if (event.condition != 0) {
                    EventConditionLayout(
                        event = event, itemSize = ItemSize.Small, textColor = Color.White
                    ) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            EventManager.selectEvent(event)
                            GuideManager.showGuide.value = false
                            if (GuideManager.guideIndex.intValue <= BATTLE_GUIDE_START) {
                                GuideManager.guideIndex.intValue = BATTLE_GUIDE_START
                                if (event.isBattle()) {
                                    GuideManager.showGuide.value = true
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun TopCardView(
    event: Event, cardSize: CardSize, flipControl: FlippableController, visible: Boolean
) {
    if (visible) {
        ShadowedView(cardSize = cardSize) {
            Flippable(
                modifier = Modifier.size(cardSize.width, cardSize.height),
                flipDurationMs = 400,
                flipOnTouch = false,
                flipController = flipControl,
                flipEnabled = true,
                frontSide = {
                    SingleCardBack(
                        modifier = Modifier.size(cardSize.width, cardSize.height),
                        drawableRes = R.drawable.card_back
                    )
                },
                backSide = {
                    ClickExpandLayout {
                        InnerCard(
                            cardSize = cardSize,
                            event = event,
                        )
                    }
                },
                contentAlignment = Alignment.TopCenter,
                onFlippedListener = {},
                autoFlip = false,
                flipAnimationType = FlipAnimationType.HORIZONTAL_CLOCKWISE
            )
        }
    } else {
        Spacer(modifier = Modifier.size(cardSize.width, cardSize.height))
    }
}

@Composable
fun SingleCardBack(modifier: Modifier, drawableRes: Int) {
    Image(
        modifier = modifier
            .fillMaxSize()
            .padding(top = padding10)
            .padding(padding3)
            .clip(RoundedCornerShape(padding6)),
        contentScale = ContentScale.Crop,
        colorFilter = ColorFilter.tint(
            B35, BlendMode.SrcAtop
        ),
        painter = painterResource(id = drawableRes),
        contentDescription = null
    )
    Image(
        modifier = Modifier
            .fillMaxSize()
            .padding(top = padding10),
        contentScale = ContentScale.FillBounds,
        painter = painterResource(id = R.drawable.common_frame2),
        contentDescription = null
    )
}

@Composable
fun InnerCard(cardSize: CardSize, event: Event) {
    Box(Modifier.size(cardSize.width, cardSize.height)) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = padding10)
                .padding(padding3)
                .clip(RoundedCornerShape(cardSize.getRadius())),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = getImageResourceDrawable(event.pic)),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = padding10),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_frame2),
            contentDescription = null
        )
        CardTitleView(
            Modifier.align(Alignment.TopCenter), event.name, cardSize, event.isMainLine
        )
        CardTypeView(
            Modifier
                .align(Alignment.BottomStart)
                .graphicsLayer {
                    translationX = -padding10.toPx()
                    translationY = padding10.toPx()
                }, cardSize, event.play
        )
        EventIdTag(modifier = Modifier.align(Alignment.Center), event, cardSize)
    }
}

@Composable
fun CardTypeView(modifier: Modifier, cardSize: CardSize, play: Int) {
    val playName = play.playToName()
    EffectButton(modifier = modifier, onClick = {
        Dialogs.alertDialog.value = CommonAlert(content = play.playToTips(), onlyConfirm = true)
    }) {
        Image(
            modifier = Modifier.size(cardSize.width / 3),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.dungeon_type_frame),
            contentDescription = null
        )
        Text(text = playName, style = cardSize.getTextStyle(), textAlign = TextAlign.Center)
    }
}

@Composable
fun CardTitleView(modifier: Modifier, title: String, cardSize: CardSize, mainLine: Int) {
    Box(
        modifier = modifier.fillMaxWidth(), contentAlignment = Alignment.TopCenter
    ) {
        Image(
            modifier = Modifier
                .fillMaxWidth()
                .scale(1.4f),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.common_name_frame3),
            contentDescription = null
        )
        Text(
            modifier = Modifier.padding(top = cardSize.height / 20f),
            text = title,
            style = cardSize.getTextStyle(),
            textAlign = TextAlign.Center,
            color = if (mainLine == 1) Color.Red else Color.White
        )
    }
}
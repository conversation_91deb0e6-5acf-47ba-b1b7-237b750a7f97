package com.moyu.chuanqirensheng.screen.more

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.router.ADVANCED_TUTOR_SCREEN
import com.moyu.chuanqirensheng.feature.router.ENDING_SCREEN
import com.moyu.chuanqirensheng.feature.router.FAMOUS_SCREEN
import com.moyu.chuanqirensheng.feature.router.RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.REPUTATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.SKILL_ILLUSTRATION_SCREEN
import com.moyu.chuanqirensheng.feature.router.SKIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.TCG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SHARE_CODE
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TCG
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

data class MoreItem(
    val name: String,
    val route: () -> Unit,
    val icon: String,
    val unlock: () -> Boolean = { true },
    val red: () -> Boolean = { false }
)

val moreItems = listOf(
    MoreItem(name = GameApp.instance.getWrapString(R.string.skin),
        route = { goto(SKIN_SCREEN) },
        icon = "skin_icon",
        red = {
            SkinManager.hasRed()
        }),
    MoreItem(name = GameApp.instance.getWrapString(R.string.reputation_title),
        route = { goto(REPUTATION_SCREEN) },
        icon = "reputation_icon",
        red = {
            ReputationManager.hasRed()
        }),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.life_record), route = {
            goto(ENDING_SCREEN)
        }, icon = "ending_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.rank_title),
        route = { goto(RANK_SCREEN) },
        icon = "rank_icon"
    ),
    MoreItem(name = GameApp.instance.getWrapString(R.string.tcg_title), icon = "tcg_icon", red = {
        TcgManager.hasRed()
    }, unlock = {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_TCG)
        UnlockManager.getUnlockedFlow(unlock)
    }, route = {
        goto(TCG_SCREEN)
    }),
    MoreItem(name = GameApp.instance.getWrapString(R.string.share_code),
        route = { Dialogs.shareCodeDialog.value = true },
        icon = "share_code_icon",
        unlock = {
            val unlock = repo.gameCore.getUnlockById(UNLOCK_SHARE_CODE)
            UnlockManager.getUnlockedFlow(unlock)
        }),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.tutor),
        route = { Dialogs.tutorDialog.value = true },
        icon = "tutor_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.skill_illustration),
        route = {
            goto(SKILL_ILLUSTRATION_SCREEN)
        },
        icon = "illustration_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.advanced_tutor),
        route = {
            goto(ADVANCED_TUTOR_SCREEN)
        },
        icon = "advanced_tutor_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.famous_rank),
        route = { goto(FAMOUS_SCREEN) },
        icon = "famous_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.setting),
        route = { Dialogs.settingDialog.value = true },
        icon = "setting_icon"
    ),
)

@Composable
fun MoreScreen() {
    GameBackground(title = stringResource(R.string.more)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(vertical = padding6)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(padding10))
            FlowRow(
                maxItemsInEachRow = 3,
                horizontalArrangement = Arrangement.spacedBy(padding22),
                overflow = FlowRowOverflow.Visible,
            ) {
                moreItems.filter { it.unlock() }.forEach {
                    OneItem(Modifier, it)
                }
            }
            val text = remember {
                mutableStateOf("")
            }
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = padding26)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    DecorateTextField(
                        modifier = Modifier
                            .weight(1f)
                            .height(
                                textFieldHeight
                            ), text.value, hintText = stringResource(id = R.string.input_hint)
                    ) {
                        text.value = it
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    GameButton(
                        enabled = text.value.isNotEmpty(),
                        onClick = {
                            AwardManager.doNetAward(text.value)
                        },
                        text = stringResource(R.string.exchange),
                        textColor = Color.White,
                        buttonStyle = ButtonStyle.Orange,
                        buttonSize = ButtonSize.Medium
                    )
                }
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    text = stringResource(R.string.contact1),
                    style = MaterialTheme.typography.h3
                )
                if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                    Row(modifier = Modifier.clickable {
                        val myClipboard =
                            GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                        val myClip = ClipData.newPlainText("text", "T15760456854")
                        myClipboard.setPrimaryClip(myClip)
                        ("微信: T15760456854" + GameApp.instance.getWrapString(R.string.copied)).toast()
                    }) {
                        Text(
                            text = "微信: ",
                            style = MaterialTheme.typography.h3
                        )
                        Text(
                            text = "T15760456854",
                            style = MaterialTheme.typography.h3,
                            textDecoration = TextDecoration.Underline,
                            color = RaceNameColor
                        )
                    }
                }
                Row(modifier = Modifier.clickable {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", "<EMAIL>")
                    myClipboard.setPrimaryClip(myClip)
                    ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                }) {
                    Text(
                        text = "email：",
                        style = MaterialTheme.typography.h3
                    )
                    Text(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                        textDecoration = TextDecoration.Underline,
                        color = RaceNameColor
                    )
                }
            }
            Spacer(modifier = Modifier.size(padding10))
        }
    }
}

@Composable
fun OneItem(modifier: Modifier, moreItem: MoreItem, size: ItemSize = ItemSize.LargePlus, callback: () -> Unit = {}) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box {
            IconView(
                itemSize = ItemSize.Huge,
                res = getImageResourceDrawable(moreItem.icon),
                frame = R.drawable.hero_frame2,
                name = moreItem.name,
                showName = false,
                clipShape = RoundedCornerShape(50)
            ) {
                callback()
                moreItem.route()
            }
            if (moreItem.red()) {
                Image(
                    modifier = Modifier
                        .size(imageSmall)
                        .align(Alignment.TopEnd),
                    painter = painterResource(id = R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
        Box(modifier = Modifier.graphicsLayer {
            translationY = -padding19.toPx()
        }, contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier
                    .width(ItemSize.Huge.frameSize)
                    .scale(1.5f),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.shop_name_frame),
                contentDescription = null
            )
            Text(modifier = Modifier.graphicsLayer {
                translationY = padding4.toPx()
            }, text = moreItem.name, style = size.getTextStyle(), textAlign = TextAlign.Center)
        }
    }
}

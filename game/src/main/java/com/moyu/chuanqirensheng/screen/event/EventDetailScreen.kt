package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.isBattle
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.screen.resource.CurrentGoldPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentResourcePoint
import com.moyu.chuanqirensheng.screen.role.MasterInfoView
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.B75
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.gapSmall
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun EventDetailScreen() {
    Box(Modifier.fillMaxSize().background(B50)) {
        Image(
            modifier = Modifier.fillMaxSize(),
            colorFilter = ColorFilter.tint(
                B75, BlendMode.SrcAtop
            ),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = getImageResourceDrawable(EventManager.selectedEvent.value?.bgPic?.takeIf { it != "0" }
                ?: "0")),
            contentDescription = null)
        Column(Modifier.fillMaxSize()) {
            if (EventManager.selectedEvent.value?.isBattle() == false) {
                MasterInfoView(
                    Modifier
                        .fillMaxWidth()
                        .height(eventTopLayoutHeight)
                )
                Box(modifier = Modifier.fillMaxWidth()) {
                    TextLabel(text = EventManager.getEventSelectTitle())
                    Row(modifier = Modifier.fillMaxWidth().graphicsLayer {
                        translationY = -padding3.toPx()
                    }) {
                        Spacer(modifier = Modifier.weight(1f))
                        CurrentGoldPoint()
                        CurrentResourcePoint()
                    }
                    EffectedSkillsRow(
                        Modifier
                            .align(Alignment.BottomEnd)
                            .padding(end = padding22)
                            .graphicsLayer {
                                translationY = padding19.toPx()
                            })
                }
            }
            EventManager.selectedEvent.value?.let {
                EventLayout(event = it) {
                    EventDetailLayout(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(bottom = gapSmall),
                        event = it
                    )
                }
            }
        }
    }
}
package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.padding0


@Composable
fun IconView(
    modifier: Modifier = Modifier,
    res: Int? = null,
    frame: Int? = R.drawable.item_quality_0,
    itemSize: ItemSize = ItemSize.LargePlus,
    resZIndex: Float = 0f,
    itemExtraPadding: Dp = padding0,
    clipShape: RoundedCornerShape = RoundedCornerShape(itemSize.itemSize / 12),
    name: String? = null,
    showName: Boolean = true,
    callback: () -> Unit = {}
) {
    EffectButton(modifier = modifier, onClick = {
        callback()
    }) {
        res?.let {
            Image(
                modifier = Modifier.zIndex(resZIndex)
                    .size(itemSize.itemSize)
                    .padding(itemExtraPadding)
                    .clip(clipShape),
                painter = painterResource(id = res),
                contentScale = ContentScale.Crop,
                contentDescription = name
            )
        }
        frame?.let {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(id = frame),
                contentDescription = null
            )
        }
        name?.let {
            if (showName) {
                Text(text = name, color = Color.Black, style = itemSize.getTextStyle())
            }
        }
    }
}
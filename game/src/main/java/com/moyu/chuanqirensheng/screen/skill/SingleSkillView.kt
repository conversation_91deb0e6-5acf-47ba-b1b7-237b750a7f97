package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.skill.getFrameDrawable
import com.moyu.chuanqirensheng.logic.skill.getTouchInfo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill


@Composable
fun SingleSkillView(
    modifier: Modifier = Modifier,
    skill: Skill,
    itemSize: ItemSize = ItemSize.Large,
    showName: Boolean = true,
    showNum: Boolean = false,
    textColor: Color = Color.Black,
    colorFilter: ColorFilter? = null,
    frame: Int? = null,
    locked: Boolean = false,
    hide: Boolean = false,
    showRed: Boolean = false,
    showStars: Boolean = true,
    imageClip: Shape = RoundedCornerShape(itemSize.frameSize / 12),
    callback: (Skill) -> Unit = { Dialogs.skillDetailDialog.value = skill }
) {
    LaunchedEffect(Unit) {
        if (!hide) {
            GameIllustrationManager.unlockSkill(skill)
        }
    }
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        EffectButton(onClick = {
            callback(skill)
        }) {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(frame ?: skill.getFrameDrawable()),
                contentDescription = null,
            )
            Image(
                modifier = Modifier
                    .size(itemSize.itemSize)
                    .clip(imageClip),
                contentScale = ContentScale.Crop,
                colorFilter = colorFilter,
                painter = painterResource(if (hide) R.drawable.common_question else getImageResourceDrawable(skill.icon)),
                contentDescription = skill.getTouchInfo(),
            )
            if (skill.extraInfo.isNotEmpty()) {
                Box(
                    modifier = Modifier
                        .background(B50)
                        .padding(padding4)) {
                    Text(text = skill.extraInfo, style = MaterialTheme.typography.body1)
                }
            }
            if (skill.canShowStar() && showStars) {
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter),
                    skill.level,
                    starWidth = itemSize.frameSize / 4.3f
                )
            }
            if (locked) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize / 2),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = stringResource(id = R.string.locked),
                )
            }
            if (showRed && skill.new) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
        if (showName) {
            Spacer(modifier = Modifier.size(padding2))
            val text = if (showNum) skill.name + "x${skill.num}" else skill.name
            Text(
                text = if (hide) "???" else text,
                style = itemSize.getTextStyle(),
                maxLines = 2,
                minLines = 1,
                textAlign = TextAlign.Center,
                color = textColor
            )
        }
    }
}
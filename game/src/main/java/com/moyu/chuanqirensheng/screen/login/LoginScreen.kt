package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugButton
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.draw.ui.DrawIcon
import com.moyu.chuanqirensheng.feature.guide.BATTLE_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.EVENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.guide.TALENT_GUIDE_START
import com.moyu.chuanqirensheng.feature.lottery.ui.LotteryIcon
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayIcon
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.tower.ui.TowerIcon
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TALENT
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.antiaddict.AntiAddictDialog
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_NOT_ENOUGH
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.privacy.PrivacyManager
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding300
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight


@Composable
fun LoginScreen() {
    LaunchedEffect(Unit) {
        repo.inGame.value = false
        setBooleanValueByKey(KEY_NEW_USER, false)
        // 海外版本没有健康页面，所以这里还是要有一次初始化，里面有去重
        if (!PrivacyManager.privacyNeedShow && !PrivacyManager.permissionNeedShow) {
            GameApp.instance.initSDK(GameApp.instance.activity)
        }
        // 保证能及时刷新
        QuestManager.createTasks()

        // 只要回到首页，就清除局内死亡次数（用来做局内触发礼包）
        setIntValueByKey(KEY_DIED_IN_GAME, 0)
        setIntValueByKey(KEY_KEY_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_DIAMOND_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_PVP_DIAMOND_NOT_ENOUGH, 0)
        setIntValueByKey(KEY_DIED_IN_PVP, 0)
    }
    LaunchedEffect(GuideManager.guideIndex.intValue) {
        // 学习天赋引导
        if (UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(UNLOCK_MENU_TALENT))) {
            //经营技巧解锁了才显示
            if (GuideManager.guideIndex.intValue == TALENT_GUIDE_START) {
                GuideManager.showGuide.value = true
                // 显示引导后，要标记引导已经结束，免得后续中间引导冒出来
                setIntValueByKey(KEY_GUIDE_INDEX, 999)
            }
        }
    }
    GameBackground(
        showCloseIcon = false,
        showPreviewIcon = false,
    ) {
        Column(Modifier.fillMaxSize()) {
            Spacer(modifier = Modifier.size(userHeadHeight))
            GameLogo(
                Modifier
                    .align(Alignment.CenterHorizontally)
            )
            Spacer(modifier = Modifier.weight(1f))
            StartAndContinueGame(Modifier.align(Alignment.CenterHorizontally))
            Spacer(modifier = Modifier.weight(1f))
            Box(
                Modifier
                    .fillMaxWidth()
                    .heightIn(padding0, padding300)
            ) {
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .padding(end = padding6),
                    verticalArrangement = spacedBy(padding6)
                ) {
                    SevenDayIcon()
                    NewTaskIcon()
                    SignIcon()
                    BattlePassIcon()
                    BattlePass2Icon()
                    MoreIcon()
                    ErrorOrderIcon()
                }
                Column(
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .heightIn(padding0, padding360)
                        .padding(start = padding6)
                        .verticalScroll(rememberScrollState()),
                    verticalArrangement = spacedBy(padding6)
                ) {
                    FirstChargeIcon()
                    LotteryIcon()
                    DrawIcon()
                    PvpIcon()
                    TowerIcon()
                }
            }
            Spacer(modifier = Modifier.weight(1f))
            BottomItems()
            Spacer(modifier = Modifier.size(padding10))
        }
        Box(Modifier.fillMaxWidth()) {
            TopItemsLeft(Modifier.align(Alignment.TopStart))
            TopItemsRight(Modifier.align(Alignment.TopEnd))
        }
        DebugButton(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = padding120)
        )
    }
    AntiAddictDialog {
        if (GameApp.instance.hasLogin()) {
            GameApp.instance.checkAntiAddiction(GameApp.instance.activity)
        } else {
            GameApp.instance.login(GameApp.instance.activity)
        }
    }
}

@Composable
fun StartAndContinueGame(modifier: Modifier) {
    Column(modifier = modifier) {
        GameButton(
            buttonSize = ButtonSize.Huge,
            buttonStyle = ButtonStyle.Orange,
            text = stringResource(id = R.string.start_game),
        ) {
            if (GuideManager.guideIndex.intValue < EVENT_GUIDE_START) {
                GuideManager.guideIndex.intValue = EVENT_GUIDE_START
            }
            repo.clickStart()
        }
        Spacer(modifier = Modifier.size(padding12))
        if (ContinueManager.haveSaver()) {
            if (GuideManager.guideIndex.intValue == 0) {
                GuideManager.showGuide.value = false
                GuideManager.guideIndex.intValue = BATTLE_GUIDE_START
                setIntValueByKey(KEY_GUIDE_INDEX, BATTLE_GUIDE_START)
            }
            GameButton(
                buttonStyle = ButtonStyle.Blue,
                buttonSize = ButtonSize.Huge,
                text = stringResource(id = R.string.continue_game),
            ) {
                ContinueManager.recreateGame()
                repo.continueGame()
            }
        }
    }
}

package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.skill.getBattleTreeLevelInfo
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.logic.skillTag
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.TitleLabel
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.model.skill.isBinFuTree


@Composable
fun SkillDetailDialog(show: MutableState<Skill?>) {
    show.value?.let { skill ->
        LaunchedEffect(Unit) {
            BattleManager.setSkillUnNew(skill)
            GameIllustrationManager.unlockSkill(skill)
        }
        PanelDialog(onDismissRequest = { show.value = null }, contentBelow = {
            if (skill.canShowStar()) {
                Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.CenterEnd) {
                    SkillLevelButton(Modifier.padding(end = padding36), skill)
                }
            }
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = skill.name, style = MaterialTheme.typography.h1, color = Color.Black)
                if (skill.isBattleTree() || skill.isBinFuTree()) {
                    Spacer(modifier = Modifier.size(padding4))
                    Text(text = skill.getBattleTreeLevelInfo(), style = MaterialTheme.typography.h3, color = Color.Red)
                }
                if (skill.canShowStar()) {
                    Spacer(modifier = Modifier.size(padding4))
                    Stars(stars = skill.level, starWidth = cardStarBigSize)
                }
                Spacer(modifier = Modifier.size(padding4))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly
                ) {
                    skill.skillTagIds.forEach {
                        TitleLabel(
                            modifier = Modifier.size(padding130, padding48),
                            text = it.skillTag()
                        )
                    }
                }
                Spacer(modifier = Modifier.size(padding16))
                Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                    SkillImageView(skill)
                    Text(
                        modifier = Modifier.width(padding150),
                        text = skill.getRealDescColorful(),
                        style = MaterialTheme.typography.h4,
                        color = Color.Black
                    )
                }
                Spacer(modifier = Modifier.size(padding26))
                skill.getAllEnhancements().forEach {
                    Text(text = it.desc(), style = MaterialTheme.typography.h4, color = Color.Red)
                }
            }
        }
    }
}

@Composable
fun SkillImageView(skill: Skill) {
    Box(contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier
                .size(padding130, padding150)
                .padding(horizontal = padding4, vertical = padding6),
            painter = painterResource(id = getImageResourceDrawable(skill.icon)),
            contentScale = ContentScale.Crop,
            contentDescription = null
        )
        Image(
            modifier = Modifier.size(padding130, padding150),
            painter = painterResource(id = R.drawable.common_frame2),
            contentDescription = null
        )
    }
}

@Composable
fun SkillLevelButton(modifier: Modifier, skill: Skill) {
    EffectButton(modifier = modifier, onClick = {
        Dialogs.skillLevelInfoDialog.value = skill
    }) {
        Image(
            modifier = Modifier.size(padding60),
            painter = painterResource(id = R.drawable.menu_info),
            contentDescription = stringResource(id = R.string.level_info)
        )
    }
}
package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.NEW_TASK_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_NEW_TASK
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2

@Composable
fun NewTaskIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_NEW_TASK)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Box(modifier = modifier.graphicsLayer {
            translationX = LabelSize.Large.width.toPx() / 2.2f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                text = stringResource(R.string.new_quest),
                icon = R.drawable.icon_new_task
            ) {
                goto(NEW_TASK_SCREEN)
            }
            if (QuestManager.newTasks.any {
                    QuestManager.getTaskDoneFlow(it) && !it.opened
                } || MissionManager.hasRed()) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(end = padding10, top = padding2)
                        .size(imageTinyPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}

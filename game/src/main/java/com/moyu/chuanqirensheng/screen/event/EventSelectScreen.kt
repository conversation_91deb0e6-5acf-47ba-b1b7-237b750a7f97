package com.moyu.chuanqirensheng.screen.event

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.SizeTransform
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.EventDebugButton
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.skill.getFrameDrawable
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.screen.resource.CurrentGoldPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentResourcePoint
import com.moyu.chuanqirensheng.screen.role.MasterInfoView
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.addAnimationVertical
import com.moyu.chuanqirensheng.util.getImageResourceDrawable

@Composable
fun EventSelectScreen() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(B50)
    ) { // 左侧空出一些，避开setting
        Box {
            MasterInfoView(
                Modifier
                    .fillMaxWidth()
                    .height(eventTopLayoutHeight)
            )
            EventDebugButton(
                Modifier
                    .padding(top = padding48)
            )
        }

        Box(modifier = Modifier.fillMaxWidth()) {
            TextLabel(text = EventManager.getEventSelectTitle())
            Row(modifier = Modifier
                .fillMaxWidth()
                .graphicsLayer {
                    translationY = -padding3.toPx()
                }) {
                Spacer(modifier = Modifier.weight(1f))
                CurrentGoldPoint()
                CurrentResourcePoint()
            }
            EffectedSkillsRow(
                Modifier
                    .align(Alignment.BottomEnd)
                    .padding(end = padding22)
                    .graphicsLayer {
                        translationY = padding19.toPx()
                    })
        }
        EventSelectPage(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        )
    }
}

@Composable
fun EffectedSkillsRow(modifier: Modifier) {
    if (BattleManager.effectedSkills.isNotEmpty()) {
        EffectButton(modifier = modifier, onClick = {
            Dialogs.gameMasterDialog.intValue = 2
        }) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Row(verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(-padding8)) {
                    BattleManager.effectedSkills.takeLast(5).forEach {
                        IconView(
                            res = getImageResourceDrawable(it.icon),
                            frame = it.getFrameDrawable(),
                            itemSize = ItemSize.Small,
                            resZIndex = 99f,
                        ) {
                            Dialogs.gameMasterDialog.intValue = 2
                        }
                    }
                }
                AnimatedContent(
                    targetState = BattleManager.effectedSkills.size,
                    transitionSpec = {
                        addAnimationVertical(duration = 600).using(
                            SizeTransform(clip = true)
                        )
                    },
                    label = ""
                ) { target ->
                    Text(
                        text = stringResource(
                            R.string.effected_skill_num,
                            target
                        ),
                        style = MaterialTheme.typography.h5
                    )
                }
            }
        }
    }
}

package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.PagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.tabButtonHeight
import com.moyu.chuanqirensheng.ui.theme.tabButtonWidth
import kotlinx.coroutines.launch

@Composable
fun NavigationTab(
    modifier: Modifier = Modifier,
    pageState: PagerState,
    titles: List<String>,
    redIcons: List<Boolean> = listOf(false, false, false, false, false, false)
) {
    val scope = rememberCoroutineScope()
    Row(
        modifier = modifier
            .fillMaxWidth().padding(horizontal = padding16)
            .zIndex(999f),
        horizontalArrangement = if (titles.size >= 5) Arrangement.SpaceEvenly else Arrangement.spacedBy(
            padding4
        )
    ) {
        titles.forEachIndexed { index, title ->
            EffectButton(onClick = {
                scope.launch {
                    pageState.animateScrollToPage(index)
                }
            }) {
                TagView(
                    modifier = Modifier
                        .size(tabButtonWidth, tabButtonHeight),
                    title,
                    selected = index == pageState.currentPage,
                    redIcons[index]
                )
            }
        }
    }
}
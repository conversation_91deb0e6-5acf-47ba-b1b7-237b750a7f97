package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.foundation.layout.Box
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow


@Composable
fun StrokedText(
    modifier: Modifier = Modifier,
    text: AnnotatedString,
    color: Color = Color.White,
    style: TextStyle,
    strokeColor: Color = Color.Black,
    strokeWidth: Float = if (style == MaterialTheme.typography.h1) 1f else if (style == MaterialTheme.typography.h2) 1f else 1f, // 描边宽度
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Text(
            text = text,
            color = color,
            style = style,
            textAlign = textAlign,
            maxLines = maxLines,
            minLines = minLines,
            overflow = overflow,
            softWrap = softWrap
        )
        // 第二个文本：只负责描边，忽略 AnnotatedString 中的颜色
        val plainText = remember(text) {
            // 根据需要生成不带颜色 SpanStyle 的副本
            // 比如用 text.copy() 后把所有 SpanStyle 的 color 去掉
            removeColorStyles(text)
        }
        Text(
            text = plainText,
            color = strokeColor,
            style = style.copy(
                drawStyle = Stroke(
                    miter = strokeWidth, width = strokeWidth
                ),
            ),
            textAlign = textAlign,
            maxLines = maxLines,
            minLines = minLines,
            overflow = overflow,
            softWrap = softWrap
        )
    }
}


@Composable
fun StrokedText(
    modifier: Modifier = Modifier,
    text: String,
    color: Color = Color.White,
    style: TextStyle,
    strokeColor: Color = Color.Black,
    strokeWidth: Float = if (style == MaterialTheme.typography.h1) 3f else if (style == MaterialTheme.typography.h2) 2f else 1f, // 描边宽度
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    textAlign: TextAlign? = null,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
) {
    Box(
        modifier = modifier, contentAlignment = Alignment.Center
    ) {
        Text(
            text = text,
            color = color,
            style = style,
            textAlign = textAlign,
            maxLines = maxLines,
            minLines = minLines,
            overflow = overflow,
            softWrap = softWrap
        )
        Text(
            text = text,
            color = strokeColor,
            style = style.copy(
                drawStyle = Stroke(
                    miter = strokeWidth, width = strokeWidth
                )
            ),
            textAlign = textAlign,
            maxLines = maxLines,
            minLines = minLines,
            overflow = overflow,
            softWrap = softWrap
        )
    }
}

// 去除AnnotatedString中SpanStyle的color的一个简单示例
fun removeColorStyles(annotatedString: AnnotatedString): AnnotatedString {
    val builder = AnnotatedString.Builder()
    builder.append(annotatedString.text)
    // 拷贝 span 样式，但去除 color 信息
    annotatedString.spanStyles.forEach { rangeStyle ->
        val oldStyle = rangeStyle.item
        // 只保留除了 color 之外的其他属性
        val newSpanStyle = oldStyle.copy(color = Color.Unspecified)
        builder.addStyle(
            style = newSpanStyle,
            start = rangeStyle.start,
            end = rangeStyle.end
        )
    }
    // 同理对 paragraphStyles 进行拷贝
    annotatedString.paragraphStyles.forEach { paraStyle ->
        builder.addStyle(
            style = paraStyle.item,
            start = paraStyle.start,
            end = paraStyle.end
        )
    }
    return builder.toAnnotatedString()
}
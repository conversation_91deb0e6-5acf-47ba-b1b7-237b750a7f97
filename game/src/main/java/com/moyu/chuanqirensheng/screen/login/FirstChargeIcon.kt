package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.layout.Box
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.millisToHoursMinutesSeconds
import com.moyu.core.model.Gift
import kotlinx.coroutines.delay


@Composable
fun FirstChargeIcon(modifier: Modifier = Modifier) {
    val refreshIndex = remember {
        mutableIntStateOf(0)
    }
    // todo 为了在计时结束触发刷新
    if (isNetTimeValid() && refreshIndex.intValue >= 0) {
        val gifts = remember {
            mutableStateOf(emptyList<Gift>())
        }
        val showGift = remember(gifts.value) {
            derivedStateOf {
                gifts.value.firstOrNull { !it.dialogShowed }
            }
        }
        LaunchedEffect(
            refreshIndex.intValue,
            AwardManager.electric.value,
            AwardManager.adNum.value
        ) {
            gifts.value = GiftManager.getDisplayGifts()
        }
        gifts.value.firstOrNull()?.let {
            Box(modifier = modifier.graphicsLayer {
                translationX = -LabelSize.Large.width.toPx() / 2.2f
            }, contentAlignment = Alignment.Center) {
                TextLabel(
                    modifier = Modifier.scale(1.1f),
                    labelSize = LabelSize.Large,
                    flip = true,
                    text = stringResource(R.string.check_gift),
                    forceTextStyle = if (stringResource(R.string.check_gift).length <= 4) MaterialTheme.typography.h3 else MaterialTheme.typography.h5,
                    icon = getImageResourceDrawable(it.icon)
                ) {
                    Dialogs.giftDetailDialog.value = it
                }
                if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                    Box(Modifier.graphicsLayer {
                        translationY = padding16.toPx()
                        translationX = padding45.toPx()
                    }) {
                        UpdateTimeText(it) {
                            refreshIndex.intValue += 1
                        }
                    }
                }
            }
        }
        showGift.value?.let {
            Dialogs.giftDetailDialog.value = it
        }
    }
}

@Composable
fun UpdateTimeText(gift: Gift, callback: () -> Unit) {
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                if (currentTime.longValue >= gift.displayTime + gift.limitTime * 60 * 1000L) {
                    callback()
                    break
                }
                delay(500)
            }
        }
    }
    if (currentTime.longValue < gift.displayTime + gift.limitTime * 60 * 1000L) {
        Text(
            text = ((gift.displayTime + gift.limitTime * 60 * 1000L) - currentTime.longValue).millisToHoursMinutesSeconds(),
            style = MaterialTheme.typography.h6,
            color = SkillLevel5Color
        )
    }
}

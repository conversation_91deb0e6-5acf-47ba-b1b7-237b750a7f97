package com.moyu.chuanqirensheng.screen.filter

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.SkillStoryColor
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.toQualityColor
import com.moyu.core.model.Ally
import com.moyu.core.model.Event


data class ItemOrder<T, R : Comparable<R>>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val order: ((T)-> R)? = null,
)

val allyOrderList = listOf<ItemOrder<Ally, String>>(
    ItemOrder(GameApp.instance.getWrapString(R.string.none), Color.White, listOf(1)),
    ItemOrder(GameApp.instance.getWrapString(R.string.name), Color.White, listOf(1)) {
        it.name
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.star_level), Color.White, listOf(1)) {
        it.star.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.quality), Color.White, listOf(1)) {
        it.quality.toString()
    },
    ItemOrder(GameApp.instance.getWrapString(R.string.play_name16), Color.White, listOf(1)) {
        it.getRace().getRaceCountryIndex().toString()
    },
)

data class ItemFilter<T>(
    val name: String,
    val color: Color,
    val excludeTypes: List<Int>,
    val filter: (T) -> Boolean
)

val allyFilterList = listOf<ItemFilter<Ally>>(
    ItemFilter(GameApp.instance.getWrapString(R.string.all), Color.White, listOf(1, 2, 3)) { true },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race1),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 1 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race2),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 2 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race3),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 3 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race4),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 4 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race5),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 5 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race6),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 6 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race7),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 7 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race8),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 8 },
    ItemFilter(
        GameApp.instance.getWrapString(com.moyu.core.R.string.race9),
        Color.White,
        listOf(1)
    ) { card -> repo.gameCore.getRaceById(card.id).raceType == 9 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_max), 3.toQualityColor(), listOf(2)) { card -> card.quality == 3 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_high), 2.toQualityColor(), listOf(2)) { card -> card.quality == 2 },
    ItemFilter(GameApp.instance.getWrapString(R.string.quality_normal), 1.toQualityColor(), listOf(2)) { card -> card.quality == 1 },
    ItemFilter("0" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 0 },
    ItemFilter("1" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 1 },
    ItemFilter("2" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 2 },
    ItemFilter("3" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 3 },
    ItemFilter("4" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 4 },
    ItemFilter("5" + GameApp.instance.getWrapString(R.string.star), SkillStoryColor, listOf(3)) { card -> card.star == 5 },
    ItemFilter(ReputationManager.reputations[0], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 0 },
    ItemFilter(ReputationManager.reputations[1], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 1 },
    ItemFilter(ReputationManager.reputations[2], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 2 },
    ItemFilter(ReputationManager.reputations[3], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 3 },
    ItemFilter(ReputationManager.reputations[4], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 4 },
    ItemFilter(ReputationManager.reputations[5], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 5 },
    ItemFilter(ReputationManager.reputations[6], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 6 },
    ItemFilter(ReputationManager.reputations[7], Color.Blue, listOf(4)) { card -> card.getRace().getRaceCountryIndex() == 7 },
)

val eventFilterList = listOf<ItemFilter<Event>>(
    ItemFilter("全部", Color.White, listOf(1, 2, 3, 4)) { true },
    ItemFilter("剧情对白", Color.White, listOf(1)) { card -> card.play == 1 },
    ItemFilter("招募武将", Color.White, listOf(1)) { card -> card.play == 2 },
    ItemFilter("离间武将", Color.White, listOf(1)) { card -> card.play == 3 },
    ItemFilter("战技修炼", Color.White, listOf(1)) { card -> card.play == 4 },
    ItemFilter("附魔", Color.White, listOf(1)) { card -> card.play == 5 },
    ItemFilter("兵种演习", Color.White, listOf(1)) { card -> card.play == 7 },
    ItemFilter("铁匠铺", Color.White, listOf(1)) { card -> card.play == 9 },
    ItemFilter("紫薇星宿", Color.White, listOf(1)) { card -> card.play == 10 },
    ItemFilter("锦囊妙计", Color.White, listOf(1)) { card -> card.play == 11 },
    ItemFilter("获授兵符", Color.White, listOf(1)) { card -> card.play == 12 },
    ItemFilter("颁布政令", Color.White, listOf(1)) { card -> card.play == 13 },
    ItemFilter("城市设施", Color.White, listOf(1)) { card -> card.play == 14 },
    ItemFilter("奇遇", Color.White, listOf(1)) { card -> card.play == 15 },
    ItemFilter("阵营求援", Color.White, listOf(1)) { card -> card.play == 16 },
    ItemFilter("野战", Color.Green, listOf(1)) { card -> card.play == 21 },
    ItemFilter("攻城战", Color.Green, listOf(1)) { card -> card.play == 22 },
    ItemFilter("守城战", Color.Green, listOf(1)) { card -> card.play == 23 },
    ItemFilter("武将单挑", Color.Green, listOf(1)) { card -> card.play == 24 },
    ItemFilter("增援战", Color.Green, listOf(1)) { card -> card.play == 25 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
    ItemFilter("仙人试炼", Color.Green, listOf(1)) { card -> card.play == 26 },
)

@Composable
fun CommonFilterView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(padding60),
            painter = painterResource(id = R.drawable.menu_filter),
            contentDescription = stringResource(R.string.filter)
        )
    }
}

@Composable
fun CommonOrderView(modifier: Modifier = Modifier, showFilter: MutableState<Boolean>) {
    EffectButton(
        modifier = modifier, onClick = {
            showFilter.value = !showFilter.value
        }) {
        Image(
            modifier = Modifier.size(padding60),
            painter = painterResource(id = R.drawable.menu_rank),
            contentDescription = stringResource(R.string.order)
        )
    }
}


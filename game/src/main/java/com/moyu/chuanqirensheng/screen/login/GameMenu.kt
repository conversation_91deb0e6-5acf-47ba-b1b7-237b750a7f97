package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.FORTUNE_SCREEN
import com.moyu.chuanqirensheng.feature.router.QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.TALENT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.router.gotoSellWithTabIndex
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_ALLY
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_SELL
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TALENT
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_MENU_TASK
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.canStarUp
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.bottomItemSize
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.Unlock

@Composable
fun BottomItems() {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_hero,
            text = stringResource(R.string.menu1),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_ALLY),
            redIcon = repo.allyManager.data.any { it.new } || repo.allyManager.data.any { it.canStarUp() }
        ) {
            goto(FORTUNE_SCREEN)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_talent,
            text = stringResource(R.string.menu2),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_TALENT),
            redIcon = TalentManager.hasRed()
        ) {
            goto(TALENT_SCREEN)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_shop,
            text = stringResource(R.string.menu3),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_SELL),
            redIcon = SellManager.getRedVip() || SellManager.getAllRedFree()
        ) {
            gotoSellWithTabIndex(0)
        }
        MenuButton(
            modifier = Modifier.size(bottomItemSize),
            icon = R.drawable.icon_quest,
            text = stringResource(id = R.string.quest),
            unlock = repo.gameCore.getUnlockById(UNLOCK_MENU_TASK),
            redIcon = (QuestManager.dailyTasks + QuestManager.oneTimeTasks).any {
                QuestManager.getTaskDoneFlow(it) && !it.opened
            }
        ) {
            goto(QUEST_SCREEN)
        }
    }
}


@Composable
fun MenuButton(
    modifier: Modifier,
    text: String = "",
    icon: Int = 0,
    unlock: Unlock?,
    redIcon: Boolean = false,
    onclick: () -> Unit
) {
    val locked =
        unlock?.let { !UnlockManager.getUnlockedFlow(unlock) }
            ?: false
    EffectButton(
        modifier = modifier,
        onClick = { if (locked) (unlock?.desc ?: "").toast() else onclick() },
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.menu_button),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding16)
                .graphicsLayer {
                    translationX = padding2.toPx()
                },
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = icon),
            contentDescription = null
        )
        Image(
            modifier = Modifier
                .fillMaxHeight()
                .align(Alignment.CenterStart)
                .scale(1.2f)
                .graphicsLayer {
                    translationY = padding10.toPx()
                },
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_line2),
            contentDescription = null
        )
        if (redIcon) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .size(imageSmall),
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
        if (locked) {
            Image(
                modifier = Modifier
                    .align(Alignment.Center)
                    .size(imageMedium),
                painter = painterResource(R.drawable.common_lock),
                contentDescription = null
            )
        }
        Text(
            modifier = Modifier
                .width(padding6)
                .align(Alignment.CenterStart),
            text = text,
            style = MaterialTheme.typography.h2,
            color = Color.White
        )
    }
}
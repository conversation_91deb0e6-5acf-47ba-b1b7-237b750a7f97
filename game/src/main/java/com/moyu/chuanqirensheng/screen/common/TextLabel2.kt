package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R



@Composable
fun TextLabel2(
    modifier: Modifier = Modifier,
    labelSize: LabelSize = LabelSize.Medium2,
    frame: Int = R.drawable.common_frame4,
    text: String,
) {
    Box(
        modifier = modifier.size(labelSize.width, labelSize.height),
        contentAlignment = Alignment.Center
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = frame),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
        Text(
            text = text,
            style = labelSize.getTextStyle(),
            maxLines = 1
        )
    }
}
package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.core.model.Event


@Composable
fun EventDetailLayout(modifier: Modifier, event: Event) {
    Box(modifier = modifier) {
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(immersionBarHeightInDp))
            EventManager.getOrCreateHandler(event).Layout(event)
        }
    }
}
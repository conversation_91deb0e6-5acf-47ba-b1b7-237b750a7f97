package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getFrameDrawable
import com.moyu.chuanqirensheng.logic.getTouchInfo
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.RoleHpWithAnim
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.Stars
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.effect.orangeItemGif
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.expBarHeight
import com.moyu.chuanqirensheng.ui.theme.hpHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Ally
import kotlin.math.roundToInt


@Composable
fun SingleAllyView(
    ally: Ally,
    showName: Boolean = true,
    showNum: Boolean = false,
    showHp: Boolean = false,
    extraInfo: String = "",
    textColor: Color = Color.Black,
    showRed: Boolean = false,
    hide: Boolean = false,
    showEffect: Boolean = false,
    itemSize: ItemSize = ItemSize.LargePlus,
    selectCallBack: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it }
) {
    LaunchedEffect(Unit) {
        if (!hide) {
            GameIllustrationManager.unlockAlly(ally)
        }
    }
    val race = repo.gameCore.getRaceById(ally.id)
    EffectButton(modifier = Modifier.width(itemSize.frameSize), onClick = {
        selectCallBack(ally)
    }) {
        Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier.size(itemSize.frameSize),
                    painter = painterResource(ally.getFrameDrawable()),
                    contentDescription = null,
                )
                Image(
                    modifier = Modifier
                        .size(itemSize.itemSize)
                        .clip(RoundedCornerShape(itemSize.itemSize / 12)),
                    alignment = Alignment.TopCenter,
                    contentScale = ContentScale.Crop,
                    painter = painterResource(id = if (hide) R.drawable.common_question else getImageResourceDrawable(race.getHeadIcon())),
                    contentDescription = ally.getTouchInfo()
                )
                if (extraInfo.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .background(B50)
                            .padding(padding4)
                    ) {
                        Text(text = extraInfo, style = itemSize.getTextStyle())
                    }
                }
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter),
                    ally.star,
                    starWidth = itemSize.frameSize / 4.3f
                )
                if (showRed && ally.new) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
                if (showEffect && ally.quality == 3) {
                    val infiniteTransition = rememberInfiniteTransition(label = "")
                    val index = infiniteTransition.animateFloat(
                        initialValue = 1f,
                        targetValue = orangeItemGif.count.toFloat(),
                        animationSpec = infiniteRepeatable(
                            animation = tween(3800, easing = LinearEasing),
                            repeatMode = RepeatMode.Restart,
                        ),
                        label = ""
                    )
                    if (index.value.roundToInt() <= orangeItemGif.count) { // 做一个间歇的效果
                        Image(
                            modifier = Modifier
                                .size(itemSize.frameSize)
                                .scale(1.8f)
                                .graphicsLayer {
                                    translationX = padding2.toPx()
                                    translationY = -padding2.toPx()
                                }, painter = painterResource(
                                getImageResourceDrawable(
                                    "${orangeItemGif.gif}${index.value.roundToInt()}"
                                )
                            ), contentDescription = null
                        )
                    }
                }
            }
            if (showNum) {
                Box(contentAlignment = Alignment.CenterStart) {
                    CommonBar(
                        modifier = Modifier.size(itemSize.itemSize, expBarHeight),
                        currentValue = ally.num,
                        maxValue = ally.starUpNum + 1,
                        fullRes = R.drawable.common_card_line,
                        emptyRes = R.drawable.common_card_empty,
                        textColor = Color.White,
                        style = MaterialTheme.typography.h6
                    )
                    if (ally.num - 1 >= ally.starUpNum && ally.star < ally.starLimit) {
                        Image(
                            modifier = Modifier
                                .height(expBarHeight)
                                .graphicsLayer {
                                    translationX = -padding19.toPx()
                                }.scale(1.3f),
                            contentScale = ContentScale.FillHeight,
                            painter = painterResource(R.drawable.hero_starup),
                            contentDescription = null
                        )
                    }
                }
            }
            if (showName) {
                Box(modifier = Modifier.graphicsLayer {
                    translationY = -itemSize.frameSize.toPx() / 10
                }, contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier
                            .width(itemSize.frameSize)
                            .scale(1.8f),
                        painter = painterResource(id = R.drawable.shop_name_frame),
                        contentScale = ContentScale.FillWidth,
                        contentDescription = null
                    )
                    Text(
                        modifier = Modifier.graphicsLayer {
                            translationY = itemSize.frameSize.toPx() / 15
                        },
                        text = if (hide) "???" else race.name,
                        style = itemSize.getTextStyle(),
                        maxLines = 2,
                        minLines = 1,
                        textAlign = TextAlign.Center,
                        color = textColor
                    )
                }
            }
            if (showHp) {
                Box(
                    modifier = Modifier.size(itemSize.frameSize * 0.88f, hpHeight)
                ) {
                    RoleHpWithAnim { BattleManager.getRoleByAlly(ally = ally) }
                }
                Spacer(modifier = Modifier.size(padding2))
            }
        }
    }
}
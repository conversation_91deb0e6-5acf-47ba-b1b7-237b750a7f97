package com.moyu.chuanqirensheng.screen.filter

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.QUICK_GAP
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.filterHeight
import com.moyu.chuanqirensheng.ui.theme.filterWidth
import com.moyu.chuanqirensheng.ui.theme.padding2


@Composable
fun <T> OrderLayout(
    modifier: Modifier,
    show: MutableState<Boolean>,
    filter: MutableState<ItemOrder<T, String>>,
    filterList: List<ItemOrder<T, String>>
) {
    if (show.value) {
        Box(modifier = Modifier
            .fillMaxSize()
            .clickable {
                show.value = false
            }
            .background(B50))
    }
    AnimatedVisibility(
        modifier = modifier, visible = show.value
    ) {
        Column(modifier = Modifier.verticalScroll(rememberScrollState())) {
            repeat(filterList.size) { selectIndex ->
                EffectButton(clickGap = QUICK_GAP, onClick = {
                        filter.value = filterList[selectIndex]
                }) {
                    Box(
                        contentAlignment = Alignment.Center,
                        modifier = Modifier.padding(bottom = padding2)
                    ) {
                        val res = if (filter.value == filterList[selectIndex]) R.drawable.common_button11
                        else R.drawable.common_button22
                        Image(
                            modifier = Modifier.size(filterWidth, filterHeight),
                            painter = painterResource(res),
                            contentDescription = null
                        )
                        Text(
                            text = filterList[selectIndex].name,
                            style = MaterialTheme.typography.h3,
                            color = filterList[selectIndex].color
                        )
                    }
                }
            }
        }
    }
}
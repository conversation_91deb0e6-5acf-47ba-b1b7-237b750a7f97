package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.detail.GOD_BATTLE_PLAY
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.model.Event


const val INIT_AGE = 0
val battleEventIds = listOf(21, 22, 23, 24, 25, 26)
val singleBattleEventIds = listOf(24, 26)
val masterEventIds = listOf(21, 22, 23, 26)
const val EVENT_SIZE = 3

// 只生效一次，生效后要移除
fun Event.forceLose(): Boolean {
    return if (BattleManager.adventureProps.value.failEventPlayIds.contains(
            play
        )
    ) {
        BattleManager.adventureProps.value.failEventPlayIds.toMutableList().let {
            it.remove(play)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                failEventPlayIds = it
            )
        }
        true
    } else if (BattleManager.adventureProps.value.failEventPlayIds.toMutableList().contains(0)) {
        BattleManager.adventureProps.value.failEventPlayIds.toMutableList().let {
            it.remove(0)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                failEventPlayIds = it
            )
        }
        true
    } else {
        false
    }
}

// 只生效一次，生效后要移除
fun Event.tryForceWin(): Boolean {
    return if (BattleManager.adventureProps.value.winEventPlayIds.contains(
            play
        )
    ) {
        BattleManager.adventureProps.value.winEventPlayIds.toMutableList().let {
            it.remove(play)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                winEventPlayIds = it
            )
        }
        true
    } else if (BattleManager.adventureProps.value.winEventPlayIds.toMutableList().contains(0)) {
        BattleManager.adventureProps.value.winEventPlayIds.toMutableList().let {
            it.remove(0)
            BattleManager.adventureProps.value = BattleManager.adventureProps.value.copy(
                winEventPlayIds = it
            )
        }
        true
    } else {
        false
    }
}

fun Event.isBattle() = play in battleEventIds
fun Event.isSingle() = play in singleBattleEventIds
fun Event.isNeedMaster() = play in masterEventIds
fun Event.isNeedGod() = play == GOD_BATTLE_PLAY

fun Int.ageToProgress(): String {
    return (this - INIT_AGE).let {
        GameApp.instance.getWrapString(R.string.age_to_progress_text, this)
    }
}

fun Int.levelToDifficultProgress(): String {
    val difficult = repo.gameCore.getDifficultPool()[this / 1000]
    return difficult.name + " " + (this - INIT_AGE).let {
        GameApp.instance.getWrapString(R.string.age_to_progress_text, this % 1000)
    }
}
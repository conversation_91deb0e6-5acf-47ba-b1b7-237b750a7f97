package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.feature.quest.onTaskAge
import com.moyu.chuanqirensheng.feature.quest.onTaskDoneEvent
import com.moyu.chuanqirensheng.feature.quest.onTaskEnding
import com.moyu.chuanqirensheng.feature.quest.onTaskEnterEvent
import com.moyu.chuanqirensheng.feature.router.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.feature.router.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.detail.BattlePropPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.BetrayAllyPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.BingFuPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.BuildingPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.EnchantPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.EquipPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.ExamBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.GodBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.HaloBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.HelpPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.HireAllyPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.JinNangPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.MeetingPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.NonePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.PROTECT_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.PolicyPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.ProtectBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SIEGE_BATTLE
import com.moyu.chuanqirensheng.logic.event.detail.SiegeBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SingleBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SkillPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.TalkPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.TroopUpgradePlayHandler
import com.moyu.chuanqirensheng.logic.skill.AgeEvent
import com.moyu.chuanqirensheng.logic.skill.EnterEvent
import com.moyu.chuanqirensheng.logic.skill.FailedEvent
import com.moyu.chuanqirensheng.logic.skill.SucceededEvent
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.core.GameCore
import com.moyu.core.model.Event
import com.moyu.core.model.Timing
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

object EventManager {
    val selectedEvent = mutableStateOf<Event?>(null)// 当前选中事件
    val selectionEvents = mutableStateListOf<Event>() // 当前可选事件
    val eventRecorder = EventRecorder()

    private val eventHandlerClasses = hashMapOf(
        1 to { TalkPlayHandler() },
        2 to { HireAllyPlayHandler() },
        3 to { BetrayAllyPlayHandler() },
        4 to { SkillPlayHandler() },
        5 to { EnchantPlayHandler() },
        7 to { TroopUpgradePlayHandler() },
        9 to { EquipPlayHandler() },
        10 to { BattlePropPlayHandler() },
        11 to { JinNangPlayHandler() },
        12 to { BingFuPlayHandler() },
        13 to { PolicyPlayHandler() },
        14 to { BuildingPlayHandler() },
        15 to { MeetingPlayHandler() },
        16 to { HelpPlayHandler() },
        21 to { HaloBattlePlayHandler() },
        22 to { SiegeBattlePlayHandler() },
        23 to { ProtectBattlePlayHandler() },
        24 to { SingleBattlePlayHandler() },
        25 to { GodBattlePlayHandler() },
        26 to { ExamBattlePlayHandler() },
    )
    private val playHandlers = hashMapOf<Int, PlayHandler>()

    fun onNewGame() {
        playHandlers.clear()
        eventRecorder.clear()
        selectedEvent.value = null
        selectionEvents.clear()
    }

    fun getEventSelectTitle(): String {
        return BattleManager.adventureProps.value.age.ageToProgress()
    }

    fun getNextEvents(): List<Event> {
        if (selectionEvents.isNotEmpty()) { // 保护
            return selectionEvents
        }

        if (DebugManager.allEvent) {
            return repo.gameCore.getEventPool().filter { StoryManager.eventInStoryBag(it) }
                .map { it.createUUID() }
        }

        val age = BattleManager.adventureProps.value.age
        val fixedAppear =
            repo.gameCore.getEventPool().filter { StoryManager.eventInStoryBag(it) }
                .any { it.appear == age % 10 }
        val allFixedEvents = if (fixedAppear) {
            /**
             * 如果这一年，存在appear和这一年相等的event，
             * 筛选出来，直接随机，比如第1年，因为存在appear==1的event，全部筛选出来，随机3个，不需要看same
             */
            repo.gameCore.getEventPool().asSequence()
                .filter { age >= it.age[0] && age <= it.age[1] }
                .filter { it.appear == age % 10 }
                .filter { StoryManager.eventInStoryBag(it) }
                .filter(eventRecorder.filterNotUsed)
                .filter(eventRecorder.filterFront)
                .filter(eventRecorder.filterDisappear)
                .map { it.createUUID() }
                .toList()
        } else emptyList()
        val allEvents = allFixedEvents.ifEmpty {
            /**
             * 如果这一年，不存在appear和这一年相等的event，
             * 则从所有appear == -1的events里随机选一个，
             * 再根据随机到的这个event的same和play，选3个
             * 注意same==1则要求play相同
             * same==0则不要求
             */
            val innerList = repo.gameCore.getEventPool().asSequence()
                .filter { age >= it.age[0] && age <= it.age[1] }
                .filter { it.appear == -1 }
                .filter { StoryManager.eventInStoryBag(it) }
                .filter(eventRecorder.filterNotUsed)
                .filter(eventRecorder.filterFront)
                .filter(eventRecorder.filterDisappear)
                .map { it.createUUID() }
                .toList().shuffled(RANDOM)
            selectEvents(innerList.toMutableList())
        }

        val events = if (!GuideManager.nonBattleEventShowed.value) {
            // 首次进入游戏，展示非战斗事件引导
            listOf(100078,100424,100442).map {
                repo.gameCore.getEventById(it).createUUID()
            }
        } else allEvents.shuffled(RANDOM).take(EVENT_SIZE)
        selectionEvents.clear()
        selectionEvents.addAll(events)
        return events.map { it.createUUID() }
    }

    fun selectEvents(innerList: MutableList<Event>, targetCount: Int = 3): List<Event> {
        val selectedEvents = mutableListOf<Event>()

        while (selectedEvents.size < targetCount && innerList.isNotEmpty()) {
            val selectedEvent = selectEventByWeight(innerList)
            selectedEvents.add(selectedEvent)

            // 更新列表基于selectedEvent的属性
            if (selectedEvent.same == 1) {
                // 删除与selectedEvent play不同的事件
                innerList.removeAll { it.play != selectedEvent.play }
            } else {
                // 删除与selectedEvent play相同的事件以及所有same为1的事件
                innerList.removeAll { it.play == selectedEvent.play || it.same == 1 }
            }
            // 移除当前选出来的事件
            innerList.removeAll { it.uuid == selectedEvent.uuid }
        }

        return selectedEvents
    }

    fun selectEventByWeight(events: List<Event>): Event {
        val totalWeight = events.sumOf { it.weight }
        val randomWeight = RANDOM.nextInt(totalWeight) + 1

        var currentWeight = 0
        for (event in events) {
            currentWeight += event.weight
            if (randomWeight <= currentWeight) {
                return event
            }
        }

        throw IllegalStateException("Should never reach here if the list is not empty")
    }

    suspend fun selectEvent(event: Event): Boolean {
        if (getUsedEvents().any { it.selectAge == BattleManager.getAge() }) return false
        if (!DebugManager.repeatEvent && event.isRepeat == 0 && getUsedEvents().any { it.id == event.id }) return false
        if (selectedEvent.value?.selectAge == BattleManager.getAge()) return false
        return if (triggerEvent(event)) {
            // 条件满足，根据玩法进行游戏
            goto(EVENT_DETAIL_SCREEN)
            selectedEvent.value = event.copy(selectAge = BattleManager.getAge())
            ContinueManager.selectEvent(event)

            doEvent(event)
            playerMusicByScreen()

            GameCore.instance.onBattleEffect(SoundEffect.SelectEvent)
            true
        } else {
            // 条件不满足
            GameApp.instance.getWrapString(R.string.event_not_ready).toast()
            false
        }
    }

    fun getOrCreateHandler(event: Event): PlayHandler {
        val handler = playHandlers[event.id]
        return if (handler == null) {
            val newHandler = eventHandlerClasses[event.play]?.invoke() ?: NonePlayHandler()
            playHandlers[event.id] = newHandler
            newHandler
        } else {
            handler
        }
    }

    private suspend fun doEvent(event: Event) {
        onTaskEnterEvent(event)

        repo.onBattleInfo(
            "\n" + GameApp.instance.getWrapString(
                R.string.choose_event1, BattleManager.adventureProps.value.age, event.name
            ),
            BattleInfoType.ExtraSkill
        )
        adventureSkillTrigger(triggerSkill = EnterEvent.copy(mainId = event.play))
        BattleManager.onEventSelect()
        getOrCreateHandler(event).eventSelect(event)
    }

    fun doEventBattleResult(event: Event?, result: Boolean, forceQuit: Boolean = false, forceKill: Boolean = true) {
        if (event == null) return
        if (getOrCreateHandler(event).eventFinished.value) return

        // 战斗失败
        BattleManager.onBattleEnd()
        playerMusicByScreen() // 音乐
        GameApp.globalScope.launch(Dispatchers.Main) {
            // 需要杀死你的出战军团卡，因为可能你强行退出战斗了
            if (forceKill) {
                repo.battle.value.getAllPlayers().filter { !it.isDeath() }.forEach {
                    BattleManager.updateAllyInGameById(it, 0)
                }
            }
            BattleManager.checkAnyAllyDied()
            if (!forceQuit && event.isMainLine == 1 && !result && BattleManager.canKeepFight(event)
            ) {
                // 继续战斗，判定还有没有盟友，不需要要做啥
                GameApp.instance.getWrapString(R.string.keep_fight_tips).toast()
            } else {
                getOrCreateHandler(event).apply {
                    eventFinished.value = true
                    eventResult.value = result
                    doEventResult(event, result)
                }
            }
        }
    }

    fun doEventResult(event: Event, result: Boolean) {
        GuideManager.nonBattleEventShowed.value = true
        if (event.dialogType == 1) {
            // 1=普通对话时
            goNextEvent(event, result)
        } else if (event.dialogType == 2 && result) {
            // 结束后对话
        } else if (event.dialogType == 3 && !result) {
            // 失败后对话
        } else {
            goNextEvent(event, result)
        }
    }

    fun goNextEvent(event: Event, result: Boolean) {
        if (eventRecorder.addResult(event, result)) {
            // 这里说明不可重复事件重复进行了结算，直接跳回事件选择页面，极端情况，防止卡死
            GameApp.globalScope.launch(Dispatchers.Main) {
                gotoNextEvent(event, false)
            }
            return
        }
        GameApp.globalScope.launch(Dispatchers.Main) {
            repo.onBattleInfo(
                "【${event.name}】${GameApp.instance.getWrapString(R.string.events)}${
                    if (result) GameApp.instance.getWrapString(R.string.win) else GameApp.instance.getWrapString(
                        R.string.lose
                    )
                }", BattleInfoType.ExtraSkill
            )
            if (result) {
                // 其他事件在弹窗里跳转
                Dialogs.eventPassDialog.value = event
            } else {
                Dialogs.eventFailDialog.value = event
            }
        }
    }

    fun getUsedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.usedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.usedEvents
    }

    fun getSucceededEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.succeededEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.succeededEvents
    }

    fun getFailedEvents(startAge: Int? = null): List<Event> {
        return startAge?.let {
            eventRecorder.failedEvents.filter { it.selectAge >= startAge }
        } ?: eventRecorder.failedEvents
    }

    suspend fun gotoNextEvent(event: Event?, pass: Boolean, agePlus: Int = 1, surrend: Boolean = false,) {
        BattleManager.onNextEvent()
        selectionEvents.clear()
        if (pass) {
            adventureSkillTrigger(triggerSkill = SucceededEvent.copy(mainId = event?.play ?: 0))
        } else {
            adventureSkillTrigger(triggerSkill = FailedEvent.copy(mainId = event?.play ?: 0))
        }
        val story = EndingManager.saveEnding(
            BattleManager.you.value,
            eventRecorder.usedEvents,
            pass
        )
        if (pass && event != null) {
            onTaskDoneEvent(event)
        }
        event?.let {
            onTaskEnding(it, pass)
        }
        if (!pass && (event?.isMainLine == 1 || StoryManager.selectedEndless())) {
            EndingManager.ending(story)
        } else if (event?.isEnd == true && ((pass && event.endType == 1) || (!pass && event.endType == 2))) {
            EndingManager.ending(story)
        } else if (BattleManager.getGameAllies().all { it.isDead() }) {
            EndingManager.ending(story)
        } else if (BattleManager.getAge() == StoryManager.getMaxAge()) {
            EndingManager.ending(story)
        } else if (surrend) {
            EndingManager.ending(story)
        } else {
            adventureSkillTrigger(triggerSkill = AgeEvent)
            // 初始阶段年龄不增加
            BattleManager.gainAdventureProp(AdventureProps(age = agePlus))
            repeat(agePlus) {
                BattleManager.oneYearPass()
            }
            onTaskAge(BattleManager.adventureProps.value.age)

            BattleManager.you.value.markSkillNewTurn(BattleManager.you.value)
            BattleManager.you.value.clearGrave(Timing.TurnBegin)

            BattleManager.updateYou()

            goto(EVENT_SELECT_SCREEN)
            playerMusicByScreen()

            if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                GameReviewManager.checkTriggerReviewDialog(BattleManager.getAge())
            }
        }
    }

    fun extraIsGameFailed(): Boolean {
        selectedEvent.value?.play?.takeIf { it == SIEGE_BATTLE }?.let {
            return repo.battleTurn.intValue > (selectedEvent.value?.playPara1?.get(1) ?: 0)
        }
        if (repo.gameMode.value.isTowerMode() && TowerManager.targetLevel.value.type.first() == 4) {
            return repo.battleTurn.intValue > (TowerManager.targetLevel.value.playPara1.first())
        }
        return false
    }

    fun extraIsGameWin(): Boolean {
        selectedEvent.value?.play?.takeIf { it == PROTECT_BATTLE }?.let {
            return repo.battleTurn.intValue > (selectedEvent.value?.playPara1?.get(1) ?: 0)
        }
        return false
    }

    fun resetEventRecorder(used: List<Event>, success: List<Event>, failed: List<Event>) {
        eventRecorder.resetEvents(used, success, failed)
    }
}
package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.core.model.Event

class EventRecorder {

    val succeededEvents = mutableStateListOf<Event>()
    val failedEvents = mutableStateListOf<Event>()
    val usedEvents = mutableStateListOf<Event>()
    
    fun clear() {
        succeededEvents.clear()
        failedEvents.clear()
        usedEvents.clear()
    }

    fun addResult(event: Event, adjustedResult: Boolean): Boolean {
        if (usedEvents.any { it.uuid == event.uuid }) return true
        if (usedEvents.any { it.selectAge == BattleManager.getAge() }) return true
        event.copy(selectAge = BattleManager.getAge()).let {
            usedEvents.add(it)
            if (adjustedResult) {
                succeededEvents.add(it)
            } else {
                failedEvents.add(it)
            }
            return false
        }
    }

    fun resetEvents(used: List<Event>, success: List<Event>, failed: List<Event>) {
        usedEvents.clear()
        usedEvents.addAll(used)
        succeededEvents.clear()
        succeededEvents.addAll(success)
        failedEvents.clear()
        failedEvents.addAll(failed)
    }

    val filterNotUsed: (Event) -> Boolean = {
        if (it.isRepeat == 1) true else it.id !in usedEvents.map { it.id }
    }
    val filterFront: (Event) -> Boolean = {
        it.front.first() == 0 || it.front.intersect(usedEvents.map { it.id }
            .toSet()).isNotEmpty()
    }
    val filterDisappear: (Event) -> Boolean = {
        it.disappear.first() == 0 || it.disappear.intersect(usedEvents.map { it.id }
            .toSet())
            .isEmpty()
    }
}
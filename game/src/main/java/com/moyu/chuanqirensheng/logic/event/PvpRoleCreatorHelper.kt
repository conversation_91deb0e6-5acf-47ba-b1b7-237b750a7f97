package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Race
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

fun createPvpRole(race: Race, talents: Map<Int, Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().firstOrNull { it.mainId == mainId && it.level == talents[mainId] }
        talentSkill?.let {
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}

fun createPvpPlayerRole(race: Race, talents: Map<Int, Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().firstOrNull { it.mainId == mainId && it.level == talents[mainId] }
        talentSkill?.let {
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.player(name = race.name)
    )
}
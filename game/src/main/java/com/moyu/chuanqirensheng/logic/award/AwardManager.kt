package com.moyu.chuanqirensheng.logic.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.toMutableStateList
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_NUM
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.onTaskGetItem
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.gainGold
import com.moyu.chuanqirensheng.logic.battle.BattleManager.gainResource
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.skill.GetReputationInGame
import com.moyu.chuanqirensheng.logic.skill.ReputationDownEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationLevelDownEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationLevelUpEvent
import com.moyu.chuanqirensheng.logic.skill.ReputationUpEvent
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager.checkAlly
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.anticheat.GuardedList
import com.moyu.chuanqirensheng.sub.datastore.KEY_AD_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_BADGES
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_CHARGE_DONE
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_KEY_COST
import com.moyu.chuanqirensheng.sub.datastore.KEY_LOTTERY_MONEY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_DIAMOND_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPUTATIONS
import com.moyu.chuanqirensheng.sub.datastore.KEY_TCG_CARD_REWARD
import com.moyu.chuanqirensheng.sub.datastore.KEY_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS
import com.moyu.chuanqirensheng.sub.datastore.KEY_WAR_PASS2
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObjectSync
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.share.ShareManager.shareCount
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.AdUtil.decodeText
import com.moyu.chuanqirensheng.util.AdUtil.encodeText
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_BADGE
import com.moyu.core.model.EMPTY_REPUTATION
import com.moyu.core.model.Sell
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.perBiggerI
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object AwardManager {
    val tcgCardRewardRecords = mutableStateListOf<Int>()

    val battlePassBought = mutableMapOf<Int, MutableState<Boolean>>()
    val battlePass2Bought = mutableMapOf<Int, MutableState<Boolean>>()
    val diamond = Guarded(KEY_DIAMOND)
    val diamondAll = Guarded(KEY_DIAMOND_ALL)
    val key = Guarded(KEY_KEY)
    val keyCost = Guarded(KEY_KEY_COST)
    val keyAll = Guarded(KEY_KEY_ALL)
    val electric = Guarded(KEY_ELECTRIC)
    val pvpDiamond = Guarded(KEY_PVP_DIAMOND)
    val pvpDiamondAll = Guarded(KEY_PVP_DIAMOND_ALL)
    val warPass = Guarded(KEY_WAR_PASS)
    val warPass2 = Guarded(KEY_WAR_PASS2)
    val adNum = Guarded(KEY_AD_NUM)
    val badges = GuardedList(KEY_BADGES, EMPTY_BADGE.toMutableStateList())
    val reputations = GuardedList(KEY_REPUTATIONS, EMPTY_REPUTATION.toMutableStateList())
    val lotteryMoney = Guarded(KEY_LOTTERY_MONEY)

    fun init() {
        (KEY_WAR_PASS_UNLOCK_EVIDENCE..KEY_WAR_PASS_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePassBought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }

        (KEY_WAR_PASS2_UNLOCK_EVIDENCE..KEY_WAR_PASS2_UNLOCK_EVIDENCE + KEY_WAR_PASS_NUM).forEachIndexed { index, i ->
            battlePass2Bought[index + 1] =
                GuardedB(KEY_UNLOCK_EVIDENCE + i)
        }

        tcgCardRewardRecords.addAll(
            getListObject(
                KEY_TCG_CARD_REWARD
            )
        )
    }

    suspend fun gainAward(award: Award) {
        withContext(Dispatchers.Main) {
            onTaskGetItem(award)
            MusicManager.playSound(SoundEffect.GainAward)
            if (award.reputations.any { it != 0 }) {
                val oldReputations = toReputationLevels()
                award.reputations.forEachIndexed { index, i ->
                    if (i != 0) {
                        gainReputation(index, i)
                        if (i > 0) {
                            adventureSkillTrigger(triggerSkill = ReputationUpEvent.copy(mainId = index))
                        } else {
                            adventureSkillTrigger(triggerSkill = ReputationDownEvent.copy(mainId = index))
                        }
                    }
                    // 是否声望升级
                    if (toReputationLevels()[index] > oldReputations[index]) {
                        adventureSkillTrigger(triggerSkill = ReputationLevelUpEvent.copy(mainId = index))
                    } else if (toReputationLevels()[index] < oldReputations[index]) {
                        adventureSkillTrigger(triggerSkill = ReputationLevelDownEvent.copy(mainId = index))
                    }
                }
            }
            award.diamond.takeIf { it != 0 }?.let {
                gainDiamond(it)
            }
            award.pvpDiamond.takeIf { it != 0 }?.let {
                gainPvpDiamond(it)
            }
            award.lotteryMoney.takeIf { it != 0 }?.let {
                lotteryMoney.value += it
            }
            award.pvpScore.takeIf { it != 0 }?.let {
                PvpManager.pvpScore.value += it
            }
            award.gold.takeIf { it != 0 }?.let {
                gainGold(it)
            }
            award.resource.takeIf { it != 0 }?.let {
                gainResource(it)
            }
            award.key.takeIf { it != 0 }?.let {
                gainKey(it)
            }
            award.electric.takeIf { it != 0 }?.let {
                gainElectric(it)
            }
            award.warPass.takeIf { it != 0 }?.let {
                gainWarPass(it)
            }
            award.warPass2.takeIf { it != 0 }?.let {
                gainWarPass2(it)
            }
            award.exp.takeIf { it != 0 }?.let { rawExp ->
                BattleManager.gainExp(rawExp)
            }
            award.allHeal.takeIf { it > 0 }?.let {
                BattleManager.healAllAllyInGame(it)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_heal_all),
                    BattleInfoType.Battle
                )
            }
            award.battleHeal.takeIf { it > 0 }?.let {
                BattleManager.healBattleAllyInGame(it)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_heal_battle),
                    BattleInfoType.Battle
                )
            }
            award.titleId.takeIf { it > 0 }?.let {
                BattleManager.gainTitle(it)
            }
            award.upgradeSkills.forEach { pair ->
                val allyUuid = pair.first
                val skill = pair.second
                BattleManager.troopSkill(allyUuid, skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_skill_upgrade) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.allies.forEach { ally ->
                BattleManager.gainInGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_ally) + ally.name,
                    BattleInfoType.Battle
                )
            }
            award.equips.forEach { equip ->
                BattleManager.gainEquip(equip)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_equip) + equip.name,
                    BattleInfoType.Battle
                )
            }

            award.loseAllies.forEach { ally ->
                BattleManager.dropFromGame(ally)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_ally) + ally.name,
                    BattleInfoType.Battle
                )
            }
            award.outAllies.forEach { ally ->
                checkAlly(ally)
                repo.allyManager.gain(ally.copy(new = true))
            }
            award.badges.forEachIndexed { index, i ->
                if (i != 0) {
                    badges[index] += i
                    val badge = repo.gameCore.getBadgePool()[index]
                    repo.onBattleInfo(
                        GameApp.instance.getWrapString(R.string.gain) + badge.name + "*" + i,
                        BattleInfoType.Battle
                    )
                }
            }
            award.skills.forEach { skill ->
                BattleManager.gainSkillInGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_title) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.loseSkills.forEach { skill ->
                BattleManager.dropFromGame(skill)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.lose_skill_card) + skill.name,
                    BattleInfoType.Battle
                )
            }
            award.advProperty.takeIf { it.isNotEmpty() }?.let { property ->
                BattleManager.gainAdventureProp(property)
            }
            award.battleProperty.takeIf { it.isNotEmpty() }?.let { property ->
                BattleManager.gainBattleProp(property)
            }
            award.skins.forEach {
                SkinManager.gainSkin(it.copy(new = true))
            }
            award.tcgs.forEach { tcgCard ->
                TcgManager.gainCard(tcgCard)
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_tcg_comma) + tcgCard.name,
                    BattleInfoType.Battle
                )
            }
            award.unlockList.forEach {
                UnlockManager.unlockCode(it)
            }
        }
    }

    fun doNetAward(text: String) {
        GameApp.globalScope.launch {
            // 确认是否是推广码
            if (AdUtil.isShareCode(text)) {
                GameApp.instance.getObjectId()?.let {
                    encodeText(it)?.let { encryptedUserId ->
                        val realId = AdUtil.simpleDecryptionText(text)
                        val flow = getLongFlowByKey(realId)
                        if (flow != 0L) {
                            GameApp.instance.getWrapString(R.string.share_code_already_used_by_you)
                                .toast()
                        } else if (text == GameApp.instance.getShareCode()) {
                            GameApp.instance.getWrapString(R.string.share_code_can_not_use_your_own)
                                .toast()
                        } else {
                            val result = RetrofitModel.useShareCode(
                                it,
                                text,
                                encryptedUserId,
                                getVersionCode()
                            )
                            if (!result.valid) {
                                GameApp.instance.getWrapString(R.string.code_error_tips2).toast()
                            } else {
                                if (shareCount.value >= repo.gameCore.getMaxUseOtherCount()) {
                                    GameApp.instance.getWrapString(
                                        R.string.top_share_award_tips,
                                        repo.gameCore.getMaxUseOtherCount()
                                    ).toast()
                                } else {
                                    setLongValueByKey(realId, 1)
                                    shareCount.value += 1
                                    if (!result.isEmpty()) {
                                        gainAward(result)
                                        // 显示
                                        Dialogs.awardDialog.value = result
                                    }
                                }
                            }
                        }
                    } ?: GameApp.instance.getWrapString(R.string.code_error).toast()
                } ?: GameApp.instance.getWrapString(R.string.confirm_login).toast()
            } else {
                // 确认是否是网络兑换码
                val netCode = decodeText(text)
                if (netCode?.startsWith("u_") == true) {
                    val flow = getLongFlowByKey(text)
                    if (flow != 0L) {
                        GameApp.instance.getWrapString(R.string.code_already_used_by_you).toast()
                    } else {
                        GameApp.instance.getObjectId()?.let {
                            encodeText(it)?.let { encodedUserId ->
                                val result =
                                    RetrofitModel.getCodeAwards(it, netCode, encodedUserId, getVersionCode())
                                if (!result.valid) {
                                    GameApp.instance.getWrapString(R.string.code_error_tips2).toast()
                                } else {
                                    // 标记已领取
                                    setLongValueByKey(text, 1)
                                    if (result.unlockList.isNotEmpty()) {
                                        GameApp.instance.getWrapString(R.string.unlocked_tips).toast()
                                    } else if (result.message.isNotEmpty()) {
                                        result.message.toast()
                                    }
                                    if (!result.isEmpty()) {
                                        gainAward(result)
                                        // 显示
                                        Dialogs.awardDialog.value = result
                                    }

                                }
                            } ?: GameApp.instance.getWrapString(R.string.code_error).toast()
                        } ?: GameApp.instance.getWrapString(R.string.confirm_login).toast()
                    }
                } else {
                    awardList.find { text == it.code && (isNetTimeValid()) }
                        ?.let {
                            val flow = getLongFlowByKey(it.code)
                            if (flow == 0L) {
                                val award = it
                                // 标记已领取
                                setLongValueByKey(it.code, 1)
                                // 领取奖励
                                gainAward(award)
                                // 显示
                                Dialogs.awardDialog.value = award
                            } else {
                                GameApp.instance.getWrapString(R.string.code_used).toast()
                            }
                        } ?: GameApp.instance.getWrapString(R.string.code_not_found).toast()
                }
            }
        }
    }

    fun gainDiamond(gain: Int) {
        diamond.value += gain
        diamondAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }


    fun gainPvpDiamond(gain: Int) {
        pvpDiamond.value += gain
        pvpDiamondAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_pvp_diamond) + gain,
                BattleInfoType.ExtraSkill
            )
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    fun gainKey(gain: Int) {
        key.value += gain
        keyAll.value += gain
        if (gain > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_keys) + gain, BattleInfoType.ExtraSkill
            )
        } else {
            keyCost.value += -gain
            GameCore.instance.onBattleEffect(SoundEffect.UserMoney)
        }
    }

    suspend fun gainElectric(gain: Int) {
        withContext(Dispatchers.Main) {
            electric.value += gain
            SevenDayManager.gainElectric(gain)
            setBooleanValueByKey(KEY_FIRST_CHARGE_DONE, true)

            GameApp.globalScope.launch(Dispatchers.IO) {
                delay(2000)
                GameApp.instance.uploadCurrentSave()
            }
        }
    }

    suspend fun gainWarPass(gain: Int) {
        withContext(Dispatchers.Main) {
            warPass.value += gain
        }
    }

    suspend fun gainWarPass2(gain: Int) {
        withContext(Dispatchers.Main) {
            warPass2.value += gain
        }
    }

    suspend fun gainReputation(type: Int, num: Int) {
        withContext(Dispatchers.Main) {
            if (num > 0) {
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.gain_reputation) + ReputationManager.reputations[type] + num,
                    BattleInfoType.ExtraSkill
                )
                reputations[type] = reputations[type] + num
                adventureSkillTrigger(triggerSkill = GetReputationInGame.copy(mainId = type))
            } else if (num < 0) {
                reputations[type] = 0.coerceAtLeast(reputations[type] + num)
            }
        }
    }

    suspend fun gainTcgCardReward(tcgAward: TcgAward, award: Award) {
        if (tcgCardRewardRecords.contains(tcgAward.id)) {
            GameApp.instance.getWrapString(R.string.already_got).toast()
        } else {
            tcgCardRewardRecords.add(tcgAward.id)
            setListObjectSync(
                KEY_TCG_CARD_REWARD, tcgCardRewardRecords
            )
            GameApp.globalScope.launch {
                gainAward(award)
                Dialogs.awardDialog.value = award
            }
        }
    }

    fun isAffordable(award: Award, consume: Boolean = false): Boolean {
        award.badges.forEachIndexed { index, i ->
            if (i != 0) {
                if (badges[index] < i) {
                    return false
                }
            }
        }
        if (award.gold > BattleManager.gold.value) {
            return false
        }
        if (award.diamond > diamond.value) {
            return false
        }
        if (award.pvpDiamond > pvpDiamond.value) {
            return false
        }
        if (award.key > key.value) {
            return false
        }
        if (award.resource > BattleManager.resource.value) {
            return false
        }
        if (award.titleLevel != 0) {
            if (BattleManager.yourTitle.value.level < award.titleLevel) {
                return false
            }
        }
        if (award.reputations.any { it != 0 } && !reputations.perBiggerI(award.reputations)) {
            return false
        }
        if (!BattleManager.adventureProps.value.perBiggerI(award.advProperty)) {
            return false
        }
        if (award.skills.isNotEmpty()) {
            award.skills.forEach { target ->
                if (BattleManager.skillGameData.none {
                        it.id == target.id
                    }) {
                    return false
                }
            }
        }
        if (consume) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                gainGold(-award.gold)
                gainDiamond(-award.diamond)
                gainKey(-award.key)
                gainResource(-award.resource)
                award.badges.forEachIndexed { index, i ->
                    if (i != 0) {
                        badges[index] -= i
                    }
                }
            }
        }
        return true
    }

    fun toReputationLevels(): List<Int> {
        return reputations.map {
            ReputationManager.getReputationLevel(it)
        }
    }

    fun toReputationLevelData(): List<ReputationLevel> {
        return reputations.map {
            ReputationManager.getReputationLevelData(it)
        }
    }

    suspend fun realGainItem(it: Sell, realAward: Award) {
        setBooleanValueByKey(KEY_GIFT_AWARDED + it.id, true)
        setBooleanValueByKey(SELL_FOREVER + it.id, true)
        if (it.isGift()) {
            // todo 一定要先更新KEY_GIFT_AWARDED，不然首页的礼包icon不会正常消失，
            // 因为AwardManager.electric变化后，首页礼包icon就会重新检查条件，所以要先改变条件再出发更新
            gainAward(realAward)
            if (repo.inGame.value && !repo.gameMode.value.isPvpMode()) {
                // todo 礼包奖励，如果在局内，直接局内也要获得盟友卡
                gainAward(Award(allies = realAward.outAllies))
            }
            Dialogs.awardDialog.value = realAward
        } else if (it.isMonthCard()) {
            MonthCardManager.openPackage(it)
        } else if (it.isTower()) {
            TowerManager.openPackage(it)
        } else {
            gainAward(realAward)
            SellManager.openGoogleBillSell(it)
            Dialogs.awardDialog.value = realAward
            if (it.isNewTaskPackage()) {
                SevenDayManager.markGoogleSellItem(it)
            }
        }
    }
}
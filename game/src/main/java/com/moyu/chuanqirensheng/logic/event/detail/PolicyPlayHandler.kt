package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.getRealRequireByDifficult
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM

const val POLICY_PLAY = 13

class PolicyPlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = POLICY_PLAY
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()
    private val requires = mutableStateListOf<AdventureProps>()

    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TextLabel(
                Modifier.graphicsLayer {
                    translationX = padding42.toPx()
                },
                labelSize = LabelSize.Huge,
                text = event.play.playToName(),
                extraTextOffset = padding19 + padding19
            )
            EventAward3To1Layout(
                awards = awards,
                content = { index ->
                    Text(
                        modifier = Modifier.verticalScroll(rememberScrollState()),
                        text = awards[index].skills.first().getRealDescColorful(),
                        style = MaterialTheme.typography.h4
                    )
                },
                buttonEnables = List(awards.size) {
                    BattleManager.adventureProps.value >= requires[it]
                },
                buttonConditions = requires.map { Award(advProperty = it) }
            ) {
                if (BattleManager.adventureProps.value >= requires[it]) {
                    if (!eventFinished.value) {
                        eventFinished.value = true
                        EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                        EventManager.doEventResult(event, true)
                    }
                } else {
                    GameApp.instance.getWrapString(R.string.learn_fail_prop).toast()
                }
            }
            Spacer(modifier = Modifier.size(padding30))
            if (requires.all { BattleManager.adventureProps.value < it }) {
                GameButton(text = stringResource(id = R.string.quit)) {
                    if (!eventFinished.value) {
                        eventFinished.value = true
                        EventManager.doEventResult(event, true)
                    }
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        val gainProps = repo.gameCore.getPoolById(event.playPara1.first()).toAwards()
        val requiredProps = if (event.playPara2.first()
                .toInt() == 0
        ) List(gainProps.size) { Award() } else repo.gameCore.getPoolById(
            event.playPara2.first().toInt()
        ).toAwards()
        val combined = gainProps.zip(requiredProps).shuffled(RANDOM).take(3)
        awards.addAll(
            combined.map { it.first }
        )
        requires.clear()
        requires.addAll(combined.map { getRealRequireByDifficult(it.second).advProperty })
    }
}

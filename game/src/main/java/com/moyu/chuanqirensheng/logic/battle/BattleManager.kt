package com.moyu.chuanqirensheng.logic.battle

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.skin.getProperty
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.event.adventureSkillTrigger
import com.moyu.chuanqirensheng.logic.event.createPvpPlayerRole
import com.moyu.chuanqirensheng.logic.event.createTowerRole
import com.moyu.chuanqirensheng.logic.event.detail.GOD_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.event.isNeedMaster
import com.moyu.chuanqirensheng.logic.event.isSingle
import com.moyu.chuanqirensheng.logic.skill.GainExp
import com.moyu.chuanqirensheng.logic.skill.GainMoney
import com.moyu.chuanqirensheng.logic.skill.GainResource
import com.moyu.chuanqirensheng.logic.skill.UseMoney
import com.moyu.chuanqirensheng.logic.skill.UseResource
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.sub.anticheat.GuardedMemory
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.GameCore
import com.moyu.core.logic.enemy.DefaultAllyCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.logic.role.ALLY_ROW1_SECOND
import com.moyu.core.logic.role.ALLY_ROW2_THIRD
import com.moyu.core.logic.role.positionList
import com.moyu.core.logic.role.positionOrderedListAllies
import com.moyu.core.model.Ally
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.GameItem
import com.moyu.core.model.Pool
import com.moyu.core.model.RACE_SIZE
import com.moyu.core.model.Title
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.role.RoleExtraInfo
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.SkillEnhancementType
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isAllySpecial
import com.moyu.core.model.skill.isBattleTree
import com.moyu.core.model.skill.isTroopSkill
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID

val NO_TITLE = Title(name = GameApp.instance.getWrapString(R.string.no_title))

object BattleManager {
    val battleRolePositions = mutableMapOf<String, Pair<Dp, Dp>>()

    val you = mutableStateOf(Role(roleIdentifier = Identifier.player()))
    val yourTitle = mutableStateOf(NO_TITLE)
    val yourExp = GuardedMemory()

    val skillGameData = mutableStateListOf<Skill>()
    val allyGameData = mutableStateListOf<Ally>()
    val equipGameData = mutableStateListOf<Equipment>()

    val gold = GuardedMemory()
    val resource = GuardedMemory()

    val adventureProps = mutableStateOf(EMPTY_ADV_PROPS)
    val masterProp = mutableStateOf(EMPTY_PROPERTY)
    val battleProp = mutableStateOf(EMPTY_PROPERTY)
    val battleSkillPropMap = mutableMapOf<Int, Property>()
    val battleRaceProps = mutableListOf(EMPTY_PROPERTY)
    val battleEnchants = mutableStateListOf<Skill>()
    val troopSkills = mutableStateMapOf<String, Skill>()

    val currentBgMusic = mutableIntStateOf(0)
    val godReplacedAlly = mutableStateOf<Ally?>(null)

    val effectedSkills = mutableStateListOf<Skill>()

    fun init() {
        currentBgMusic.intValue = MusicManager.getRandomDungeonMusic()
        battleRolePositions.clear()

        gold.value = repo.gameCore.getInitGold()
        resource.value = repo.gameCore.getInitResource()

        battleRaceProps.clear()
        repeat(RACE_SIZE) {
            battleRaceProps.add(EMPTY_PROPERTY)
        }

        adventureProps.value = EMPTY_ADV_PROPS

        battleProp.value = EMPTY_PROPERTY
        masterProp.value = EMPTY_PROPERTY
        battleSkillPropMap.clear()

        battleEnchants.clear()
        troopSkills.clear()
        allyGameData.clear()
        skillGameData.clear()
        equipGameData.clear()
        effectedSkills.clear()

        // 主角加入战场
        if (!StoryManager.selectedEndless()) {
            // 无尽模式第一个ally是主角，不需要额外添加
            allyGameData.add(
                repo.gameCore.getAllyPool().first { it.id == StoryManager.getSelectStory().id }
                    .copyToGame()
            )
        }
        if (!getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
            // 还没完成首次游戏
            repo.gameCore.getFirstAllyIds().map {
                repo.gameCore.getAllyById(it)
            }.forEach {
                allyGameData.add(it.copyToGame())
            }
        } else {
            allyGameData.addAll(repo.allyManager.data.filter {
                it.selected
            }.sortedBy { it.selectedTime }.map { it.copyToGame() })
        }

        createYou()
        yourTitle.value = NO_TITLE
        yourExp.value = 0
    }

    fun onNewGame() {
        init() // 还要再init一次，改变了选择，要重新梳理下技能和军团卡的关系
    }

    fun createYou() {
        adventureProps.value = AdventureProps.createNew()
        val talents = TalentManager.talents.map { talent ->
            repo.gameCore.getSkillPool()
                .first { it.mainId == talent.key && it.level == talent.value }
        }
        you.value = Role(
            extraInfo = RoleExtraInfo(allyUuid = UUID.generateUUID().toString()),
            roleIdentifier = Identifier.player()
        ).apply {
            talents.forEach {
                learnSkill(it, roleIdentifier)
            }
        }
    }

    fun selectAllToGame() {
        allyGameData.clear()
        allyGameData.addAll(repo.allyManager.data.map {
            it.copyToGame()
        })
    }

    fun selectToGame(target: Ally) {
        val index = allyGameData.indexOfFirst { target.id == it.id } // 这里没有重复技能，未进入游戏，没有uuid，用id判定
        if (index != -1) {
            allyGameData.removeAt(index)
            repo.allyManager.selectToGame(target)
        } else {
            // todo -1是因为有固定主角，如果是无尽模式，没有固定主角，不-1
            if ((if (StoryManager.selectedEndless()) allyGameData.size else allyGameData.size - 1) >= UnlockManager.getInitAllyNum()) {
                GameApp.instance.getWrapString(R.string.carry_to_game, UnlockManager.getInitAllyNum())
                    .toast()
            } else {
                repo.allyManager.selectToGame(target)
                allyGameData.add(target.copyToGame())
            }
        }
    }

    suspend fun gainInGame(target: Ally) {
        allyGameData.add(target.copyToGame())
        DetailProgressManager.gainInGame(target)
    }

    fun gainTempInGame(target: Ally): Ally {
        val result = target.copyToGame().copy(temp = true)
        allyGameData.add(result)
        return result
    }

    suspend fun gainSkillInGame(target: Skill) {
        val setAgeSkill = if (!target.isAdventure()) target else {
            target.copy(gainAge = adventureProps.value.age)
        }
        setAgeSkill.copyToGame().let {
            skillGameData.add(it)
            DetailProgressManager.gainInGame(it)
            if (it.isAdventure()) {
                you.value.apply {
                    learnSkill(it, this.roleIdentifier)
                }
            }
        }
    }

    suspend fun dropFromGame(target: Ally, needTrigger: Boolean = true) {
        if (target.isMaster()) {
            // 主角死亡，直接下阵
            if (isAllyInBattle(getGameMaster())) {
                selectAllyToBattle(getGameMaster(), -1)
            }
        } else {
            skillGameData.filter { it.equipAllyUuid == target.uuid }.forEach {
                skillGameData.indexOfItemInGame(it.uuid) { index ->
                    //bug，遇到交换军团卡的事件，如果不取下军团卡身上的技能，交换军团卡之后军团卡身上技能就没了，这种情况改成自动卸下吧
                    skillGameData[index] = skillGameData[index].copy(equipAllyUuid = "")
                }
            }
            allyGameData.removeAll { it.uuid == target.uuid }
            if (needTrigger) {
                DetailProgressManager.dropFromGame(target)
            }
        }
    }

    suspend fun dropFromGame(target: Skill) {
        skillGameData.removeAll { it.uuid == target.uuid }
        DetailProgressManager.dropInGame(target)
        you.value.apply {
            forgetSkill(target)
        }
    }

    fun addEnchant(skill: Skill) {
        battleEnchants.add(skill)
    }

    fun troopSkill(allyUuid: String, skill: Skill) {
        troopSkills[allyUuid] = skill
    }

    fun enchant(targetSkill: Skill, enchantSkill: Skill): Skill {
        var finalSkill = targetSkill
        GameCore.instance.onBattleEffect(SoundEffect.UpgradeTalent)
        enchantSkill.subType.forEachIndexed { index, subType ->
            finalSkill = finalSkill.enchant(
                SkillEnhancementType(subType), enchantSkill.effectNum[index]
            )
        }
        return finalSkill
    }

    fun getGameAllies(): List<Ally> {
        return allyGameData.sortedByDescending { it.battlePosition }
    }

    fun getGameAlliesNoMaster(): List<Ally> {
        return allyGameData.filterNot { it.isMaster() }
    }

    fun getAliveGameAllies(): List<Ally> {
        return allyGameData.filter {
            !it.isDead()
        }
    }

    fun getBattleAllies(): Map<Int, Ally> {
        val mutableMap = mutableMapOf<Int, Ally>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = it
        }
        return mutableMap
    }

    fun getBattleRoles(capacity: Int): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        if (capacity == 1) {
            allyGameData.firstOrNull { it.battlePosition == ALLY_ROW1_SECOND }?.let {
                mutableMap[ALLY_ROW1_SECOND] = getRoleByAlly(it)
            }
        } else {
            allyGameData.filter { it.battlePosition >= 0 }.forEach {
                mutableMap[it.battlePosition] = getRoleByAlly(it)
            }
        }
        return mutableMap
    }

    fun getPvpBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createPvpPlayerRole(it.getRace(), TalentManager.talents)
        }
        return mutableMap
    }

    fun getTowerBattleRoles(): Map<Int, Role> {
        val mutableMap = mutableMapOf<Int, Role>()
        allyGameData.filter { it.battlePosition >= 0 }.forEach {
            mutableMap[it.battlePosition] = createTowerRole(it.getRace(), TalentManager.talents, emptyList())
        }
        return mutableMap
    }

    fun selectAllyToBattle(ally: Ally, position: Int) {
        if (position !in positionList && position != -1) {
            return
        }
        if (isAllyInBattle(ally)) {
            if (ally.temp) {
                GameApp.instance.getWrapString(R.string.god_need_tips).toast()
            } else {
                allyGameData.indexOfItemInGame(ally.uuid) {
                    allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
                }
            }
        } else if (allyGameData.any { it.battlePosition == position }) {
            allyGameData.indexOfItemInGame(allyGameData.first { it.battlePosition == position }.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(-1)
            }
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        } else {
            allyGameData.indexOfItemInGame(ally.uuid) {
                allyGameData[it] = allyGameData[it].switchSelectToBattle(position)
            }
        }
    }

    suspend fun checkAnyAllyDied() {
        // 不可以直接foreach，因为要改变list内容
        allyGameData.filter { it.isDead() }.map { it.uuid }.forEach {
            allyGameData.indexOfItemInGame(it) { index ->
                if (allyGameData[index].isDead()) {
                    dropFromGame(allyGameData[index])
                }
            }
        }
    }

    fun updateAllyInGameById(role: Role, hp: Int) {
        allyGameData.indexOfItemInGame(role.extraInfo.allyUuid) { index ->
            allyGameData[index] =
                allyGameData[index].copy(gameHp = if (hp >= role.getOriginProperty().hp) 100 else hp * 100 / role.getOriginProperty().hp)
        }
    }

    fun reliveAllyInGame(ally: Ally, relive: Boolean = true) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].relive()
        }
        if (relive) {
            GameCore.instance.onBattleEffect(SoundEffect.ReliveAlly)
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.HealAlly)
        }
    }

    fun healBattleAllyInGame(percent: Int) {
        getBattleAllies().values.forEach { ally ->
            allyGameData.indexOfItemInGame(ally.uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        if (getGameMaster().isDead()) {
            allyGameData.indexOfItemInGame(getGameMaster().uuid) { index ->
                allyGameData[index] = allyGameData[index].heal(percent)
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    fun healAllAllyInGame(percent: Int) {
        allyGameData.forEachIndexed { index, _ ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    fun healOneAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].heal(percent)
        }
        GameCore.instance.onBattleEffect(SoundEffect.Heal)
    }

    suspend fun hurtAllyInGame(ally: Ally, percent: Int) {
        allyGameData.indexOfItemInGame(ally.uuid) { index ->
            allyGameData[index] = allyGameData[index].hurt(percent)
            if (allyGameData[index].isDead()) {
                dropFromGame(allyGameData[index])
            }
        }
        GameCore.instance.onBattleEffect(SoundEffect.Damage1)
    }

    fun getGameSkills(): List<Skill> {
        return skillGameData
    }

    fun getRoleByAlly(ally: Ally): Role {
        if (repo.inGame.value) {
            val extraProperty =
                battleProp.value + battleRaceProps[ally.getRaceType() - 1]
            val masterProperty = if (ally.mainId == getGameMaster().mainId) {
                masterProp.value
            } else {
                Property()
            }
            val masterEquipProperty = if (ally.isMaster()) {
                getGameEquips().map { it.getProperty() }
                    .reduceOrNull { acc, property -> acc + property }
                    ?: Property()
            } else {
                Property()
            }
            val masterSkinProperty = if (ally.isMaster()) {
                SkinManager.currentSkins.firstOrNull { it.type == StoryManager.getSelectStory().id }
                    ?.getProperty() ?: Property()
            } else {
                Property()
            }

            return DefaultAllyCreator.create(
                race = repo.gameCore.getRaceById(ally.id),
                identifier = Identifier.player(name = ally.name),
                diffProperty = extraProperty
                        + masterEquipProperty
                        + masterSkinProperty
                        + masterProperty
                        + (ally.exerciseProperty ?: Property()) // 附加属性加上
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    ally.equipSkills.forEach {
                        learnSkill(it, this.roleIdentifier)
                    }

                    troopSkills[ally.uuid]?.let {
                        // 兵种可能已升级
                        forgetSkill(getSkills().first { it.isTroopSkill() })
                        learnSkill(it, roleIdentifier)
                    }

                    if (ally.isMaster()) {
                        // 主角有附魔效果
                        val special = getSkills().first { it.isAllySpecial() }
                        forgetSkill(special)
                        var enchanted = special
                        if (battleEnchants.isNotEmpty()) {
                            battleEnchants.forEach { enchant ->
                                enchanted = enchant(enchanted, enchant)
                            }
                        }
                        learnSkill(enchanted, roleIdentifier)

                        skillGameData.filter { it.isBattleTree() }.forEach {
                            learnSkill(it, roleIdentifier)
                        }
                    }
                    val extraSkillProp = getSkills().mapNotNull {
                        battleSkillPropMap[it.id]
                    }.reduceOrNull { acc, property -> acc + property } ?: Property()
                    setInitProperty(getInitProperty() + extraSkillProp)
                    setPropertyToDefault()
                    setCurrentHp(Integer.max(1, this.getOriginProperty().hp * ally.gameHp / 100))
                }
        } else {
            return DefaultAllyCreator.create(
                repo.gameCore.getRaceById(ally.id),
                Property(),
                identifier = Identifier.player(name = ally.name)
            ).copy(extraInfo = RoleExtraInfo(ally.uuid, allyQuality = ally.quality))
                .apply {
                    setPropertyToDefault()
                }
        }
    }

    fun getGameRoles(): List<Role> {
        return getGameAllies().filterNot { it.isDead() }.map {
            getRoleByAlly(it)
        }
    }

    fun onEventSelect() {
        allyGameData.removeAll { it.temp }
    }

    fun onBattleEnd() {
        allyGameData.removeAll { it.temp }
        godReplacedAlly.value?.let { ally ->
            allyGameData.firstOrNull { it.uuid == ally.uuid }?.let {
                selectAllyToBattle(it, ALLY_ROW2_THIRD)
            }
        }
        godReplacedAlly.value = null
    }

    fun onNextEvent() {
        battleRolePositions.clear()
        // todo 闪退保护
        try {
            if (getGameMaster().isDead()) {
                allyGameData.indexOfItemInGame(getGameMaster().uuid) {
                    // 主角不会死，保留1点血
                    allyGameData[it] = allyGameData[it].copy(gameHp = 1)
                }
            }
        } catch (e: Exception) {
            e.message?.toast()
            e.printStackTrace()
        }
    }

    fun isAllyInBattle(ally: Ally): Boolean {
        return allyGameData.any { it.uuid == ally.uuid && it.battlePosition >= 0 }
    }

    fun onPermanentDiff(target: Role, diff: Property) {
        allyGameData.indexOfItemInGame(target.extraInfo.allyUuid) {
            val newProperty = (allyGameData[it].exerciseProperty ?: Property()) + diff
            allyGameData[it] = allyGameData[it].copy(exerciseProperty = newProperty)
        }
    }

    fun setAllyUnNew() {
        val temp = allyGameData.toList()
        allyGameData.clear()
        allyGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setAllyUnNew(ally: Ally) {
        allyGameData.indexOfItemInGame(ally.uuid) {
            allyGameData[it] = allyGameData[it].copy(new = false)
        }
    }

    fun setSkillUnNew(filter: (Skill) -> Boolean) {
        val temp = skillGameData.filter { filter(it) }.toList()
        skillGameData.removeAll { filter(it) }
        skillGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setSkillUnNew(skill: Skill) {
        skillGameData.indexOfItemInGame(skill.uuid) {
            skillGameData[it] = skillGameData[it].copy(new = false)
        }
    }

    fun setEquipUnNew() {
        val temp = equipGameData.toList()
        equipGameData.clear()
        equipGameData.addAll(temp.map { it.copy(new = false) })
    }

    fun setEquipUnNew(equipment: Equipment) {
        equipGameData.indexOfItemInGame(equipment.uuid) {
            equipGameData[it] = equipGameData[it].copy(new = false)
        }
    }

    fun oneYearPass() {
        skillGameData.map {
            it.nextYear()
        }.let {
            skillGameData.clear()
            skillGameData.addAll(it)
        }
        you.value.oneYearPass()
    }

    fun gainBattleProp(property: Property) {
        if (property.getNonZeroString().isNotEmpty()) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_battle_prop) + property.getNonZeroString(),
                BattleInfoType.ExtraSkill
            )
        }
        masterProp.value += property
    }

    suspend fun gainAdventureProp(property: AdventureProps) {
        if (property.getNonZeroString().isNotEmpty()) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_adv_prop) + property.getNonZeroString(),
                BattleInfoType.ExtraSkill
            )
        }
        withContext(Dispatchers.Main) {
            adventureProps.value += property
            adventureProps.value = adventureProps.value.ensureNonNegative()
        }
    }

    fun gainPermanentProp(propertyByEnum: Property) {
        battleProp.value += propertyByEnum
    }

    fun gainPermanentRaceProp(race: Int, property: Property) {
        battleRaceProps[race] = battleRaceProps[race] + property
    }

    fun gainPermanentSkillProp(skillId: Int, property: Property) {
        battleSkillPropMap[skillId] = (battleSkillPropMap[skillId] ?: EMPTY_PROPERTY) + property
    }

    fun getGameMaster(): Ally {
        return if (StoryManager.selectedEndless()) {
            allyGameData.first()
        } else {
            allyGameData.first { it.id == StoryManager.getSelectStory().id }
        }
    }

    fun Ally.isMaster(): Boolean {
        return if (StoryManager.selectedEndless()) {
            this.uuid == allyGameData.first().uuid
        } else {
            this.id == StoryManager.getSelectStory().id
        }
    }

    fun getGameMasterRole(): Role {
        return getRoleByAlly(getGameMaster())
    }

    fun getGameEquips(): List<Equipment> {
        return equipGameData
    }

    fun haveMoreThan(poolById: Pool): Boolean {
        return false
    }

    fun getAge(): Int {
        return adventureProps.value.age
    }

    fun gainEquip(equipment: Equipment) {
        if (equipGameData.any { it.mainId == equipment.mainId }) {
            val currentLevel = equipGameData.first { it.mainId == equipment.mainId }.star
            val newLevel = currentLevel + 1
            repo.gameCore.getEquipPool()
                .firstOrNull { it.mainId == equipment.mainId && it.star == newLevel }?.let {
                    equipGameData.removeAll { it.mainId == equipment.mainId }
                    equipGameData.add(it)
                }
        } else if (equipGameData.any { it.type == equipment.type }) {
            equipGameData.removeAll { it.type == equipment.type }
            equipGameData.add(equipment)
        } else {
            equipGameData.add(equipment)
        }
    }

    fun getUpgradeSkillPool(): List<Pair<String, Skill>> {
        // 获取所有盟友
        val allies = getGameRoles()
        // 尝试获取所有盟友的兵种技能，过滤掉没有兵种技能的盟友
        val allySkills = allies.mapNotNull { ally ->
            ally.getSkills().firstOrNull { it.isTroopSkill() }?.let { skill ->
                Pair(ally, skill)
            }
        }
        // 可用升级
        val targetSkillPool = allySkills.mapNotNull { (ally, currentSkill) ->
            repo.gameCore.getSkillPool().firstOrNull {
                it.mainId == currentSkill.mainId && it.level == currentSkill.level + 1
            }?.let { upgradedSkill ->
                Pair(ally, upgradedSkill)
            }
        }.shuffled(RANDOM).sortedByDescending { pair ->
            if (pair.first.getAlly().isMaster()) 999 else pair.first.getRace().quality
        }.take(3)

        return targetSkillPool.map { Pair(it.first.extraInfo.allyUuid, it.second) }

    }

    fun updateYou() {
        you.value = you.value.copy(updateId = you.value.updateId + 1)
    }

    suspend fun gainExp(realExp: Int) {
        val oldLevel = Title.getTitleLevel(yourExp.value)
        yourExp.value += realExp
        val newLevel = Title.getTitleLevel(yourExp.value)
        repo.onBattleInfo(
            GameApp.instance.getWrapString(R.string.gain_exp) + realExp,
            BattleInfoType.Battle
        )
        // todo 这个实现有点搓
        adventureSkillTrigger(triggerSkill = GainExp)
        if (newLevel != oldLevel) {
            Dialogs.levelUpDialog.value = true
        }
    }

    fun gainTitle(titleId: Int) {
        yourTitle.value = repo.gameCore.getTitlePool().first { it.id == titleId }
        repo.onBattleInfo(
            GameApp.instance.getWrapString(R.string.gain_position_title) + yourTitle.value.name,
            BattleInfoType.Battle
        )
    }

    suspend fun gainGold(gold: Int) {
        BattleManager.gold.value += gold
        if (BattleManager.gold.value < 0) {
            BattleManager.gold.value = 0
        }
        if (gold > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_gold) + gold, BattleInfoType.ExtraSkill
            )
            adventureSkillTrigger(triggerSkill = GainMoney.copy(num = gold))
        } else if (gold < 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.lose_gold) + gold, BattleInfoType.ExtraSkill
            )
            adventureSkillTrigger(triggerSkill = UseMoney.copy(num = -gold))
        }
    }

    suspend fun gainResource(resource: Int) {
        BattleManager.resource.value += resource
        if (BattleManager.resource.value < 0) {
            BattleManager.resource.value = 0
        }
        if (resource > 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.gain_resource) + resource,
                BattleInfoType.ExtraSkill
            )
            adventureSkillTrigger(triggerSkill = GainResource.copy(num = resource))
        } else if (resource < 0) {
            repo.onBattleInfo(
                GameApp.instance.getWrapString(R.string.lose_resource) + resource,
                BattleInfoType.ExtraSkill
            )
            adventureSkillTrigger(triggerSkill = UseResource.copy(num = -resource))
        }
    }

    fun getAdventureSkillTips(skill: Skill): String {
        return if (you.value.getGraveSkills().any { it.uuid == skill.uuid }) {
            GameApp.instance.getWrapString(R.string.effected)
        } else if (skill.grave != -1) {
            GameApp.instance.getWrapString(R.string.effect_multiple_times)
        } else {
            GameApp.instance.getWrapString(R.string.not_effect)
        }
    }

    fun oneShotSelect(data: SelectAllyData) {
        getBattleAllies().forEach {
            selectAllyToBattle(it.value, -1)
        }
        if (repo.gameMode.value.isNormalMode() && !getGameMaster().isDead()) {
            selectAllyToBattle(
                getGameMaster(),
                ALLY_ROW1_SECOND
            )
        }
        if (data.needGod) {
            godReplacedAlly.value = getBattleAllies()[ALLY_ROW2_THIRD]
            selectAllyToBattle(
                getGameAllies().first { it.temp },
                ALLY_ROW2_THIRD
            )
        }
        val battleAllies = getBattleAllies()
        positionOrderedListAllies.forEach { targetPosition ->
            if (targetPosition !in battleAllies.keys) {
                getGameAllies()
                    .filter { it.battlePosition == -1 }
                    .filter { data.filter(it) }
                    .sortedByDescending { it.star }
                    .sortedByDescending { it.quality }
                    .firstOrNull { !it.isDead() }
                    ?.let {
                        selectAllyToBattle(it, targetPosition)
                    }
            }
        }
    }

    fun canKeepFight(event: Event): Boolean {
        val helperAlive = if (event.play == GOD_BATTLE_PLAY) {
            getGameAllies().any { it.temp }
        } else {
            true
        }
        val singleMasterAlive = if (event.isNeedMaster() && event.isSingle()) {
            getGameAllies().any { it.isMaster() }
        } else {
            true
        }
        return getAliveGameAllies()
            .isNotEmpty() && helperAlive && singleMasterAlive
    }
}

inline fun <T : GameItem> List<T>.indexOfItemInGame(uuid: String, callback: (Int) -> Unit) {
    indexOfFirst { uuid == it.uuid }.takeIf { it != -1 }?.let { index ->
        callback(index)
    }
}
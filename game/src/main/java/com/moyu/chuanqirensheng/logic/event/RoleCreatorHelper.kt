package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.pvp.pvpTalentMainIds
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.logic.skill.ExtraSkillProcessor.doPvpSkillProperty
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Event
import com.moyu.core.model.Race
import com.moyu.core.model.Tower
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role

fun createRole(race: Race, event: Event): Role {
    val difficultProperty = DifficultManager.getSelected().toProperty()
    val diffProperty = event.getDiffProperty()
    return DefaultRoleCreator.create(
        race,
        diffProperty + difficultProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}

fun createTowerRole(race: Race, tower: Tower): Role {
    val diffProperty = tower.getDiffProperty()
    return DefaultRoleCreator.create(
        race,
        diffProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}

fun createTowerRole(race: Race, talents: Map<Int, Int>, heroAdvSkillId: List<Int>): Role {
    var resultProperty = Property()
    talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
        val talentSkill = repo.gameCore.getSkillPool().firstOrNull { it.mainId == mainId && it.level == talents[mainId] }
        talentSkill?.let {
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    heroAdvSkillId.forEach {
        repo.gameCore.getSkillById(it).let { skill ->
            resultProperty += skill.doPvpSkillProperty(race, race.skillId)
        }
    }
    if (race.getAlly().isMaster()) {
        talents.filterKeys { it in pvpTalentMainIds }.keys.forEach { mainId ->
            val talentSkill = repo.gameCore.getSkillPool().first { it.mainId == mainId && it.level == talents[mainId] }
            resultProperty += talentSkill.doPvpSkillProperty(race, race.skillId)
        }
    }
    return DefaultRoleCreator.create(
        race,
        resultProperty,
        emptyList(),
        Identifier.player(name = race.name)
    )
}

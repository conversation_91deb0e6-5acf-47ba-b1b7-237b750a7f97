package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import com.moyu.core.util.chance
import kotlin.math.min

const val BETRAY_ALLY_PLAY = 3

class BetrayAllyPlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = BETRAY_ALLY_PLAY
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TextLabel(Modifier.graphicsLayer {
                translationX = padding42.toPx()
            }, labelSize = LabelSize.Huge, text = event.play.playToName(), extraTextOffset = padding19 + padding19)
            EventAward3To1Layout(
                awards = awards,
                content = { index ->
                    Text(
                        modifier = Modifier.verticalScroll(rememberScrollState()),
                        text = awards[index].allies.first().getRace().story,
                        style = MaterialTheme.typography.h4
                    )
                },
                buttonTexts = List(awards.size) {
                    stringResource(
                        id = R.string.rate,
                        min(
                            100,
                            event.playPara2.first()
                                .toInt() + BattleManager.adventureProps.value.commerce
                        )
                    )
                },
            ) {
                if (!eventFinished.value) {
                    eventFinished.value = true
                    if ((event.playPara2.first()
                            .toInt() + BattleManager.adventureProps.value.commerce).chance()
                    ) {
                        GameApp.instance.getWrapString(R.string.betray_ok).toast()
                        EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                        EventManager.doEventResult(event, true)
                    } else {
                        GameApp.instance.getWrapString(R.string.betray_fail).toast()
                        EventManager.doEventResult(event, true)
                    }
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        awards.addAll(
            repo.gameCore.getPoolById(event.playPara1.first().toInt()).toAwards().shuffled(RANDOM)
                .take(3)
        )
    }
}
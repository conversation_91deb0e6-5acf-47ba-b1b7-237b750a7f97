package com.moyu.chuanqirensheng.logic.skill

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.core.GameCore
import com.moyu.core.logic.info.addExtraSkillInfo
import com.moyu.core.logic.skill.special
import com.moyu.core.model.Award
import com.moyu.core.model.Race
import com.moyu.core.model.getRaceTypeName
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.Property
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import kotlin.math.roundToInt

object ExtraSkillProcessor {
    fun Skill.doPvpSkillProperty(race: Race, groupSkillId: List<Int>): Property {
        val range = this.special()
        var resultProperty = Property()
        for (index in range) {
            resultProperty += when (effectType[index].type) {
                201 -> {
                    Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                }

                in 202..211 -> {
                    val raceIndex = effectType[index].type - 202
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    if (race.raceType == raceIndex + 1) {
                        property
                    } else Property()
                }

                in 25500000..25599999 -> {
                    val skillId = effectType[index].type - 25500000
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    if (groupSkillId.contains(skillId)) {
                        property
                    } else Property()
                }

                else -> {
                    error("未知的额外效果类型：${effectType[index].type}")
                }
            }
        }
        return resultProperty
    }
    suspend fun Skill.doExtraEffect() {
        val range = this.special()
        for (index in range) {
            when (effectType[index].type) {
                101 -> { // 属性永久提高
                    GameCore.instance.addExtraSkillInfo("", this)
                    doPermanent(index, effectNum[index])
                }

                102 -> { // 属性永久降低
                    GameCore.instance.addExtraSkillInfo("", this)
                    doPermanent(index, -effectNum[index])
                }

                201 -> {
                    /**
                     * 所有盟友卡，都提高指定的属性
                     */
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(
                            R.string.extra_skill_buff
                        ) + property.getNonZeroString(), this

                    )
                    BattleManager.gainPermanentProp(property)
                }

                in 202..211 -> {
                    val raceIndex = effectType[index].type - 202
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        (raceIndex + 1).getRaceTypeName() + GameApp.instance.getWrapString(
                            R.string.extra_skill_buff
                        ) + property.getNonZeroString(), this

                    )
                    BattleManager.gainPermanentRaceProp(raceIndex, property)
                }

                in 25500000..25599999 -> {
                    val skillId = effectType[index].type - 25500000
                    val skillName = repo.gameCore.getSkillById(skillId).name
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(
                            R.string.skill_extra_skill_buff, skillName
                        ) + property.getNonZeroString(), this
                    )
                    BattleManager.gainPermanentSkillProp(skillId, property)
                }

                in 25600000..25699999 -> {
                    val skillId = effectType[index].type - 25600000
                    val skillName = repo.gameCore.getSkillById(skillId).name
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        -effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(
                            R.string.skill_extra_skill_debuff, skillName
                        ) + property.getNonZeroString(), this
                    )
                    BattleManager.gainPermanentSkillProp(skillId, property)
                }

                212 -> {
                    /**
                     * 所有盟友卡，都降低指定的属性
                     */
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        -effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(
                            R.string.extra_skill_debuff
                        ) + property.getNonZeroString(), this
                    )
                    BattleManager.gainPermanentProp(property)
                }

                in 213..223 -> {
                    val race = effectType[index].type - 213
                    val property = Property.getPropertyByEnum(
                        subType[index],
                        -effectNum[index]
                    )
                    GameCore.instance.addExtraSkillInfo(
                        race.getRaceTypeName() + GameApp.instance.getWrapString(
                            R.string.extra_skill_debuff
                        ), this
                    )
                    BattleManager.gainPermanentRaceProp(race, property)
                }

                253 -> {
                    // 主角卡的战斗属性，提高指定的属性
                    val property = Property.getPropertyByEnum(subType[index], effectNum[index])
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.master_extra_skill_buff),
                        this
                    )
                    val award = Award(battleProperty = property)
                    AwardManager.gainAward(award)
                }

                254 -> {
                    // 主角卡的战斗属性，降低指定的属性
                    val property = Property.getPropertyByEnum(subType[index], -effectNum[index])
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.master_extra_skill_debuff),
                        this
                    )
                    val award = Award(battleProperty = property)
                    AwardManager.gainAward(-award)
                }

                300 -> { // 获取道具
                    // todo num要取技能指定的nums
                    GameCore.instance.addExtraSkillInfo("", this)
                    val award = repo.gameCore.getPoolById(subType[index])
                        .toAward(forceNum = effectNum[index].toInt())
                    AwardManager.gainAward(award)
                }

                400 -> { // 获取道具
                    GameCore.instance.addExtraSkillInfo("", this)
                    val award = repo.gameCore.getPoolById(subType[index]).toAwards(
                        forceNum = effectNum[index].toInt(),
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData },
                    ).toAward()
                    AwardManager.gainAward(-award)
                }

                602 -> {
                    if (subType[index] != 5) {
                        error("602的subType只支持5")
                    }
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.mercenary_price_down) + effectNum[index].toInt() + "。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            shopPriceMercenary = effectNum[index].toInt()
                        )
                    )
                }

                701 -> {
                    //指定事件必定成功（effectNum填写play）
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.event_sure_win) +"。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            winEventPlayIds = listOf(
                                subType[index]
                            )
                        )
                    )
                }

                702 -> {
                    //指定事件必定失败（effectNum填写play）
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.event_sure_fail) + effectNum[index].toInt() + "。",
                        this
                    )
                    BattleManager.gainAdventureProp(
                        AdventureProps(
                            failEventPlayIds = listOf(
                                effectNum[index].toInt()
                            )
                        )
                    )
                }

                703 -> {
                    //复活随机1个阵亡的军团卡
                    BattleManager.getGameAllies().filter { it.isDead() }.shuffled(RANDOM)
                        .firstOrNull()?.let {
                            GameCore.instance.addExtraSkillInfo(
                                GameApp.instance.getWrapString(R.string.relive_ally) + it.name + "。",
                                this
                            )
                            BattleManager.reliveAllyInGame(it)
                        } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_dead_ally), this
                        )
                    }
                }

                704 -> {
                    //恢复x个军团卡到满血（x=0表示所有军团卡恢复到满血，否则优先选择未满血的军团卡进行恢复）
                    val hurts = BattleManager.getGameAllies().filter { it.isHurt() }
                    val targets = hurts.shuffled(RANDOM).take(1)
                    targets.takeIf { it.isNotEmpty() }?.forEach {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.heal_ally, it.name, 100),
                            this
                        )
                        BattleManager.reliveAllyInGame(it)
                    } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_hurt_ally), this
                        )
                    }
                }


                705 -> {
                    //掉血x个军团卡（x=0表示所有军团）
                    val hurts = BattleManager.getGameAllies().filter { !it.isDead() }
                    val percent = effectNum[index].toInt()
                    val race = subType[index]
                    hurts.filter { it.getRaceType() == race || race == 0 }
                        .takeIf { it.isNotEmpty() }?.forEach {
                            GameCore.instance.addExtraSkillInfo(
                                GameApp.instance.getWrapString(R.string.hurt_ally, it.name),
                                this
                            )
                            BattleManager.hurtAllyInGame(it, percent)
                        } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_alive_ally), this
                        )
                    }
                }

                708 -> {
                    //恢复所有盟友卡的生命上限百分比
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(R.string.heal_ally_all) + effectNum[index].toInt(),
                        this
                    )
                    BattleManager.healBattleAllyInGame(effectNum[index].toInt())
                }

                709 -> {
                    //恢复主角卡的生命上限百分比
                    val it = BattleManager.getGameMaster()
                    GameCore.instance.addExtraSkillInfo(
                        GameApp.instance.getWrapString(
                            R.string.heal_ally,
                            it.name,
                            effectNum[index].toInt()
                        ),
                        this
                    )
                    BattleManager.healOneAllyInGame(
                        BattleManager.getGameMaster(),
                        effectNum[index].toInt()
                    )
                }

                710 -> {
                    // 恢复盟友卡的生命上限百分比
                    val hurts =
                        BattleManager.getGameAllies().filter { it.isHurt() || it.isMaster() }.sortedBy { it.gameHp }
                            .take(subType[index])
                    hurts.takeIf { it.isNotEmpty() }?.forEach { hurt ->
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(
                                R.string.heal_ally,
                                hurt.name,
                                effectNum[index].toInt()
                            ),
                            this
                        )
                        BattleManager.healOneAllyInGame(hurt, effectNum[index].toInt())
                    } ?: kotlin.run {
                        GameCore.instance.addExtraSkillInfo(
                            GameApp.instance.getWrapString(R.string.no_hurt_ally), this
                        )
                    }
                }

                else -> {
                    error("未知的额外效果类型：${effectType[index].type}")
                }
            }
        }
    }

    private suspend fun Skill.doPermanent(index: Int, diff: Double) {
        when (subType[index]) {
            1 -> {
                BattleManager.gainAdventureProp(AdventureProps(science = diff.roundToInt()))
            }

            2 -> {
                BattleManager.gainAdventureProp(AdventureProps(politics = diff.roundToInt()))
            }

            3 -> {
                BattleManager.gainAdventureProp(AdventureProps(military = diff.roundToInt()))
            }

            4 -> {
                BattleManager.gainAdventureProp(AdventureProps(religion = diff.roundToInt()))
            }

            5 -> {
                BattleManager.gainAdventureProp(AdventureProps(commerce = diff.roundToInt()))
            }

            else -> error("不存在Skill.doPermanent")
        }
    }
}
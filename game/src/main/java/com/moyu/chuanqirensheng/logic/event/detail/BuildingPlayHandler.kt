package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

const val BUILDING_PLAY = 14

class BuildingPlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = BUILDING_PLAY
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    val contentList = listOf(
        GameApp.instance.getWrapString(R.string.play_name14_content_1),
        GameApp.instance.getWrapString(R.string.play_name14_content_2),
        GameApp.instance.getWrapString(R.string.play_name14_content_3),
    )
    val buttonList = listOf(
        GameApp.instance.getWrapString(R.string.play_name14_result_1),
        GameApp.instance.getWrapString(R.string.play_name14_result_2),
        GameApp.instance.getWrapString(R.string.play_name14_result_3),
    )

    val icons = listOf(
        R.drawable.play_14_reuslt1,
        R.drawable.play_14_reuslt2,
        R.drawable.play_14_reuslt3,
    )
    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TextLabel(Modifier.graphicsLayer {
                translationX = padding42.toPx()
            }, labelSize = LabelSize.Huge, text = event.play.playToName(), extraTextOffset = padding19 + padding19)
            EventAward3To1Layout(
                awards = awards,
                replaceIcons = icons,
                replaceTexts = buttonList,
                content = { index ->
                    Text(
                        modifier = Modifier.verticalScroll(rememberScrollState()),
                        text = contentList[index],
                        style = MaterialTheme.typography.h4
                    )
                },
                buttonTexts = List(3) {
                    stringResource(id = R.string.do_select)
                },
            ) {
                if (!eventFinished.value) {
                    eventFinished.value = true
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                        EventManager.doEventResult(event, true)
                        DetailProgressManager.buildingDecision(it + 1)
                    }
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        awards.addAll(
            event.playPara1.map {
                repo.gameCore.getPoolById(it).toAward()
            }
        )
    }
}

package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.getAwardDesc
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM

const val HIRE_ALLY_PLAY = 2

class HireAllyPlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = HIRE_ALLY_PLAY
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TextLabel(Modifier.graphicsLayer {
                translationX = padding42.toPx()
            }, labelSize = LabelSize.Huge, text = event.play.playToName(), extraTextOffset = padding19 + padding19)
            EventAward3To1Layout(awards,
                content = { index ->
                    Text(
                        modifier = Modifier.verticalScroll(rememberScrollState()),
                        text = awards[index].getAwardDesc(),
                        style = MaterialTheme.typography.h4
                    )
                }) {
                if (!eventFinished.value) {
                    eventFinished.value = true
                    EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                    EventManager.doEventResult(event, true)
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        awards.addAll(
            repo.gameCore.getPoolById(event.playPara1.first()).toAwards().shuffled(RANDOM).take(3)
        )
    }
}
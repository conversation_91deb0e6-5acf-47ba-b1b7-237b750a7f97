package com.moyu.chuanqirensheng.logic.judge

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.feature.quest.onTaskKillEnemy
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.core.GameCore
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlin.math.min

class GameJudgeManager : GameJudge {
    override val inBattle = mutableStateOf(false)
    override val gameOver = mutableStateOf(false)

    override fun onGameWin() {
        if (!repo.gameMode.value.isPvpMode()) {
            setIntValueByKey(KEY_DIED_IN_GAME, 0)
        }

        gameOver.value = true
        inBattle.value = false
        GameApp.instance.getWrapString(R.string.congratulations).toast()
        goto(LOGIN_SCREEN)
        playerMusicByScreen() // 音乐
    }

    override fun onGameOver(ending: Ending?) {
        if (!repo.gameMode.value.isPvpMode()) {
            setIntValueByKey(KEY_DIED_IN_GAME, 0)
        }

        gameOver.value = true
        inBattle.value = false
        ContinueManager.clearSave()
        goto(LOGIN_SCREEN)
        playerMusicByScreen() // 音乐
    }

    override fun onBattleWin(allies: List<Role>, enemies: List<Role>) {
        updateAlliesAfterBattle(allies)
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        GameApp.globalScope.launch(gameDispatcher) {
            onTaskKillEnemy(enemies)
            Dialogs.gameWinDialog.value = allies + enemies
            restartEffect(dialogEffectState, winBattleEffect)
        }
        ReportManager.battle(1, 0, BattleManager.getAge())
    }

    override fun onBattleLose(allies: List<Role>, enemies: List<Role>) {
        increaseIntValueByKey(KEY_DIED_IN_GAME)
        updateAlliesAfterBattle(allies)
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
        ReportManager.battle(0, 0, BattleManager.getAge())
    }

    private fun updateAlliesAfterBattle(allies: List<Role>) {
        GameApp.globalScope.launch(Dispatchers.Main) {
            allies.forEach {
                BattleManager.updateAllyInGameById(
                    it,
                    hp = min(it.getOriginProperty().hp, it.getCurrentProperty().hp)
                )
            }
            BattleManager.checkAnyAllyDied()
        }
    }
}
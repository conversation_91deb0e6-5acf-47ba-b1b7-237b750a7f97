package com.moyu.chuanqirensheng.logic

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.text.playNameMap
import com.moyu.chuanqirensheng.text.playRuleMap
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.model.Ally
import com.moyu.core.model.Award
import com.moyu.core.model.Sell
import com.moyu.core.model.Skin

fun Sell.getPriceText(): String {
    return if (isAifadian()) {
        if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) price.toString() else priceDollar.toString()
    } else price.toString()
}

fun Sell.getPriceTextWithUnit(): String {
    return if (isAifadian()) {
        if (!GameApp.instance.resources.getBoolean(R.bool.has_google_service)) price.toString() + GameApp.instance.getWrapString(R.string.real_money_unit) else priceDollar.toString() + GameApp.instance.getWrapString(R.string.real_money_dollar)
    } else price.toString()
}

fun Int.unlockIdIsStory():Boolean {
    return this in repo.gameCore.getStoryPool().map { it.unlockId }
}

fun Award.getAwardDesc(): String {
    return if (titleId != 0) {
        repo.gameCore.getTitlePool().first { it.id == titleId }.desc
    } else if (gold != 0) {
         GameApp.instance.getWrapString(R.string.play_content_1)
    } else if (resource != 0) {
        GameApp.instance.getWrapString(R.string.play_content_2)
    } else if (exp != 0) {
        GameApp.instance.getWrapString(R.string.play_content_3)
    } else if (reputations.any { it != 0 }) {
        GameApp.instance.getWrapString(R.string.play_content_4)
    } else if (advProperty.getPropertyByTarget(1) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_5)
    } else if (advProperty.getPropertyByTarget(2) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_6)
    } else if (advProperty.getPropertyByTarget(3) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_7)
    } else if (advProperty.getPropertyByTarget(4) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_8)
    } else if (advProperty.getPropertyByTarget(5) != 0) {
        GameApp.instance.getWrapString(R.string.play_content_9)
    } else if (battleProperty.getPropertyByTarget(1) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_10)
    } else if (battleProperty.getPropertyByTarget(2) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_11)
    } else if (battleProperty.getPropertyByTarget(3) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_12)
    } else if (battleProperty.getPropertyByTarget(4) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_13)
    } else if (battleProperty.getPropertyByTarget(5) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_14)
    } else if (battleProperty.getPropertyByTarget(6) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_15)
    } else if (battleProperty.getPropertyByTarget(7) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_16)
    } else if (battleProperty.getPropertyByTarget(8) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_17)
    } else if (battleProperty.getPropertyByTarget(9) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_18)
    } else if (battleProperty.getPropertyByTarget(10) != 0.0) {
        GameApp.instance.getWrapString(R.string.play_content_19)
    } else if (allies.isNotEmpty()) {
        allies.first().getRace().story
    } else if (skills.isNotEmpty()) {
        skills.first().getRealDesc()
    } else ""
}

fun Skin.getStoryName(): String {
    return GameCore.instance.getStoryPool()
        .first { it.id == type }.name.dropLast(1) + GameApp.instance.getWrapString(R.string.skin)
}

fun Int.getQualityName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.blue_quality)
        2 -> GameApp.instance.getWrapString(R.string.purple_quality)
        else -> GameApp.instance.getWrapString(R.string.orange_quality)
    }
}

fun Int.getQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.item2_quality_1
        2 -> R.drawable.item2_quality_2
        3 -> R.drawable.item2_quality_3
        else -> R.drawable.item2_quality_0
    }
}

fun Int.getQualityFrameCircle(): Int {
    return when (this) {
        1 -> R.drawable.hero_frame2_green
        2 -> R.drawable.hero_frame2_blue
        3 -> R.drawable.hero_frame2_orange
        else -> R.drawable.hero_frame2
    }
}

fun Ally.canStarUp(): Boolean {
    val maxStar = star >= starLimit
    return starUpNum != 0 && num - 1 >= starUpNum && !maxStar && AwardManager.diamond.value >= starUpRes
}

fun Ally.getTouchInfo(): String {
    return "$star" + GameApp.instance.getWrapString(R.string.star) + quality.getQualityName()
        .take(2) + name + GameApp.instance.getWrapString(
        R.string.number
    ) + num + "，" + getRaceTips().take(2)
}

fun Ally.getRaceTips(): String {
    return when (type) {
        1 -> GameApp.instance.getWrapString(R.string.race_desc_1)
        2 -> GameApp.instance.getWrapString(R.string.race_desc_2)
        3 -> GameApp.instance.getWrapString(R.string.race_desc_3)
        4 -> GameApp.instance.getWrapString(R.string.race_desc_4)
        5 -> GameApp.instance.getWrapString(R.string.race_desc_5)
        6 -> GameApp.instance.getWrapString(R.string.race_desc_6)
        7 -> GameApp.instance.getWrapString(R.string.race_desc_7)
        8 -> GameApp.instance.getWrapString(R.string.race_desc_8)
        else -> GameApp.instance.getWrapString(R.string.race_desc_9)
    }
}

fun Ally.getFrameDrawable(): Int {
    return quality.getQualityFrame()
}

fun Int.getEquipTypeTips(): String {
    return when (this) {
        1 -> AppWrapper.getString(R.string.itemtype_desc_1)
        2 -> AppWrapper.getString(R.string.itemtype_desc_2)
        3 -> AppWrapper.getString(R.string.itemtype_desc_3)
        4 -> AppWrapper.getString(R.string.itemtype_desc_4)
        5 -> AppWrapper.getString(R.string.itemtype_desc_5)
        6 -> AppWrapper.getString(R.string.itemtype_desc_6)
        else -> ""
    }
}

fun Int.toJinNangElementName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.jinnang_element1)
        2 -> GameApp.instance.getWrapString(R.string.jinnang_element2)
        3 -> GameApp.instance.getWrapString(R.string.jinnang_element3)
        4 -> GameApp.instance.getWrapString(R.string.jinnang_element4)
        5 -> GameApp.instance.getWrapString(R.string.jinnang_element5)
        6 -> GameApp.instance.getWrapString(R.string.jinnang_element6)
        else -> ""
    }
}

fun Int.toPolicyElementName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.policy_element1)
        2 -> GameApp.instance.getWrapString(R.string.policy_element2)
        3 -> GameApp.instance.getWrapString(R.string.policy_element3)
        4 -> GameApp.instance.getWrapString(R.string.policy_element4)
        5 -> GameApp.instance.getWrapString(R.string.policy_element5)
        else -> ""
    }
}

fun Int.toEquipTypeRes() = getImageResourceDrawable("equipment_type${this}")

fun Int.playToName(): String {
    return playNameMap[this] ?: ""
}

fun Int.playToTips(): String {
    return playRuleMap[this] ?: ""
}

fun Int.skillTag(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.skill_tag1)
        2 -> GameApp.instance.getWrapString(R.string.skill_tag2)
        3 -> GameApp.instance.getWrapString(R.string.skill_tag3)
        4 -> GameApp.instance.getWrapString(R.string.skill_tag4)
        5 -> GameApp.instance.getWrapString(R.string.skill_tag5)
        6 -> GameApp.instance.getWrapString(R.string.skill_tag6)
        7 -> GameApp.instance.getWrapString(R.string.skill_tag7)
        8 -> GameApp.instance.getWrapString(R.string.skill_tag8)
        9 -> GameApp.instance.getWrapString(R.string.skill_tag9)
        10 -> GameApp.instance.getWrapString(R.string.skill_tag10)
        11 -> GameApp.instance.getWrapString(R.string.skill_tag11)
        12 -> GameApp.instance.getWrapString(R.string.skill_tag12)
        13 -> GameApp.instance.getWrapString(R.string.skill_tag13)
        14 -> GameApp.instance.getWrapString(R.string.skill_tag14)
        15 -> GameApp.instance.getWrapString(R.string.skill_tag15)
        16 -> GameApp.instance.getWrapString(R.string.skill_tag16)
        else -> GameApp.instance.getWrapString(R.string.skill_tag17)
    }
}
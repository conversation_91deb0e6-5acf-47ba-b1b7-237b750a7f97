package com.moyu.chuanqirensheng.logic.award

import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.ending.Ending
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.core.GameCore
import com.moyu.core.model.Award
import com.moyu.core.model.Event
import com.moyu.core.model.Mission
import com.moyu.core.model.Pool
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.toAward
import com.moyu.core.model.toAwards
import com.moyu.core.util.RANDOM
import kotlin.math.abs

fun Mission.toAward(): Award {
    return repo.gameCore.getPoolById(reward).toAward()
}

fun Ending.toAward(): Award {
    val level = repo.gameCore.getEndingAwardLevel(age)
    return (if (level != -1) {
        // 钻石
        val difficult = DifficultManager.getSelected().id
        val diamond = repo.gameCore.getEndingAwardDiamond(difficult, level)
        val greenAllyNum = repo.gameCore.getGreenAllyNum(difficult, level)
        val blueAllyNum = repo.gameCore.getBlueAllyNum(difficult, level)
        val orangeAllyNum = repo.gameCore.getOrangeAllyNum(difficult, level)
        val allies = repo.gameCore.getGreenAllyPool().shuffled(
            RANDOM).take(greenAllyNum) + repo.gameCore.getBlueAllyPool().shuffled(
            RANDOM).take(blueAllyNum) + repo.gameCore.getOrangeAllyPool().shuffled(
            RANDOM).take(orangeAllyNum)
        Award(
            diamond = diamond,
            outAllies = allies.map {
                repo.gameCore.getAllyById(it)
            }
        )
    } else Award()) + (if (!getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
        repo.gameCore.getPoolById(repo.gameCore.getFirstEndingAwardPoolId()).toAward()
    } else {
        Award()
    })
}

fun Event.toConditionAward(): Award {
    return GameCore.instance.getPoolById(condition).toAward().let {
        getRealRequireByDifficult(it)
    }
}

fun getRealRequireByDifficult(it: Award): Award {
    val difficultLevel = DifficultManager.getSelected()
    var resultProperty = it
    // 难度影响条件
    if (it.advProperty.science > 0) {
        resultProperty += Award(advProperty = AdventureProps(science = difficultLevel.adv1))
    }
    if (it.advProperty.politics > 0) {
        resultProperty += Award(advProperty = AdventureProps(politics = difficultLevel.adv2))
    }
    if (it.advProperty.military > 0) {
        resultProperty += Award(advProperty = AdventureProps(military = difficultLevel.adv3))
    }
    if (it.advProperty.religion > 0) {
        resultProperty += Award(advProperty = AdventureProps(religion = difficultLevel.adv4))
    }
    if (it.advProperty.commerce > 0) {
        resultProperty += Award(advProperty = AdventureProps(commerce = difficultLevel.adv5))
    }
    if (it.gold > 0) {
        resultProperty += Award(gold = difficultLevel.resource1)
    }
    if (it.resource > 0) {
        resultProperty += Award(resource = + difficultLevel.resource2)
    }
    return resultProperty
}

fun Event.toAward(win: Boolean): Award {
    return toAwards(win).toAward()
}

fun Event.toAwards(win: Boolean): List<Award> {
    return try {
        if (win) winReward.map { GameCore.instance.getPoolById(it).toAward() }
        else loseReward.map {
            // 这里要小心处理
            val pool = GameCore.instance.getPoolById(it)
            // 这里是局内event可能要丢弃卡牌，我先分割pool为单个pool，一个一个算
            val pools = pool.split()
            val awards = pools.map { singlePool ->
                if (singlePool.singlePoolNeedLoseCard()) {
                    // 如果是需要丢弃卡牌，把数值改正
                    val award = singlePool.copy(num = listOf(abs(singlePool.num.first()))).toAwards(
                        onLoseAlly = { BattleManager.allyGameData },
                        onLoseSkill = { BattleManager.skillGameData },
                    ).toAward()
                    // 转负
                    -award
                } else {
                    singlePool.toAward()
                }
            }
            awards.toAward()
        }
    } catch (e: Exception) {
        emptyList()
    }
}

// todo 太麻烦了，这里是局内可能失去的卡牌枚举
fun Int.isInGameCard(): Boolean {
    return this == 1 || this == 2 || this == 3 || this == 4 || this == 11
}

fun Pool.singlePoolNeedLoseCard(): Boolean {
    return type.first().isInGameCard() && this.num.first() < 0
}

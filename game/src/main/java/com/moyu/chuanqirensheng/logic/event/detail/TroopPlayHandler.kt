package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.playToName
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.core.model.Award
import com.moyu.core.model.Event

const val TROOP_PLAY = 7

class TroopUpgradePlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = TROOP_PLAY
) : PlayHandler() {

    private val awards = mutableStateListOf<Award>()

    @Composable
    override fun Layout(event: Event) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            TextLabel(Modifier.graphicsLayer {
                translationX = padding42.toPx()
            }, labelSize = LabelSize.Huge, text = event.play.playToName(), extraTextOffset = padding19 + padding19)
            EventAward3To1Layout(
                awards,
                content = { index ->
                    Column(
                        modifier = Modifier.fillMaxSize()
                            .verticalScroll(
                                rememberScrollState()
                            ),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        val skill = awards[index].upgradeSkills.first().second
                        skill.getPrevSkill()?.let {
                            Text(
                                text = it.name + "->" + skill.name,
                                style = MaterialTheme.typography.h6,
                                color = DarkGreen
                            )
                            Spacer(modifier = Modifier.size(padding10))
                        }
                        Text(
                            text = awards[index].upgradeSkills.first().second.getRealDescColorful(),
                            style = MaterialTheme.typography.h4
                        )
                    }
                },
            ) {
                if (!eventFinished.value) {
                    eventFinished.value = true
                    EventManager.getOrCreateHandler(event).setEventAward(awards[it])
                    EventManager.doEventResult(event, true)
                }
            }
            Spacer(modifier = Modifier.size(padding30))
            if (awards.isEmpty()){
                GameButton(text = stringResource(id = R.string.quit)) {
                    if (!eventFinished.value) {
                        eventFinished.value = true
                        EventManager.doEventResult(event, true)
                    }
                }
            }
        }
    }

    override fun onEventSelect(event: Event) {
        eventFinished.value = false
        eventResult.value = true
        awards.clear()
        val targetSkillPool = BattleManager.getUpgradeSkillPool()
        awards.addAll(
            targetSkillPool.map {
                Award(upgradeSkills = listOf(it))
            }
        )
    }
}

package com.moyu.chuanqirensheng.logic.skill

import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.logic.getQualityName
import com.moyu.chuanqirensheng.logic.toJinNangElementName
import com.moyu.chuanqirensheng.logic.toPolicyElementName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_TEXT_FIXED_COLOR
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.getTextColor
import com.moyu.core.logic.info.getElementTypeName
import com.moyu.core.logic.skill.getRealDesc
import com.moyu.core.logic.skill.quality
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isJinNang
import com.moyu.core.model.skill.isZhenLing

fun Skill.getBattleTreeLevelInfo(): String {
    return GameApp.instance.getWrapString(
        R.string.tree_level_info,
        repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.mainName ?: name,
        level
    )
}

fun Skill.getTouchInfo(): String {
    return if (isJinNang()) {
        elementType.toJinNangElementName() + name
    } else if (isZhenLing()) {
        elementType.toPolicyElementName() + name
    } else {
        "$level" + GameApp.instance.getWrapString(R.string.star) + quality().getQualityName()
            .take(2) + name + "，" + elementType.getElementTypeName() + "，" + GameApp.instance.getWrapString(
            R.string.number
        ) + num
    }
}

fun Skill.canStarUp(): Boolean {
    return !repo.inGame.value && repo.gameCore.getSkillPool()
        .filter { it.mainId == mainId }.size > 1
}

fun Skill.getFrameDrawable(): Int {
    return repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.quality?.getQualityFrame()
        ?: (2.getQualityFrame())
}

fun Skill.story(): String {
    return repo.gameCore.getScrollPool().firstOrNull { it.id == id }?.story ?: "0"
}

private val damageDisplayList = DamageType.entries.map { it.display }
private val damageDefenseList = DamageType.entries.map { it.getDefenseName() }

// 正则表达式 匹配中括号、【】括号 以及 damageTypeDisplayList
private val regexStr =
    """(?<=\[)(.+?)(?=])|(?<=【)(.+?)(?=】)|""" + damageDisplayList.joinToString("|") + "|" + damageDefenseList.joinToString(
        "|"
    ) + "|" + ReputationManager.reputations.mapIndexed { index, _ -> ReputationManager.getName(index + 1) }
        .joinToString("|") + "|" + GameApp.instance.getWrapString(R.string.gold) + "|" + GameApp.instance.getWrapString(
        R.string.resource
    )

private val regexEventStr =
    """(?<=\[)(.+?)(?=])|(?<=【)(.+?)(?=】)|"""


@Composable
fun Skill.getRealDescColorful(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    return getRealDesc().toSkillAnnotatedString(spanStyle = spanStyle)
}


@Composable
fun String.toAnnotatedEventText(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) {
        return buildAnnotatedString { append(this@toAnnotatedEventText) }
    }
    return buildAnnotatedString {
        append(this@toAnnotatedEventText)
        Regex(regexEventStr).findAll(this@toAnnotatedEventText).forEach { matchResult: MatchResult ->
            addStyle(
                style = spanStyle.copy(DarkGreen),
                start = matchResult.range.first,
                end = matchResult.range.last + 1
            )
        }
    }
}


@Composable
fun String.toSkillAnnotatedString(
    spanStyle: SpanStyle = MaterialTheme.typography.h4.toSpanStyle(),
): AnnotatedString {
    if (getBooleanFlowByKey(KEY_TEXT_FIXED_COLOR)) {
        return buildAnnotatedString { append(this@toSkillAnnotatedString) }
    }
    return buildAnnotatedString {
        append(this@toSkillAnnotatedString)
        Regex(regexStr).findAll(this@toSkillAnnotatedString).forEach { matchResult: MatchResult ->
            if (damageDisplayList.any { damageDisplay -> damageDisplay == matchResult.value }) {
                addStyle(
                    style = spanStyle.copy(
                        color = DamageType.fromDisplayValue(matchResult.value)?.getTextColor()
                            ?: Color.White
                    ), start = matchResult.range.first, end = matchResult.range.last + 1
                )
            } else {
                addStyle(
                    style = spanStyle.copy(DarkGreen),
                    start = matchResult.range.first,
                    end = matchResult.range.last + 1
                )
            }
        }
    }
}
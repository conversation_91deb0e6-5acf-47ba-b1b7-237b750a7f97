package com.moyu.chuanqirensheng.api

import android.os.SystemClock
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardFromServer
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.sub.loginsdk.LoginData
import com.moyu.chuanqirensheng.sub.loginsdk.LoginUser
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.core.model.Award


object RetrofitModel {
    suspend fun uploadLikedData(likedData: LikedData) {
        doEncodedApiNoRandomCheck(
            data = likedData,
            inputSerializer = LikedData.serializer(),
        ) {
            uploadLikedData(it)
        }
    }

    suspend fun useShareCode(
        userId: String, codes: String, encryptedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = useShareCode(codes, encryptedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward()
            } ?: Award(valid = false)
        } catch (e: Exception) {
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Award(valid = false)
        }
    }

    suspend fun getCodeAwards(
        userId: String, codes: String, encodedUserId: String, versionCode: Int
    ): Award {
        return try {
            val awardString = getAwards(codes, encodedUserId, versionCode)
            val codeAwardJson = AESUtil.decrypt(awardString, userId)
            codeAwardJson?.let {
                json.decodeFromString(AwardFromServer.serializer(), it).toAward().apply {
                    // 保存解锁信息
                    unlockList.map { id ->
                        UnlockManager.unlockCode(id)
                    }
                }
            } ?: Award(valid = false)
        } catch (e: Exception) {
            GameApp.instance.getWrapString(R.string.network_error).toast()
            Award(valid = false)
        }
    }

    suspend fun getLoginData() {
        val result = doEncodedApi(
            data = GameApp.instance.getLoginUser(),
            inputSerializer = LoginUser.serializer(),
            outputSerializer = LoginData.serializer()
        ) {
            getLoginData(it)
        } ?: LoginData(0, verified = false, showDialog = true)

        GameApp.instance.lastNetWorkTime.value = result.time
        GameApp.instance.elapsedDiffTime = SystemClock.elapsedRealtime() - GameApp.instance.lastNetWorkTime.value
        if (result.needPostAntiCheat && VipManager.isCheat()) {
            GameApp.instance.loginData.value = result.copy(
                verified = false,
                showDialog = true,
                dialogText = GameApp.instance.getWrapString(R.string.login_error_tips)
            )
        } else {
            GameApp.instance.loginData.value = result
        }
        ReportManager.onLogin()
    }
}
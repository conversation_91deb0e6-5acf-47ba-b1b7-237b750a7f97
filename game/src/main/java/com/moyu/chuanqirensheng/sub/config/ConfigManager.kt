package com.moyu.chuanqirensheng.sub.config

import com.moyu.core.config.ALLY_FILE_NAME
import com.moyu.core.config.AllyConfigParser
import com.moyu.core.config.BADGE_FILE_NAME
import com.moyu.core.config.BUFF_FILE_NAME
import com.moyu.core.config.BadgeConfigParser
import com.moyu.core.config.BattlePassConfigParser
import com.moyu.core.config.BuffConfigParser
import com.moyu.core.config.COMBINEDBUFF_FILE_NAME
import com.moyu.core.config.COMMON_FILE_NAME
import com.moyu.core.config.CommonConfigParser
import com.moyu.core.config.ConfigHolder
import com.moyu.core.config.DAY_REWARD_FILE_NAME
import com.moyu.core.config.DIALOG_FILE_NAME
import com.moyu.core.config.DIFFICULT_FILE_NAME
import com.moyu.core.config.DRAW_FILE_NAME
import com.moyu.core.config.DayRewardConfigParser
import com.moyu.core.config.DialogConfigParser
import com.moyu.core.config.DifficultConfigParser
import com.moyu.core.config.DrawItemConfigParser
import com.moyu.core.config.EQUIPMENT_FILE_NAME
import com.moyu.core.config.EVENT_FILE_NAME
import com.moyu.core.config.EquipmentConfigParser
import com.moyu.core.config.EventConfigParser
import com.moyu.core.config.GIFT_FILE_NAME
import com.moyu.core.config.GiftConfigParser
import com.moyu.core.config.MISSION_FILE_NAME
import com.moyu.core.config.MissionConfigParser
import com.moyu.core.config.POOL_FILE_NAME
import com.moyu.core.config.POSITION_FILE_NAME
import com.moyu.core.config.PVP_FILE_NAME
import com.moyu.core.config.PoolConfigParser
import com.moyu.core.config.PvpConfigParser
import com.moyu.core.config.QuestConfigParser
import com.moyu.core.config.RACE_FILE_NAME
import com.moyu.core.config.REPUTATION_LEVEL_FILE_NAME
import com.moyu.core.config.RaceConfigParser
import com.moyu.core.config.ReputationConfigParser
import com.moyu.core.config.SCROLL_FILE_NAME
import com.moyu.core.config.SELL_FILE_NAME
import com.moyu.core.config.SIGN_FILE_NAME
import com.moyu.core.config.SKILL_FILE_NAME
import com.moyu.core.config.SKIN_FILE_NAME
import com.moyu.core.config.STORY_FILE_NAME
import com.moyu.core.config.ScrollConfigParser
import com.moyu.core.config.SellConfigParser
import com.moyu.core.config.SignConfigParser
import com.moyu.core.config.SkillConfigParser
import com.moyu.core.config.SkinConfigParser
import com.moyu.core.config.StoryConfigParser
import com.moyu.core.config.TALENT_FILE_NAME
import com.moyu.core.config.TASK_FILE_NAME
import com.moyu.core.config.TCG_AWARD_FILE_NAME
import com.moyu.core.config.TCG_CARD_FILE_NAME
import com.moyu.core.config.TCG_CARD_TYPE_FILE_NAME
import com.moyu.core.config.TOWER_FILE_NAME
import com.moyu.core.config.TURNTABLE_FILE_NAME
import com.moyu.core.config.TalentConfigParser
import com.moyu.core.config.TcgAwardConfigParser
import com.moyu.core.config.TcgCardConfigParser
import com.moyu.core.config.TcgCardTypeConfigParser
import com.moyu.core.config.TitleConfigParser
import com.moyu.core.config.TowerConfigParser
import com.moyu.core.config.TurnTableConfigParser
import com.moyu.core.config.UNLOCK_FILE_NAME
import com.moyu.core.config.UnlockConfigParser
import com.moyu.core.config.VIP_FILE_NAME
import com.moyu.core.config.VipConfigParser
import com.moyu.core.config.WAR_PASS2_FILE_NAME
import com.moyu.core.config.WAR_PASS_FILE_NAME
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext

object ConfigManager {
    val configLoaders = listOf(
        ConfigLoader(BUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(SKILL_FILE_NAME, SkillConfigParser(), false),
        ConfigLoader(RACE_FILE_NAME, RaceConfigParser()),
        ConfigLoader(EQUIPMENT_FILE_NAME, EquipmentConfigParser()),
        ConfigLoader(SKIN_FILE_NAME, SkinConfigParser()),
        ConfigLoader(REPUTATION_LEVEL_FILE_NAME, ReputationConfigParser()),
        ConfigLoader(COMMON_FILE_NAME, CommonConfigParser()),
        ConfigLoader(TALENT_FILE_NAME, TalentConfigParser()),
        ConfigLoader(SELL_FILE_NAME, SellConfigParser(), true),
        ConfigLoader(TASK_FILE_NAME, QuestConfigParser()),
        ConfigLoader(UNLOCK_FILE_NAME, UnlockConfigParser()),
        ConfigLoader(TCG_CARD_TYPE_FILE_NAME, TcgCardTypeConfigParser()),
        ConfigLoader(TCG_CARD_FILE_NAME, TcgCardConfigParser()),
        ConfigLoader(TCG_AWARD_FILE_NAME, TcgAwardConfigParser()),
        ConfigLoader(SCROLL_FILE_NAME, ScrollConfigParser()),
        ConfigLoader(ALLY_FILE_NAME, AllyConfigParser()),
        ConfigLoader(DIFFICULT_FILE_NAME, DifficultConfigParser()),
        ConfigLoader(DIALOG_FILE_NAME, DialogConfigParser()),
        ConfigLoader(EVENT_FILE_NAME, EventConfigParser()),
        ConfigLoader(POOL_FILE_NAME, PoolConfigParser()),
        ConfigLoader(SIGN_FILE_NAME, SignConfigParser()),
        ConfigLoader(VIP_FILE_NAME, VipConfigParser()),
        ConfigLoader(COMBINEDBUFF_FILE_NAME, BuffConfigParser()),
        ConfigLoader(STORY_FILE_NAME, StoryConfigParser()),
        ConfigLoader(WAR_PASS_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(WAR_PASS2_FILE_NAME, BattlePassConfigParser()),
        ConfigLoader(POSITION_FILE_NAME, TitleConfigParser()),
        ConfigLoader(GIFT_FILE_NAME, GiftConfigParser()),
        ConfigLoader(BADGE_FILE_NAME, BadgeConfigParser()),
        ConfigLoader(DAY_REWARD_FILE_NAME, DayRewardConfigParser()),
        ConfigLoader(MISSION_FILE_NAME, MissionConfigParser()),
        ConfigLoader(PVP_FILE_NAME, PvpConfigParser()),
        ConfigLoader(TURNTABLE_FILE_NAME, TurnTableConfigParser()),
        ConfigLoader(DRAW_FILE_NAME, DrawItemConfigParser()),
        ConfigLoader(TOWER_FILE_NAME, TowerConfigParser()),
    )

    suspend fun loadConfigs(configHolder: ConfigHolder) {
        // 并行加载所有配置文件以提高性能
        withContext(Dispatchers.IO) {
            configLoaders.map { loader ->
                async {
                    loader.loadConfig()
                    configHolder.setGameConfig(loader.getKey(), loader.getConfig())
                }
            }.awaitAll()
        }
    }
}
package com.moyu.chuanqirensheng.sub.language

import android.app.LocaleManager
import android.content.Context
import android.content.res.Configuration
import android.os.Build
import android.os.LocaleList
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.sub.datastore.KEY_LANGUAGE
import com.moyu.chuanqirensheng.sub.datastore.dataStore
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.triggerRebirth
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale

object LanguageManager {
    val languages = arrayOf("中文", "日本語", "한국어")
    val languageCodes = arrayOf("zh-rTW", "ja", "ko")
    val selectedLanguage = mutableStateOf("")

    suspend fun init() {
        mapData.putAll(
            GameApp.instance.dataStore.data.first().asMap()
                .map { it.key.name to it.value.toString() })
        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
            if (getStringFlowByKey(KEY_LANGUAGE).isEmpty() || getStringFlowByKey(KEY_LANGUAGE) !in languageCodes) {
                val language = Locale.getDefault().language
                languageCodes.forEachIndexed { index, code ->
                    if (language.startsWith(code)) {
                        selectedLanguage.value = languageCodes[index]
                    } else if (language.startsWith("zh")) {
                        // 多种中文都用繁体
                        selectedLanguage.value = languageCodes[0]
                    }
                }
                if (selectedLanguage.value.isEmpty()) {
                    selectedLanguage.value = languageCodes[0]
                }
                setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
            } else {
                selectedLanguage.value = getStringFlowByKey(KEY_LANGUAGE)
            }
            setLocale(GameApp.instance, selectedLanguage.value)
        } else {
            selectedLanguage.value = languageCodes[0]
            setLocale(GameApp.instance, selectedLanguage.value)
        }
    }


    @Composable
    fun LanguageSelectorView() {
        val showLanguage = remember {
            mutableStateOf(false)
        }
        if (showLanguage.value) {
            languages.forEachIndexed { index, language ->
                val selected = languageCodes[index] == selectedLanguage.value
                GameButton(
                    text = language,
                    buttonStyle = if (selected) ButtonStyle.Orange else ButtonStyle.Blue
                ) {
                    if (selectedLanguage.value != languageCodes[index]) {
                        selectedLanguage.value = languageCodes[index]
                        setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
                        updateLocale(GameApp.instance, languageCodes[index])
                    }
                    showLanguage.value = false
                }
            }
        } else {
            EffectButton(
                Modifier.padding(start = padding19, top = padding8, end = padding8),
                onClick = {
                    showLanguage.value = !showLanguage.value
                }) {
                Text(
                    text = stringResource(R.string.language_title),
                    style = MaterialTheme.typography.h1
                )
            }
        }
    }

    fun setLocale(context: Context, languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
    }

    fun updateLocale(context: Context, languageCode: String) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ (API 33+) 使用新的 LocaleManager API
            try {
                val localeManager = context.getSystemService(LocaleManager::class.java)
                val locale = Locale.forLanguageTag(languageCode)
                val localeList = LocaleList(locale)
                localeManager?.applicationLocales = localeList
            } catch (e: Exception) {
                // 如果新 API 失败，回退到旧方法
                e.printStackTrace()
            }
        }

        // 回退到旧的方法（Android 12 及以下，或新 API 失败时）
        setLocale(context, languageCode)
        // 重新启动活动以应用新的语言设置
        GameApp.globalScope.launch {
            delay(500)
            triggerRebirth()
        }
    }

    fun getLocalizedAssetFileName(fileName: String): String {
        val language = selectedLanguage.value
        languageCodes.forEach {
            if (language == it) {
                if (language == "zh-rTW") {
                    // 默认
                    return fileName
                }
                return it + "_" + fileName
            }
        }
        return fileName
    }

    fun isSelectedChinese() = selectedLanguage.value == languageCodes[0]
    fun getTextLines(): Int {
        return 1
    }
}
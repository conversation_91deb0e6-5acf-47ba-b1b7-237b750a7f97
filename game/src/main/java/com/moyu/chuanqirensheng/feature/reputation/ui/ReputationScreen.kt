package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.Dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.GameLabel2
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding180
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding220
import com.moyu.chuanqirensheng.ui.theme.padding250
import com.moyu.chuanqirensheng.ui.theme.padding280
import com.moyu.chuanqirensheng.ui.theme.padding290
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding380
import com.moyu.chuanqirensheng.ui.theme.padding42
import com.moyu.chuanqirensheng.ui.theme.padding420
import com.moyu.chuanqirensheng.ui.theme.padding480
import com.moyu.chuanqirensheng.ui.theme.padding500
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding64
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.util.REPUTATION_LEVEL
import com.moyu.chuanqirensheng.util.REPUTATION_TASK
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.gotoReputationShopById
import com.moyu.core.model.story.Story

@Composable
fun ReputationScreen() {
    LaunchedEffect(Unit) {
        ReputationManager.init()
    }
    GameBackground(
        title = stringResource(R.string.reputation),
        background = R.drawable.reputation_bg
    ) {
        Spacer(Modifier.fillMaxSize().background(B50))
        Column(Modifier.fillMaxSize()) {
            Spacer(Modifier.size(padding6))
            Row {
                EffectButton(onClick = {
                    goto(REPUTATION_LEVEL)
                }) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box {
                            Image(
                                modifier = Modifier.size(padding64),
                                painter = painterResource(id = R.drawable.reputation_level_icon),
                                contentDescription = null
                            )
                            if (ReputationManager.levelHasRed()) {
                                Image(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(ItemSize.LargePlus.itemSize / 20)
                                        .size(imageTinyPlus),
                                    painter = painterResource(R.drawable.red_icon),
                                    contentDescription = null
                                )
                            }
                        }
                        Spacer(Modifier.size(padding6))
                        GameLabel2(Modifier.size(padding100, padding30)) {
                            StrokedText(
                                text = stringResource(R.string.reputation_level),
                                color = Color.White,
                                style = MaterialTheme.typography.h3
                            )
                        }
                    }
                }
                Spacer(Modifier.size(padding22))
                EffectButton(onClick = {
                    goto(REPUTATION_TASK)
                }) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box {
                            Image(
                                modifier = Modifier.size(padding64),
                                painter = painterResource(id = R.drawable.reputation_task_icon),
                                contentDescription = null
                            )
                            if (ReputationManager.taskHasRed()) {
                                Image(
                                    modifier = Modifier
                                        .align(Alignment.TopEnd)
                                        .padding(ItemSize.LargePlus.itemSize / 20)
                                        .size(imageTinyPlus),
                                    painter = painterResource(R.drawable.red_icon),
                                    contentDescription = null
                                )
                            }
                        }
                        Spacer(Modifier.size(padding6))
                        GameLabel2(Modifier.size(padding100, padding30)) {
                            StrokedText(
                                text = stringResource(R.string.reputation_task),
                                color = Color.White,
                                style = MaterialTheme.typography.h3
                            )
                        }
                    }
                }
            }
            ReputationShopItems(
                Modifier
                    .fillMaxHeight()
                    .width(padding360)
                    .align(Alignment.CenterHorizontally)
            )
        }
    }
}

data class MapItem(
    val item: Story,
    val offsetX: Dp,
    val offsetY: Dp
)


// 先把要显示的 11 个项目都列出来
val items = listOf(
    MapItem(
        item = repo.gameCore.getStoryPool()[0],
        offsetX = padding12,
        offsetY = padding120
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[1],
        offsetX = padding60,
        offsetY = padding0
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[2],
        offsetX = padding200,
        offsetY = padding80
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[3],
        offsetX = padding280,
        offsetY = padding150
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[4],
        offsetX = padding100,
        offsetY = padding250
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[5],
        offsetX = padding220,
        offsetY = padding280
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[6],
        offsetX = padding290,
        offsetY = padding380
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[7],
        offsetX = padding0,
        offsetY = padding380
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[8],
        offsetX = padding180,
        offsetY = padding420
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[9],
        offsetX = padding42,
        offsetY = padding480
    ),
    MapItem(
        item = repo.gameCore.getStoryPool()[10],
        offsetX = padding290,
        offsetY = padding500
    ),
)

@Composable
fun ReputationShopItems(modifier: Modifier) {
    Box(
        modifier = modifier.graphicsLayer {
            translationX = -padding16.toPx()
            translationY = padding22.toPx()
        },
    ) {
        items.forEach { item ->
            ReputationBadge(
                modifier = Modifier
                    .offset(x = item.offsetX, y = item.offsetY)
                    .size(padding96),// 根据需要调大小
                item.item
            )
        }
    }
}

@Composable
fun ReputationBadge(
    modifier: Modifier,
    item: Story,
    style: TextStyle = MaterialTheme.typography.h6,
    textPadding: Dp = padding7,
    click: ()-> Unit = { gotoReputationShopById(item.id) }
) {
    EffectButton(modifier = modifier, onClick = {
        click()
    }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(getImageResourceDrawable(item.pic)),
            contentDescription = item.name,
        )
        StrokedText(
            text = item.name,
            color = Color.White,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = textPadding),
            style = style
        )
        if (ReputationManager.shopHasRed(item)) {
            Image(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(ItemSize.LargePlus.itemSize / 20)
                    .size(imageTinyPlus),
                painter = painterResource(R.drawable.red_icon),
                contentDescription = null
            )
        }
    }
}



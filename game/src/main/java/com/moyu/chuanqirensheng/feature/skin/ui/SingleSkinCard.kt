package com.moyu.chuanqirensheng.feature.skin.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.logic.getQualityFrameCircle
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Skin


@Composable
fun SingleSkinCard(modifier: Modifier = Modifier, skin: Skin) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        val selected = SkinManager.currentSkins.contains(skin)
        EffectButton(onClick = {
            Dialogs.skinDialog.value = skin
        }) {
            Image(
                painter = painterResource(getImageResourceDrawable(skin.getSmallIcon())),
                modifier = Modifier
                    .size(padding84)
                    .clip(RoundedCornerShape(50)),
                contentScale = ContentScale.FillWidth,
                contentDescription = if (selected) stringResource(id = R.string.game_selected) else stringResource(
                    id = R.string.game_not_selected
                )
            )
            Image(
                modifier = Modifier.width(padding110),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = skin.quality.getQualityFrameCircle()),
                contentDescription = null
            )
            if (skin.new) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmallPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
            if (selected) {
                Image(
                    painter = painterResource(id = R.drawable.common_choose),
                    modifier = Modifier
                        .size(imageLargePlus)
                        .align(Alignment.BottomEnd),
                    contentDescription = null
                )
            }
        }
        Box(modifier = Modifier.graphicsLayer {
            translationY = -padding22.toPx()
        }, contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier
                    .size(padding72, padding36)
                    .scale(2.2f),
                painter = painterResource(id = R.drawable.shop_name_frame),
                contentScale = ContentScale.FillWidth,
                contentDescription = null
            )
            Text(
                modifier = Modifier.graphicsLayer {
                    translationY = padding6.toPx()
                },
                text = skin.name,
                style = MaterialTheme.typography.h2,
                maxLines = 1,
                overflow = TextOverflow.Visible,
                textAlign = TextAlign.Center
            )
        }
        Spacer(modifier = Modifier.size(padding6))
        GameButton(
            modifier = Modifier.graphicsLayer {
                translationY = -padding16.toPx()
            },
            text = if (SkinManager.gainedSkins.contains(skin)) {
                if (selected) stringResource(id = R.string.cancel) else stringResource(id = R.string.do_select)
            } else stringResource(id = R.string.not_have_yet),
            textColor = Color.White,
            enabled = SkinManager.gainedSkins.contains(skin),
            buttonSize = ButtonSize.MediumMinus,
            buttonStyle = if (SkinManager.gainedSkins.contains(skin) && !selected) ButtonStyle.Orange else ButtonStyle.Blue
        ) {
            if (SkinManager.gainedSkins.contains(skin)) {
                SkinManager.selectSkin(skin)
            } else {
                skin.tips.toast()
            }
        }
    }
}

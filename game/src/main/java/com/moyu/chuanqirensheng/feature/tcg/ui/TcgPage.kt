package com.moyu.chuanqirensheng.feature.tcg.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.core.model.Award
import com.moyu.core.model.tcg.getTypeName
import com.moyu.core.model.tcg.getTypeTotal

@Composable
fun TcgPage() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        if (TcgManager.tcgCards.isEmpty()) {
            Spacer(modifier = Modifier.size(gapMedium))
            Text(
                text = stringResource(R.string.no_tcg_toast),
                style = MaterialTheme.typography.h2
            )
            Spacer(modifier = Modifier.weight(1f))
        } else {
            val group = TcgManager.tcgCards.groupBy { it.type }.entries.toList()
            Spacer(modifier = Modifier.size(padding12))
            LazyColumn(
                modifier = Modifier.weight(1f),
            ) {
                items(group.size) { index ->
                    Column(modifier = Modifier.fillMaxWidth()) {
                        group[index].let { typeList ->
                            val type = typeList.key
                            val list = typeList.value
                            val current = TcgManager.tcgCards.count { it.type == type }
                            TextLabel(
                                modifier = Modifier
                                    .graphicsLayer {
                                        translationX = -padding8.toPx()
                                    },
                                text = getTypeName(type) + " " + current + "/" + getTypeTotal(type)
                            )
                            FlowRow(Modifier.padding(horizontal = padding8), overflow = FlowRowOverflow.Visible,) {
                                list.forEach { item ->
                                    AwardList(
                                        award = Award(tcgs = listOf(item)),
                                        param = defaultParam.copy(itemSize = ItemSize.LargePlus),
                                        paddingHorizontalInDp = padding4,
                                        paddingVerticalInDp = padding4,
                                    )
                                }
                            }
                            Spacer(modifier = Modifier.size(padding48))
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding12))
        }
    }
}
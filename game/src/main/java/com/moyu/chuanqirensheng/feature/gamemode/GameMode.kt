package com.moyu.chuanqirensheng.feature.gamemode

const val MODE_NORMAL = 0
const val MODE_PVP = 1
const val MODE_PVP2 = 2
const val MODE_TOWER = 3

data class GameMode(
    val mode: Int = MODE_NORMAL,
) {
    fun isNormalMode() = mode == MODE_NORMAL
    fun isPvp1Mode() = mode == MODE_PVP
    fun isPvp2Mode() = mode == MODE_PVP2
    fun isTowerMode() = mode == MODE_TOWER
    fun isPvpMode() = isPvp1Mode() || isPvp2Mode()
    fun isNotNormalMode() = !isNormalMode()
}

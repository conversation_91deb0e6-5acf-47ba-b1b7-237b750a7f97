package com.moyu.chuanqirensheng.feature.story.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding72
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Story


@Composable
fun SingleStoryCard(modifier: Modifier = Modifier, story: Story, showButton: Boolean = true) {
    val lock = repo.gameCore.getUnlockById(story.unlockId)
    val unlocked = UnlockManager.getUnlockedFlow(lock)
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween
    ) {
        EffectButton(onClick = {
            if (unlocked) {
                StoryManager.switchSelection(story)
            } else {
                Dialogs.storyDetailDialog.value = story
            }
        }) {
            Image(
                painter = painterResource(getImageResourceDrawable(story.pic)),
                modifier = Modifier
                    .size(padding96)
                    .clip(RoundedCornerShape(50)),
                contentScale = ContentScale.FillWidth,
                contentDescription = if (story.selected) stringResource(id = R.string.game_selected) else stringResource(
                    id = R.string.game_not_selected
                )
            )
            Image(
                modifier = Modifier.width(padding120),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.hero_frame2),
                contentDescription = null
            )
            if (!unlocked) {
                Image(
                    painter = painterResource(id = R.drawable.common_lock),
                    modifier = Modifier
                        .size(imageLarge)
                        .align(Alignment.BottomEnd),
                    contentDescription = stringResource(id = R.string.locked)
                )
            } else if (story.selected) {
                Image(
                    painter = painterResource(id = R.drawable.common_choose),
                    modifier = Modifier
                        .size(imageLargePlus)
                        .align(Alignment.BottomEnd),
                    contentDescription = null
                )
            }
        }
        Box(modifier = Modifier.graphicsLayer {
            translationY = -padding22.toPx()
        }, contentAlignment = Alignment.Center) {
            Image(
                modifier = Modifier
                    .size(padding72, padding36)
                    .scale(2.2f),
                painter = painterResource(id = R.drawable.shop_name_frame),
                contentScale = ContentScale.FillWidth,
                contentDescription = null
            )
            Text(
                modifier = Modifier.graphicsLayer {
                    translationY = padding6.toPx()
                },
                text = story.name,
                style = MaterialTheme.typography.h2,
                maxLines = 1,
                overflow = TextOverflow.Visible,
                textAlign = TextAlign.Center
            )
        }
        if (showButton) {
            Spacer(modifier = Modifier.size(padding6))
            GameButton(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding16.toPx()
                },
                text = stringResource(id = R.string.check),
                textColor = Color.White,
                buttonSize = ButtonSize.MediumMinus,
                buttonStyle = ButtonStyle.Blue
            ) {
                Dialogs.storyDetailDialog.value = story
            }
        }
    }
}

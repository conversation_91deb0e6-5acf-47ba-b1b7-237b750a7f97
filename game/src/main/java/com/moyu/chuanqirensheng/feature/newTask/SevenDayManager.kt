package com.moyu.chuanqirensheng.feature.newTask

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_SEVEN_DAY
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_CHARGE_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COLLECT_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_COST_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC1
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC2
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC3
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC4
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC5
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC6
import com.moyu.chuanqirensheng.sub.datastore.KEY_ELECTRIC7
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_TASK_SELLS
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.model.Quest
import com.moyu.core.model.Sell
import com.moyu.core.model.toAward

object SevenDayManager {
    val collectTasks = mutableStateListOf<Quest>()
    val packages = mutableStateListOf<Sell>()
    val costTasks = mutableStateListOf<Quest>()
    val chargeTasks = mutableStateListOf<Quest>()

    val electricDay1 = Guarded(KEY_ELECTRIC1)
    val electricDay2 = Guarded(KEY_ELECTRIC2)
    val electricDay3 = Guarded(KEY_ELECTRIC3)
    val electricDay4 = Guarded(KEY_ELECTRIC4)
    val electricDay5 = Guarded(KEY_ELECTRIC5)
    val electricDay6 = Guarded(KEY_ELECTRIC6)
    val electricDay7 = Guarded(KEY_ELECTRIC7)

    fun init() {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY)
        if (UnlockManager.getUnlockedFlow(unlock)) {
            if (getLongFlowByKey(KEY_INIT_GAME_TIME) == 0L) {
                setLongValueByKey(KEY_INIT_GAME_TIME, getCurrentTime())
            }
            // 首次解锁进来，默认功能解锁前的电力，也记入第一天的充值
            if (getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)) <= 7) {
                // 首次解锁进来
                if (AwardManager.electric.value > 0
                    && electricDay1.value == 0
                    && electricDay2.value == 0
                    && electricDay3.value == 0
                    && electricDay4.value == 0
                    && electricDay5.value == 0
                    && electricDay6.value == 0
                    && electricDay7.value == 0
                ) {
                    electricDay1.value = AwardManager.electric.value
                }
            }
            createCollectTasks()
            createCostTasks()
            createChargeTasks()
            createPackages()
        }
    }

    fun createCollectTasks() {
        createTasks(collectTasks, KEY_COLLECT_TASK) {
            it.isCollectTask()
        }
    }

    fun createCostTasks() {
        createTasks(costTasks, KEY_COST_TASK) {
            it.isCostTask()
        }
    }

    fun createChargeTasks() {
        createTasks(chargeTasks, KEY_CHARGE_TASK) {
            it.isChargeTask()
        }
    }

    fun createTasks(snapTasks: SnapshotStateList<Quest>, key: String, filter: (Quest) -> Boolean) {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        if (snapTasks.isEmpty()) {
            getListObject(key, Quest.serializer()).let { taskList ->
                val pool = repo.gameCore.getGameTaskPool()
                val tasks = taskList.mapNotNull { task ->
                    pool.firstOrNull { it.id == task.id }
                        ?.copy(
                            done = task.done,
                            opened = task.opened,
                            needRemoveCount = task.needRemoveCount
                        )
                }
                snapTasks.addAll(tasks)
            }
        }

        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))
        val shouldShowTasks = repo.gameCore.getGameTaskPool().filter {
            filter(it)
        }.filter { task ->
            if (DebugManager.unlockAll) true else
            repo.gameCore.getDayRewardPool().firstOrNull { it.value == task.id }?.let {
                it.unlock <= day && it.disappear > day
            } ?: false
        }
        val currentTaskIds = snapTasks.map { it.id }
        val tobeAdded = shouldShowTasks.filter {
            !currentTaskIds.contains(it.id)
        }.map { task ->
            // 要用永久计数，但是又要移除之前的计数
            val needRemove = if (task.isMaxRecordQuest()) {
                0
            } else {
                val postFix = task.subType.map {
                    if (it == 0) "" else "_$it"
                }.reduce { acc, s -> acc + s }
                getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + task.type + postFix, 0)
            }
            task.copy(needRemoveCount = needRemove)
        }.sortedBy {
            it.order
        }
        snapTasks.addAll(tobeAdded)

        val shouldRemoveTasks = repo.gameCore.getGameTaskPool().filter {
            filter(it)
        }.filter { task ->
            if (DebugManager.unlockAll) false else
            repo.gameCore.getDayRewardPool().firstOrNull { it.value == task.id }?.let {
                it.unlock > day || it.disappear <= day
            } ?: false
        }
        snapTasks.removeAll { task ->
            shouldRemoveTasks.any { it.id == task.id }
        }

        setListObject(key, snapTasks, Quest.serializer())
    }

    fun createPackages() {
        if (!isNetTimeValid()) {
            return
        }
        if (packages.isEmpty()) {
            getListObject(KEY_NEW_TASK_SELLS, Sell.serializer()).let {
                packages.addAll(it.mapNotNull { sell ->
                    repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                        ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
                })
            }
        }
        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))
        val shouldShowPackages = repo.gameCore.getSellPool().filter {
            it.isNewTaskPackage()
        }.filter { sell ->
            if (DebugManager.unlockAll) true else
            repo.gameCore.getDayRewardPool().firstOrNull { it.value == sell.id }?.let {
                it.unlock <= day && it.disappear > day
            } ?: false
        }.sortedBy {
            it.priority
        }

        val currentPackageIds = packages.map { it.id }
        val tobeAdded = shouldShowPackages.filter {
            !currentPackageIds.contains(it.id)
        }.map { sell ->
            sell
        }

        packages.addAll(tobeAdded)
        packages.removeAll { sell ->
            shouldShowPackages.none { it.id == sell.id }
        }
        setListObject(KEY_NEW_TASK_SELLS, packages, Sell.serializer())
    }

    fun hasRed(): Boolean {
        return (collectTasks + costTasks + chargeTasks).any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun getRedIcons(): List<Boolean> {
        return mutableStateListOf(collectTasks.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }, false, costTasks.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        }, chargeTasks.any {
            QuestManager.getTaskDoneFlow(it) && !it.opened
        })
    }

    fun getElectricDayValue(day: Int): Guarded? {
        return when (day) {
            1 -> electricDay1
            2 -> electricDay2
            3 -> electricDay3
            4 -> electricDay4
            5 -> electricDay5
            6 -> electricDay6
            7 -> electricDay7
            else -> null
        }
    }

    fun gainElectric(gain: Int) {
        if (unlocked() && isNetTimeValid()) {
            getElectricDayValue(getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)))?.let {
                it.value += gain
            }
        }
    }

    fun isChargeTaskDone(num: Int): Boolean {
        return getElectricDayValue(getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)))?.let {
            it.value >= num
        } ?: false
    }

    fun isChargeTaskDoneString(num: Int): String {
        return getElectricDayValue(getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME)))?.let {
            it.value.toString() + "/" + num
        } ?: ""
    }

    suspend fun openPackage(sell: Sell) {
        if (sell.isDiamondMoney()) {
            AwardManager.gainDiamond(-sell.price)
        } else if (sell.isKeyMoney()) {
            AwardManager.gainKey(-sell.price)
        }
        ReportManager.onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )

        val realAward = sell.toAward()
        AwardManager.gainAward(realAward)
        Dialogs.awardDialog.value = realAward
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
        markGoogleSellItem(sell)
    }

    fun markGoogleSellItem(sell: Sell) {
        packages.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
            packages[it] = packages[it].copy(storage = packages[it].storage - 1)
            setListObject(KEY_NEW_TASK_SELLS, packages, Sell.serializer())
        }
    }

    fun unlocked(): Boolean {
        val unlock = repo.gameCore.getUnlockById(UNLOCK_SEVEN_DAY)
        return UnlockManager.getUnlockedFlow(unlock)
    }
}
package com.moyu.chuanqirensheng.feature.story.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CommonLine
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.DarkGreen
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding69
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Story
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

@Composable
fun StoryDetailDialog(show: MutableState<Story?>) {
    show.value?.let { story ->
        val lock = repo.gameCore.getUnlockById(story.unlockId)
        val unlocked = UnlockManager.getUnlockedFlow(lock)
        val scrollState = rememberScrollState()
        LaunchedEffect(Unit) {
            delay(200)
            scrollState.animateScrollTo(200)
        }
        PanelDialog(onDismissRequest = { show.value = null }, contentBelow = {
            if (!repo.inGame.value && !story.peek) {
                GameButton(
                    buttonSize = ButtonSize.Medium,
                    buttonStyle = ButtonStyle.Orange,
                    text = if (!unlocked) stringResource(id = R.string.go_get) else if (story.selected) stringResource(
                        id = R.string.cancel
                    ) else stringResource(id = R.string.do_select),
                ) {
                    if (unlocked) {
                        StoryManager.switchSelection(story)
                        Dialogs.storyDetailDialog.value = null
                    } else {
                        if (lock.url != "0") {
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                val sell = repo.gameCore.getSellPool()
                                    .first { it.id.toString() == lock.url }
                                BillingManager.prepay(sell) {
                                    GameApp.globalScope.launch(Dispatchers.Main) {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            }
                        } else {
                            lock.apply {
                                desc.toast()
                            }
                        }
                    }
                }
            }
        }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = padding10)
                    .verticalScroll(scrollState),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = story.name,
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                CommonLine()
                Text(
                    text = story.desc,
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Column {
                    repo.gameCore.getEventPool().filter { it.storyBag == story.unlockId }
                        .filter { it.isEnd }
                        .filter { it.storyBag == story.unlockId }.forEachIndexed { index, event ->
                            Row(verticalAlignment = Alignment.CenterVertically) {
                                Box(contentAlignment = Alignment.Center) {
                                    Image(
                                        painter = painterResource(getImageResourceDrawable(event.pic)),
                                        modifier = Modifier
                                            .size(padding69)
                                            .clip(RoundedCornerShape(50)),
                                        contentScale = ContentScale.FillWidth,
                                        contentDescription = null
                                    )
                                    Image(
                                        modifier = Modifier.width(padding80),
                                        contentScale = ContentScale.FillWidth,
                                        painter = painterResource(id = R.drawable.hero_frame2),
                                        contentDescription = null
                                    )
                                }
                                Spacer(modifier = Modifier.size(padding6))
                                Column(modifier = Modifier.fillMaxWidth()) {
                                    val done =
                                        getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.ENDING.id + "_${event.id}" + "_${0}") > 0
                                    val doneText =
                                        if (done) {
                                            stringResource(R.string.story_end_done)
                                        } else {
                                            stringResource(R.string.story_end_not_done)
                                        }
                                    Text(
                                        text = stringResource(id = R.string.ending) + (index + 1),
                                        style = MaterialTheme.typography.h3,
                                        color = Color.Black
                                    )
                                    Spacer(modifier = Modifier.size(padding8))
                                    Text(
                                        text = event.name + doneText,
                                        style = MaterialTheme.typography.h2,
                                        color = if (done) DarkGreen else DARK_RED
                                    )
                                }
                            }
                            Spacer(modifier = Modifier.size(padding14))
                        }
                }
            }
        }
    }
}
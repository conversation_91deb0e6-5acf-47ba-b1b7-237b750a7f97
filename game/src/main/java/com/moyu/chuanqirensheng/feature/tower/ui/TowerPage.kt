package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.tower.getTypeIcon
import com.moyu.chuanqirensheng.feature.tower.getTypeName
import com.moyu.chuanqirensheng.logic.event.createTowerRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.role.SingleRoleView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.buttonWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageMediumMinus
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding260
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding5
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.core.model.Tower
import com.moyu.core.model.getRaceTypeName
import kotlin.math.min

@Composable
fun TowerPage() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(padding19))
        val pool = repo.gameCore.getTowerPool()
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            items(min(pool.size - TowerManager.maxLevel.value, 50)) { index ->
                Spacer(modifier = Modifier.size(padding10))
                OneTowerItem(pool[TowerManager.maxLevel.value + index]) {
                    TowerManager.fight(tower = pool[TowerManager.maxLevel.value + index])
                }
            }
        }
        Spacer(modifier = Modifier.size(padding10))
    }
}

@Composable
fun OneTowerItem(tower: Tower, callback: () -> Unit) {
    Box(
        modifier = Modifier
            .height(padding260)
            .fillMaxWidth()
            .paint(
                painter = painterResource(id = R.drawable.common_frame_long),
                contentScale = ContentScale.FillBounds,
            )
            .padding(horizontal = padding22)
    ) {
        TowerLevel(
            modifier = Modifier
                .align(Alignment.TopStart),
            level = tower.id,
        )
        val pool = repo.gameCore.getPoolById(tower.playPara2)
        val enemies = pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createTowerRole(it, tower)
        }
        FlowRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = padding66),  // 根据需求增减
            maxItemsInEachRow = 3,
            verticalArrangement = Arrangement.spacedBy(padding4),
            horizontalArrangement = Arrangement.spacedBy(padding7)
        ) {
            enemies.forEach {
                SingleRoleView(it, itemSize = ItemSize.Medium, showName = false)
            }
        }
        Column(
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .fillMaxHeight().width(buttonWidth),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = tower.getTypeName(),
                    style = MaterialTheme.typography.h2,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.weight(1f)
                )
                Box(contentAlignment = Alignment.Center) {
                    Image(
                        modifier = Modifier.size(imageMediumMinus),
                        painter = painterResource(R.drawable.item2_quality_0),
                        contentDescription = null,
                    )
                    Image(
                        painter = painterResource(id = tower.getTypeIcon()),
                        contentDescription = null,
                        modifier = Modifier.size(padding22).clip(RoundedCornerShape(padding1))
                    )
                }
            }

            if (tower.type.first() == 2) {
                val haloPool = repo.gameCore.getSkillById(tower.playPara1.first())
                haloPool.apply {
                    SingleSkillView(Modifier, this, itemSize = ItemSize.Medium, textColor = Color.White)
                }
            } else if (tower.type.first() == 3) {
                Text(
                    text = stringResource(R.string.tower_tips3, tower.playPara1.first().getRaceTypeName()),
                    style = MaterialTheme.typography.h2,
                    textAlign = TextAlign.Center
                )
            } else if (tower.type.first() == 4) {
                Text(
                    text = stringResource(R.string.tower_tips4, tower.playPara1.first()),
                    style = MaterialTheme.typography.h2,
                    textAlign = TextAlign.Center
                )
            }
            Box(contentAlignment = Alignment.Center) {
                GameButton(
                    text = stringResource(id = R.string.challenge),
                    enabled = TowerManager.maxLevel.value + 1 == tower.id,
                    onClick = callback,
                )
                if (TowerManager.maxLevel.value >= tower.id) {
                    Image(
                        painter = painterResource(id = R.drawable.common_choose),
                        modifier = Modifier
                            .size(imageLarge)
                            .align(Alignment.BottomEnd),
                        contentDescription = null
                    )
                }
            }
        }
    }
}

@Composable
fun TowerLevel(
    modifier: Modifier = Modifier,
    level: Int,
    frame: Painter = painterResource(id = R.drawable.main_frame1)
) {
    Box(modifier = modifier.height(imageLarge), contentAlignment = Alignment.CenterStart) {
        Image(
            modifier = Modifier
                .height(imageLarge)
                .graphicsLayer {
                    rotationY = 180f
                    translationX = -padding14.toPx()
                },
            contentScale = ContentScale.FillHeight,
            painter = frame,
            contentDescription = null
        )
        Text(
            modifier = Modifier.padding(start = padding5),
            text = stringResource(R.string.tower_level, level),
            style = MaterialTheme.typography.h1
        )
    }
}

package com.moyu.chuanqirensheng.feature.sign.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.quest.getLoginDays
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun SignScreen() {
    val signs = SignManager.getShowSigns()
    GameBackground(title = stringResource(R.string.sign_title)) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                ),
        )
        Column {
            Spacer(modifier = Modifier.size(padding6))
            LazyVerticalGrid(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding10),
                columns = GridCells.Fixed(4),
                verticalArrangement = Arrangement.spacedBy(padding10),
                content = {
                    items(signs.size) { index ->
                        val sign = signs[index]
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = stringResource(R.string.day_of, sign.day),
                                color = if (getLoginDays() >= sign.day) Color.White else Color.Gray,
                                style = MaterialTheme.typography.h3
                            )
                            Box(contentAlignment = Alignment.Center) {
                                val param = if (getLoginDays() >= sign.day) {
                                    defaultParam.copy(
                                        itemSize = ItemSize.Large,
                                        showName = false,
                                        showEffect = false
                                    ) {
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            SignManager.gain(sign)
                                        }
                                    }
                                } else {
                                    defaultParam.copy(
                                        itemSize = ItemSize.Large,
                                        showName = false,
                                        showEffect = false
                                    )
                                }
                                AwardList(
                                    award = sign.toAward(),
                                    param = param,
                                    paddingHorizontalInDp = padding0,
                                    paddingVerticalInDp = padding0
                                )
                                if (SignManager.isSignGained(sign)) {
                                    Image(
                                        painter = painterResource(id = R.drawable.common_choose),
                                        contentDescription = null,
                                        modifier = Modifier.size(imageLarge)
                                    )
                                }
                                if (getLoginDays() >= sign.day && !SignManager.isSignGained(sign)) {
                                    Image(
                                        modifier = Modifier
                                            .align(Alignment.TopEnd)
                                            .size(imageSmall),
                                        painter = painterResource(R.drawable.red_icon),
                                        contentDescription = null
                                    )
                                }
                            }
                        }
                    }
                })
        }
    }
}
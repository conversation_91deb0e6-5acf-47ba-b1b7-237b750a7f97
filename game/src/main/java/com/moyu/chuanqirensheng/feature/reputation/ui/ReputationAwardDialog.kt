package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import com.moyu.chuanqirensheng.screen.effect.StrokedText
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.story.toReputationName
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.AwardList
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.core.model.sell.toAward

@Composable
fun ReputationAwardDialog(show: MutableState<Int>) {
    show.value.takeIf { it >= 0 }?.let { reputationIndex ->
        CommonDialog(title = (reputationIndex + 1).toReputationName(), onDismissRequest = {
            show.value = -1
        }) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                val level = AwardManager.toReputationLevelData()[reputationIndex]
                val pool = repo.gameCore.getReputationLevelPool().filter { it.level > 0 }
                LazyColumn(modifier = Modifier.fillMaxWidth().weight(1f), content = {
                    items(pool.size) { index ->
                        val it = pool[index]
                        val levelEnough = level.level >= it.level
                        val gained =
                            ReputationManager.isReputationAwardGained(it, reputationIndex)
                        if (!gained) {
                            Row(
                                modifier = Modifier
                                    .height(padding120)
                                    .fillMaxWidth()
                                    .padding(vertical = padding10)
                                    .paint(
                                        painterResource(R.drawable.common_frame_focus3),
                                        contentScale = ContentScale.FillBounds,
                                    )
                                    .padding(padding10),
                                verticalAlignment = Alignment.CenterVertically,
                                horizontalArrangement = Arrangement.SpaceEvenly,
                            ) {
                                StrokedText(
                                    text = "Lv." + it.name,
                                    style = MaterialTheme.typography.h2,
                                )
                                AwardList(
                                    award = it.toAward(reputationIndex),
                                )
                                GameButton(text = if (gained) stringResource(id = R.string.already_got)
                                else if (!levelEnough) stringResource(
                                    id = R.string.reputation_level_not_enough
                                )
                                else stringResource(
                                    id = R.string.gain_award
                                ),
                                    buttonSize = ButtonSize.MediumMinus,
                                    enabled = levelEnough && !gained,
                                    buttonStyle = ButtonStyle.Red,
                                    onClick = {
                                        if (levelEnough && !gained) {
                                            ReputationManager.gainAward(it, reputationIndex)
                                        } else {
                                            if (!levelEnough) {
                                                GameApp.instance.getString(R.string.reputation_level_not_enough)
                                                    .toast()
                                            }
                                        }
                                    })
                            }
                        }
                    }
                })
                GameButton(text = stringResource(id = R.string.confirm),
                    buttonStyle = ButtonStyle.Red,
                    onClick = {
                        show.value = -1
                    })
            }
        }
    }
}
package com.moyu.chuanqirensheng.feature.draw

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.application.Dialogs.drawResultDialog
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.quest.FOREVER
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.onDrawAllyCard
import com.moyu.chuanqirensheng.feature.sell.SellManager.isDrawing
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.KEY_CONSUME_ALLY_COUPON_ALL
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_RAW_TIME
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_WEEK_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_DRAW_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_ONE_TURN_CONSUME_ALLY_COUPON
import com.moyu.chuanqirensheng.sub.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.mapData
import com.moyu.chuanqirensheng.sub.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.util.gapWeek
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.model.Award
import com.moyu.core.model.Quest
import com.moyu.core.model.toAward
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextDoubleClosure
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import timber.log.Timber
import java.lang.Integer.min
import java.util.Random


fun Award.toOnlyOneOut(randomAlly: Random): Award {
    return copy(
        outAllies = this.outAllies.shuffled(randomAlly).take(1)
    )
}

object DrawManager {
    val drawTasks = mutableStateListOf<Quest>()
    val savedWeekNum = mutableStateOf(0)

    fun init() {
        createDrawTasks()
    }

    fun createDrawTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
        if (drawTasks.isEmpty()) {
            try {
                getListObject(KEY_GAME_DRAW_TASK, Quest.serializer()).let {
                    drawTasks.addAll(it.map { task ->
                        repo.gameCore.getGameTaskById(task.id)
                            .copy(done = task.done, opened = task.opened)
                    })
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (drawTasks.isEmpty()) {
            val maxGameProgress = min(
                StoryManager.getMaxAge(), getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.GAME_PROGRESS.id + "_1",
                    1
                )
            )
            drawTasks.addAll(filteredTasks.filter { it.isDrawTask() }.filter {
                maxGameProgress >= it.talent.first() && maxGameProgress <= it.talent[1]
            }.shuffled(RANDOM))

            setLongValueByKey(KEY_GAME_DRAW_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_GAME_DRAW_TASK, drawTasks)
        }
    }

    fun getAllyCouponAward(index: Int): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.type == 1 }
        // 计算所有符合条件抽奖项的总概率
        val totalRate = drawItems.sumOf { it.rate }
        // 生成一个随机数 [0, totalRate) 之间
        val randomValue = randomAlly(index).nextDoubleClosure(0.0, totalRate)
        // 遍历抽奖项，根据概率选择
        var cumulativeRate = 0.0
        drawItems.forEach { item ->
            cumulativeRate += item.rate
            if (randomValue < cumulativeRate) {
                return repo.gameCore.getPoolById(item.pool).toAward(random = randomAlly(index))
                    .toOnlyOneOut(randomAlly(index))
            }
        }
        return Award()
    }

    fun getAwardPool(): Award {
        val drawItems = repo.gameCore.getDrawPool().filter { it.type == 1 }
        return drawItems.map { repo.gameCore.getPoolById(it.pool).toAward() }
            .reduce { acc, award -> acc + award }
    }

    fun getAllyOrangeAward(index: Int): Award {
        return repo.gameCore.getPoolById(repo.gameCore.getDrawEnsurePool())
            .toAward(random = randomAlly()).toOnlyOneOut(randomAlly(index))
    }

    suspend fun buyAllyCoupon(repeat: Int = 1) {
        if (isDrawing) {
            return
        }
        isDrawing = true
        var totalAward = Award()
        repeat(repeat) {
            var this10DrawHaveOrange = false
            val award = (1..10).map {
                // 确保橙卡
                val singleAward =
                    if (needOrangeAllyThisTurn() && it == 10 && !this10DrawHaveOrange) {
                        getAllyOrangeAward(it)
                    } else getAllyCouponAward(it)
                this10DrawHaveOrange =
                    if (!this10DrawHaveOrange) singleAward.outAllies.any { it.isOrange() } else true
                totalAward += singleAward
                drawResultDialog.value = totalAward
                // delay给main线程时间去更新UI
                delay(50)
                singleAward
            }.reduce { acc, award -> acc + award }
            if (this10DrawHaveOrange) {
                setAllyOrangeDraw(0)
            } else {
                setAllyOrangeDraw(getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON) + 10)
            }
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON, repeat)
            increaseIntValueByKey(KEY_CONSUME_ALLY_COUPON_ALL, repeat)
            GameApp.globalScope.async {
                val lack = 10
                AwardManager.gainAward(
                    award.copy(
                        key = -lack * repo.gameCore.getAllyCouponRate()
                    )
                )
            }
        }
        onDrawAllyCard(10 * repeat)
        isDrawing = false
    }

    fun randomAlly(index: Int = 0): Random {
        return Random(
            (GameApp.instance.getObjectId()?:"no user").toCharArray().sumOf { it.code } + getIntFlowByKey(
                KEY_CONSUME_ALLY_COUPON_ALL
            ).toLong() * 951 + index * 63917)
    }

    fun getAllyLeftDraw(): Int {
        return 100 - getIntFlowByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON)
    }


    fun needOrangeAllyThisTurn(): Boolean {
        return getAllyLeftDraw() <= 10
    }

    fun setAllyOrangeDraw(value: Int) {
        setIntValueByKey(KEY_ONE_TURN_CONSUME_ALLY_COUPON, value)
    }

    fun show(): Boolean {
        if (getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK) == 0L) {
            return true
        }
        return ((getCurrentTime() - getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK)) / (1000 * 60 * 60 * 24)) % 7 < 2
    }

    fun refresh() {
        if (getLongFlowByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK) == 0L || getLongFlowByKey(
                KEY_DRAW_INIT_RAW_TIME
            ) == 0L
        ) {
            setLongValueByKey(KEY_DRAW_INIT_TIME_IN_A_WEEK, getCurrentTime())
            setLongValueByKey(KEY_DRAW_INIT_RAW_TIME, getCurrentTime())
        }
        savedWeekNum.value = getIntFlowByKey(KEY_DRAW_WEEK_NUM)

        val currentWeekIndex = gapWeek(
            getCurrentTime(), getLongFlowByKey(
                KEY_DRAW_INIT_TIME_IN_A_WEEK
            )
        )
        if (currentWeekIndex != savedWeekNum.value) {
            // 更新周数
            savedWeekNum.value = currentWeekIndex
            setIntValueByKey(KEY_DRAW_WEEK_NUM, currentWeekIndex)
            // 清除任务记录
            drawTasks.clear()
            setListObject(KEY_GAME_DRAW_TASK, emptyList<Quest>())
            createDrawTasks()
            // 所有的抽卡记录清理掉
            mapData.keys.filter {
                it.startsWith(
                    FOREVER + KEY_GAME_TASK_PROGRESS + QuestEvent.DRAW.id
                )
            }.forEach {
                setIntValueByKey(it, 0)
            }

            // 更新刷新时间，时间还是保持和KEY_LOTTERY_INIT_TIME初始一样，只是如果超过了一周，往前推进一周
            val rawGapTimeFromAWeek =
                getLongFlowByKey(KEY_DRAW_INIT_RAW_TIME) % (1000 * 60 * 60 * 24 * 7)
            val currentGapTimeFromAWeek = getCurrentTime() % (1000 * 60 * 60 * 24 * 7)
            setLongValueByKey(
                KEY_DRAW_INIT_TIME_IN_A_WEEK,
                getCurrentTime() - currentGapTimeFromAWeek + rawGapTimeFromAWeek
            )
        }
    }

    fun getReds(): List<Boolean> {
        return mutableStateListOf(false, drawTasks.any {
            QuestManager.getTaskDoneFlow(it)
                    && !it.opened
        })
    }
}
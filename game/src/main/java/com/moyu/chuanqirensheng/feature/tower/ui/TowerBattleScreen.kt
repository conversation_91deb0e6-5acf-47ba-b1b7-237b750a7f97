package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.isMaster
import com.moyu.chuanqirensheng.logic.event.createTowerRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.sub.datastore.KEY_TOWER_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.logic.role.ALLY_ROW1_FIRST
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.role.Role

@Composable
fun TowerBattleScreen() {
    val clickedStartBattle = remember { mutableStateOf(false) }
    val tower = TowerManager.targetLevel.value
    LaunchedEffect(Unit) {
        // 将所有角色加入到局内
        BattleManager.selectAllToGame()
        val positionMap = mutableMapOf<Int, Role?>()
        val pool = repo.gameCore.getPoolById(tower.playPara2)
        pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createTowerRole(it, tower)
        }.forEachIndexed { index, role ->
            positionMap[positionListEnemy[index]] = role
        }
        repo.setCurrentEnemies(positionMap)
        if (tower.type.first() == 2) {
            // 天气
            repo.gameCore.getSkillById(tower.playPara1.first()).apply {
                positionMap.values.firstNotNullOf { it }.let {
                    it.learnSkill(this, it)
                }
            }
        }
        TowerManager.lastTowerAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                BattleManager.selectAllyToBattle(
                    it, index + ALLY_ROW1_FIRST
                )
            }
        }
    }
    GameBackground(
        title = stringResource(R.string.tower_mode), bgMask = B65, background = R.drawable.bg_1
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            if (repo.inBattle.value) {
                Box(modifier = Modifier.graphicsLayer {
                    // todo 简单处理pvp战斗场景的位置
                    translationY = -padding22.toPx()
                }) {
                    BattleFieldLayout(repo.battleRoles)
                }
            } else {
                val filter = TowerManager.getTowerFilter(tower)
                TowerPrepareBattleLayout(allyFilter = filter) {
                    if (clickedStartBattle.value) {
                        // 防止快速点击导致的异常
                        false
                    } else if (BattleManager.getBattleAllies().values.isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(
                                content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    TowerManager.failed(
                                        emptyList(),
                                        repo.battleRoles.values.mapNotNull { it })
                                    goto(TOWER_SCREEN)
                                })
                        true
                    } else if (BattleManager.getBattleAllies().values.filter { !it.isMaster() }.any { !filter(it) }) {
                        GameApp.instance.getWrapString(R.string.tower_ally_not_match).toast()
                        false
                    } else if (positionListEnemy.none { repo.battleRoles[it]?.isOver() == false }) {
                        GameApp.instance.getWrapString(R.string.pvp_ally_error).toast()
                        false
                    } else {
                        val battleAllies = BattleManager.getTowerBattleRoles()
                        TowerManager.lastTowerAllyIds.clear()
                        TowerManager.lastTowerAllyIds.addAll(battleAllies.toSortedMap().values.map { it.getAlly().id })
                        setListObject(KEY_TOWER_ALLY_IDS, TowerManager.lastTowerAllyIds)
                        repo.setCurrentAllies(battleAllies)
                        if (repo.isCurrentEnemyEmptyOrDead()) {
                            GameApp.instance.getWrapString(R.string.pvp_error_ally).toast()
                        } else {
                            clickedStartBattle.value = true
                            repo.startBattle()
                        }
                        true
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6)
                .graphicsLayer {
                    translationY = padding16.toPx()
                }, settingBattleItems
        )
    }
}
package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.Dialogs.drawPoolDialog
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.router.LOGIN_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.resource.InfoIcon
import com.moyu.chuanqirensheng.sub.datastore.KEY_DRAW_INIT_TIME_IN_A_WEEK
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding60
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.timeLeft
import com.moyu.chuanqirensheng.util.toDayHourMinuteSecond
import kotlinx.coroutines.delay


@Composable
fun DrawPage() {
    val leftUpdateTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(Unit) {
        while (true) {
            leftUpdateTime.longValue = timeLeft(
                getCurrentTime(), getLongFlowByKey(
                    KEY_DRAW_INIT_TIME_IN_A_WEEK
                ), 2
            )
            if (leftUpdateTime.longValue <= 1000) {
                delay(1000)
                goto(LOGIN_SCREEN)
            }
            delay(500)
        }
    }
    Column(
        Modifier.paint(
            painterResource(id = R.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds
        )
    ) {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier.fillMaxWidth()
                .padding(horizontal = padding14), verticalAlignment = Alignment.CenterVertically
        ) {
            if (leftUpdateTime.longValue > 0 && DrawManager.show()) {
                Text(
                    modifier = Modifier
                        .padding(start = padding12),
                    text = stringResource(R.string.time_left) + leftUpdateTime.longValue.toDayHourMinuteSecond(),
                    style = MaterialTheme.typography.h3
                )
            }
            Spacer(modifier = Modifier.weight(1f))
            CurrentKeyPoint(
                showPlus = true,
                showFrame = true
            )
        }
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = padding8),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                DrawLabel()
                Spacer(modifier = Modifier.size(padding60))
                AllyCouponItem()
                Spacer(modifier = Modifier.size(gapLarge))
            }
        }
    }
}

@Composable
fun DrawLabel() {
    Box(
        Modifier
            .fillMaxWidth().padding(horizontal = padding16)) {
        Image(
            modifier = Modifier.fillMaxWidth().aspectRatio(1.44f),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.draw_label),
            contentDescription = null
        )
        GameButton(
            modifier = Modifier.align(Alignment.TopStart).padding(padding16),
            buttonSize = ButtonSize.MediumMinus,
            buttonStyle = ButtonStyle.Orange,
            text = stringResource(R.string.draw_pool_title),
        ) {
            drawPoolDialog.value = true
        }
        InfoIcon(modifier = Modifier.align(Alignment.TopEnd).padding(padding16)) {
            Dialogs.alertDialog.value = CommonAlert(
                title = GameApp.instance.getWrapString(R.string.draw_card),
                content = GameApp.instance.getWrapString(R.string.draw_info_content),
                onlyConfirm = true
            )
        }
    }
}

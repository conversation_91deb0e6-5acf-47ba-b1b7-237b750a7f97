package com.moyu.chuanqirensheng.feature.story.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun StoryScreen() {
    val stories = StoryManager.stories
    GameBackground(title = stringResource(R.string.select_story)) {
        Column(
            modifier = Modifier
                .padding(top = padding6)
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .padding(top = padding10),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            LazyVerticalGrid(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(), columns = GridCells.Fixed(3),
                horizontalArrangement = Arrangement.spacedBy(padding16)
            ) {
                items(stories.size) { index ->
                    val story = stories[index]
                    SingleStoryCard(
                        modifier = Modifier.height(eventCardHeight),
                        story = story,
                    )
                }
            }
        }
    }
}
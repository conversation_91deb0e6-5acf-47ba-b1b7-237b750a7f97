package com.moyu.chuanqirensheng.feature.tcg.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding48
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.pvpRecordFrameHeight
import com.moyu.core.model.tcg.TcgAward
import com.moyu.core.model.tcg.getTypeName
import com.moyu.core.model.tcg.getTypeTotal
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun TcgAwardPage() {
    val tcgAwards = repo.gameCore.getTcgAwardPool()
    val groups = tcgAwards.groupBy { it.type }
    LazyColumn(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
            .padding(vertical = padding6),
        content = {
            items(groups.size) { index ->
                val typeValue = index + 1
                Column(modifier = Modifier.fillMaxWidth()) {
                    groups[typeValue]?.let { typeList ->
                        val type = typeList.first().type
                        val current = TcgManager.tcgCards.count { it.type == type }
                        TextLabel(
                            modifier = Modifier
                                .graphicsLayer {
                                    translationX = -padding8.toPx()
                                },
                            text = getTypeName(type) + " " + current + "/" + getTypeTotal(type)
                        )
                        typeList.forEach {
                            TcgAwardRow(it)
                            Spacer(modifier = Modifier.size(padding6))
                        }
                        Spacer(modifier = Modifier.size(padding48))
                    }
                }
            }
        }
    )
}

@Composable
fun TcgAwardRow(tcgAward: TcgAward) {
    val current = TcgManager.tcgCards.count { it.type == tcgAward.type }
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(pvpRecordFrameHeight)
            .padding(horizontal = padding8)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_long),
            contentDescription = null
        )
        Column(modifier = Modifier.padding(horizontal = padding16, vertical = padding10)) {
            Text(
                text = stringResource(
                    R.string.tcg_collect_item,
                    tcgAward.num,
                    getTypeName(tcgAward.type),
                    current,
                    tcgAward.num
                ),
                style = MaterialTheme.typography.h2,
            )
            val award = tcgAward.toAward()
            Row(
                modifier = Modifier.fillMaxSize(),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                AwardList(
                    award = award,
                    param = defaultParam.copy(
                        showName = false,
                        numInFrame = true,
                        itemSize = ItemSize.Medium,
                    ),
                    paddingVerticalInDp = padding0,
                    paddingHorizontalInDp = padding0
                )
                Spacer(modifier = Modifier.weight(1f))
                val key = tcgAward.id
                if (AwardManager.tcgCardRewardRecords.contains(key)) {
                    GameButton(text = stringResource(id = R.string.already_got),
                        enabled = false,
                        buttonStyle = ButtonStyle.Orange,
                        buttonSize = ButtonSize.Medium,
                        onClick = { })
                } else {
                    val enabled = current >= tcgAward.num
                    GameButton(text = if (enabled) stringResource(R.string.gain) else stringResource(
                        id = R.string.not_complete_yet
                    ),
                        enabled = enabled,
                        buttonStyle = ButtonStyle.Orange,
                        buttonSize = ButtonSize.Medium,
                        onClick = {
                            if (AwardManager.tcgCardRewardRecords.contains(key)) {
                                GameApp.instance.getWrapString(R.string.already_got).toast()
                            } else if (current >= tcgAward.num) {
                                GameApp.globalScope.launch(Dispatchers.Main) {
                                    AwardManager.gainTcgCardReward(tcgAward, award)
                                }
                            } else {
                                GameApp.instance.getWrapString(R.string.no_collected).toast()
                            }
                        })
                }
            }
        }
    }
}
package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.story.ui.SingleStoryCard
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.Quest

@Composable
fun EndingQuestScreen() {
    val tasks = remember {
        mutableStateListOf<Quest>()
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    LaunchedEffect(refresh.intValue.toString()) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        QuestManager.createOneTimeTasks()
        // 完成的任务排前面，已领取的排最后
        QuestManager.oneTimeTasks.filter { it.isEndingTask() }.map {
            it.copy(done = QuestManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .apply {
                tasks.clear()
                tasks.addAll(this)
            }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(padding4))
        LazyColumn(modifier = Modifier
            .weight(1f)
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ), content = {
            items(tasks.size) { index ->
                val task = tasks[index]
                val preTask = tasks.getOrNull(index - 1)
                if (preTask == null || preTask.storyId() != task.storyId()) {
                    SingleStoryCard(
                        Modifier.fillMaxWidth(),
                        repo.gameCore.getStoryPool().first { it.id == task.storyId() },
                        showButton = false
                    )
                }
                SingleQuest(tasks[index]) {
                    refresh.intValue += 1
                }
                Spacer(modifier = Modifier.size(padding10))
            }
        })
        Spacer(modifier = Modifier.size(padding12))
    }
}
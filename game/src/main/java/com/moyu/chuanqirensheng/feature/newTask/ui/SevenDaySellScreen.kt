package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.timeLeft
import kotlinx.coroutines.delay


@Composable
fun SevenDaySellScreen() {
    val shopChests = SevenDayManager.packages.sortedBy {
        if (it.storage <= 0) 9999 else it.priority
    }
    val refresh = remember {
        mutableIntStateOf(0)
    }
    val currentTime = remember {
        mutableLongStateOf(0L)
    }
    LaunchedEffect(refresh.intValue) {
        SevenDayManager.createPackages()
    }
    LaunchedEffect(Unit) {
        if (isNetTimeValid()) {
            while (true) {
                currentTime.longValue = getCurrentTime()
                delay(500)
            }
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(padding12))
        Row(
            Modifier
                .align(Alignment.End)
                .padding(end = padding6)
        ) {
            CurrentDiamondPoint(showPlus = true, showFrame = true)
            CurrentKeyPoint(showPlus = true, showFrame = true)
        }
        Spacer(modifier = Modifier.size(padding4))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState()),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.size(padding4))
            // 临时
            FlowRow(
                modifier = Modifier,
                verticalArrangement = Arrangement.spacedBy(padding4),
                horizontalArrangement = Arrangement.spacedBy(padding16),
                overflow = FlowRowOverflow.Visible,
            ) {
                shopChests.forEach { sell ->
                    repo.gameCore.getDayRewardPool().firstOrNull { it.value == sell.id }?.let { rewardItem ->
                        val leftUpdateTime = timeLeft(
                            currentTime.value, getLongFlowByKey(
                                KEY_INIT_GAME_TIME
                            ), rewardItem.getKeepDays()
                        )
                        if (leftUpdateTime > 0) {
                            SevenDaySellItem(sell = sell, leftUpdateTime = leftUpdateTime)
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.size(gapLarge))
        }
    }
}
package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyCardsRow
import com.moyu.chuanqirensheng.screen.ally.SelectAllyData
import com.moyu.chuanqirensheng.screen.battle.EnemiesRow
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.core.model.Ally


@Composable
fun TowerPrepareBattleLayout(
    allyFilter: (Ally) -> Boolean = { true },
    capacity: Int = 6,
    extraLayout: @Composable ColumnScope.() -> Unit = {},
    start: () -> Boolean,
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceEvenly
    ) {
        EnemiesRow(repo.battleRoles)
        Spacer(modifier = Modifier.size(gapLarge))
        Column(
            Modifier
                .align(Alignment.End)
                .graphicsLayer {
                    translationX = gapSmallPlus.toPx()
                }) {
            extraLayout()
        }
        GameButton(
            text = stringResource(R.string.start_battle),
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big
        ) {
            start()
        }
        Spacer(modifier = Modifier.size(gapLarge))
        AllyCardsRow(modifier = Modifier
            .fillMaxWidth(),
            allies = BattleManager.getBattleAllies(),
            capacity = capacity,
            showName = true,
            showHp = true,
            allyClick = {
                BattleManager.selectAllyToBattle(it, -1)
            }) {
            Dialogs.selectAllyToBattleDialog.value = SelectAllyData(
                capacity = capacity, filter = allyFilter,
                needMaster = false, needGod = false
            ) {
                start()
            }
        }
    }
}
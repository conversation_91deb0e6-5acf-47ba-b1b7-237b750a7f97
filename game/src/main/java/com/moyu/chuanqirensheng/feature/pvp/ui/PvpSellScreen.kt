package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.runtime.Composable
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.sell.ui.SellPage
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.B65

@Composable
fun PvpSellScreen() {
    GameBackground(
        title = stringResource(R.string.pvp_shop),
        bgMask = B65,
        background = R.drawable.bg_pvp
    ) {
        SellPage(listOf(6), showPvpDiamond = true)
    }
}
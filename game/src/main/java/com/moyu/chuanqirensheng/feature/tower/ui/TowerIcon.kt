package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2

@Composable
fun TowerIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_TOWER)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Box(modifier = modifier.graphicsLayer {
            translationX = -LabelSize.Large.width.toPx() / 2.2f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                flip = true,
                text = stringResource(R.string.tower_mode),
                icon = R.drawable.tower_icon
            ) {
                goto(TOWER_SCREEN)
            }
            if (TowerManager.hasRed()) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(start = padding10, top = padding2)
                        .size(imageTinyPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}

package com.moyu.chuanqirensheng.feature.gift.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.login.UpdateTimeText
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_GIFT_AWARDED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.SkillLevel5Color
import com.moyu.chuanqirensheng.ui.theme.W10
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding30
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding620
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding84
import com.moyu.chuanqirensheng.ui.theme.padding96
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.model.Award
import com.moyu.core.model.Gift
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun GiftDetailDialog(show: MutableState<Gift?>) {
    show.value?.takeIf { !getBooleanFlowByKey(KEY_GIFT_AWARDED + it.id) || it.limitBuy == 0 }
        ?.let { gift ->
            val gifts = mutableStateOf(GiftManager.getDisplayGifts())
            EmptyDialog(onDismissRequest = {
                GiftManager.setShowed(gift)
                show.value = null
            }) {
                Box(Modifier.fillMaxSize()) {
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(bottom = padding84)
                            .padding(horizontal = padding96),
                        painter = painterResource(id = getImageResourceDrawable(gift.pic)),
                        contentDescription = null
                    )
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(padding620)
                            .padding(horizontal = padding22),
                        horizontalAlignment = Alignment.CenterHorizontally,
                    ) {
                        Spacer(modifier = Modifier.height(padding10))
                        Box(
                            Modifier
                                .fillMaxWidth()
                                .zIndex(999f),
                            contentAlignment = Alignment.Center
                        ) {
                            GameLabel {
                                Text(
                                    text = gift.name,
                                    style = MaterialTheme.typography.h1,
                                    color = Color.Black
                                )
                            }
                            EffectButton(modifier = Modifier.align(Alignment.CenterEnd), onClick = {
                                GiftManager.setShowed(gift)
                                show.value = null
                            }) {
                                Image(
                                    modifier = Modifier
                                        .height(backIconHeight)
                                        .scale(1.2f),
                                    contentScale = ContentScale.FillHeight,
                                    painter = painterResource(id = R.drawable.common_exit),
                                    contentDescription = stringResource(R.string.quit_page)
                                )
                            }
                        }
                        Spacer(modifier = Modifier.height(padding26))
                        Text(
                            modifier = Modifier
                                .align(Alignment.Start)
                                .zIndex(999f)
                                .clip(RoundedCornerShape(padding6))
                                .background(W10)
                                .padding(horizontal = padding30, vertical = padding10),
                            text = gift.content.replace("\\n", "\n"),
                            style = MaterialTheme.typography.h1,
                            color = DARK_RED,
                        )
                        Spacer(modifier = Modifier.weight(1f))
                        gift.label_pic.takeIf { it != "0" }?.let {
                            Image(
                                modifier = Modifier
                                    .align(Alignment.Start)
                                    .height(imageLarge)
                                    .scale(1.5f)
                                    .graphicsLayer {
                                        translationY = -padding19.toPx()
                                        translationX = padding22.toPx()
                                    },
                                contentScale = ContentScale.FillHeight,
                                painter = painterResource(id = getImageResourceDrawable(gift.label_pic)),
                                contentDescription = null
                            )
                        }
                        Row(
                            Modifier
                                .fillMaxWidth()
                                .background(B50)
                                .clip(RoundedCornerShape(padding10)),
                            horizontalArrangement = Arrangement.Center,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                            AwardList(
                                modifier = Modifier.scale(1.2f),
                                award = sell.toAward(),
                                param = defaultParam.copy(
                                    itemSize = ItemSize.MediumPlus, textColor = Color.White
                                ),
                                paddingHorizontalInDp = padding0,
                                mainAxisAlignment = Arrangement.SpaceEvenly
                            )
                        }
                        Spacer(modifier = Modifier.height(padding12))
                        val sell = repo.gameCore.getSellPool().first { it.id == gift.id }
                        GameButton(text = sell.getPriceTextWithUnit()) {
                            GiftManager.setShowed(gift)
                            show.value = null
                            GameApp.globalScope.launch(Dispatchers.Main) {
                                if (getBooleanFlowByKey(KEY_GIFT_AWARDED + gift.id) && gift.limitBuy != 0) {
                                    // 已经买了，并且是只能买一次的东西，就不给再买
                                    GameApp.instance.getWrapString(R.string.sold_out).toast()
                                } else {
                                    BillingManager.prepay(sell) {
                                        GameApp.globalScope.launch(Dispatchers.Main) {
                                            // todo 一定要先更新KEY_GIFT_AWARDED，不然首页的礼包icon不会正常消失，
                                            // 因为AwardManager.electric变化后，首页礼包icon就会重新检查条件，所以要先改变条件再出发更新
                                            SellManager.openGiftSell(sell)
                                            if (repo.inGame.value && !repo.gameMode.value.isPvpMode()) {
                                                // todo 礼包奖励，如果在局内，直接局内也要获得盟友卡
                                                AwardManager.gainAward(Award(allies = sell.toAward().outAllies))
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                }
                Row(
                    Modifier
                        .align(Alignment.BottomCenter)
                        .padding(horizontal = padding19)
                        .padding(bottom = padding45)
                        .horizontalScroll(rememberScrollState()),
                    horizontalArrangement = Arrangement.spacedBy(padding6)
                ) {
                    gifts.value.forEach {
                        val selected = show.value?.id == it.id
                        EffectButton(onClick = {
                            Dialogs.giftDetailDialog.value = it
                        }) {
                            Column(
                                modifier = Modifier
                                    .clip(
                                        RoundedCornerShape(
                                            padding8
                                        )
                                    )
                                    .background(W10)
                                    .padding(vertical = padding10),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                val refreshIndex = remember {
                                    mutableIntStateOf(0)
                                }
                                Image(
                                    painter = painterResource(getImageResourceDrawable(it.pic)),
                                    modifier = Modifier.size(
                                        imageHugeLite
                                    ),
                                    contentDescription = null,
                                )
                                if (it.limitTime != 0 && it.displayTime != 0L && isNetTimeValid()) {
                                    UpdateTimeText(it) {
                                        refreshIndex.intValue += 1
                                    }
                                } else {
                                    Text(
                                        text = "",
                                        style = MaterialTheme.typography.h6,
                                    )
                                }
                                Text(
                                    modifier = Modifier.width(imageHugeLite * 1.4f),
                                    maxLines = 2,
                                    minLines = 2,
                                    textAlign = TextAlign.Center,
                                    text = it.name,
                                    color = if (selected) SkillLevel5Color else Color.White,
                                    style = MaterialTheme.typography.h4
                                )
                            }
                            if (selected) {
                                Image(
                                    modifier = Modifier
                                        .size(imageLarge)
                                        .align(Alignment.BottomCenter)
                                        .graphicsLayer {
                                            rotationX = 180f
                                            translationY = padding36.toPx()
                                        },
                                    painter = painterResource(R.drawable.common_arrow_down),
                                    contentDescription = null
                                )
                            }
                        }
                    }
                }
            }
        }
}



package com.moyu.chuanqirensheng.feature.pvp

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_PVP
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.quest.QuestEvent
import com.moyu.chuanqirensheng.feature.rank.PvpData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP_BATTLE_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.event.createPvpRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.loseBattleEffect
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.winBattleEffect
import com.moyu.chuanqirensheng.sub.anticheat.Guarded
import com.moyu.chuanqirensheng.sub.datastore.KEY_DIED_IN_PVP
import com.moyu.chuanqirensheng.sub.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_NUM
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_TARGET
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_LOSE_TODAY
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_SCORE
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN
import com.moyu.chuanqirensheng.sub.datastore.KEY_PVP_WIN_TODAY
import com.moyu.chuanqirensheng.sub.datastore.getListObject
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.sub.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.GameCore
import com.moyu.core.logic.role.ENEMY_ROW1_FIRST
import com.moyu.core.model.Award
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.uuid.UUID
import kotlinx.uuid.generateUUID

// todo pvp战斗中能生效的天赋列举
val pvpTalentMainIds = listOf(
    9010,
    9011,
    9012,
    9013,
    9014,
    9015,
    9025,
    9026,
    9027,
    9028,
    9029,
    9030,
    9041,
    9042,
    9043,
    9044,
    9045,
    9055,
    9056,
    9057,
    9058,
    9059,
    9060,
    9070,
    9071,
    9072,
    9073,
    9074,
    9075,
    9086,
    9087,
    9088,
    9089,
    9090
)

const val MAX_PVP_NUM = 10
const val INIT_PVP_SCORE = 1000
const val PVP_HALO_SKILL_ID = 5010

object PvpManager {
    val pvpScore = Guarded(KEY_PVP_SCORE, mutableStateOf(INIT_PVP_SCORE))
    val lastPvpAllyIds = mutableListOf<Int>()
    val pkNumToday = Guarded(KEY_PK_NUM)
    val pkWin = Guarded(KEY_PVP_WIN)
    val pkLose = Guarded(KEY_PVP_LOSE)
    val pkWinToday = Guarded(KEY_PVP_WIN_TODAY)
    val pkLoseToday = Guarded(KEY_PVP_LOSE_TODAY)
    val pkTargetList = mutableStateListOf<String>()
    val currentTarget = mutableStateOf(RankData(System.currentTimeMillis()))
    val targetsFromServer = mutableStateOf(emptyList<RankData>())
    val currentTargets = mutableStateOf(emptyList<RankData>())

    suspend fun init() {
        initPkNum()
        lastPvpAllyIds.clear()
        lastPvpAllyIds.addAll(getListObject(KEY_PK_ALLY_IDS))
    }

    fun initPkNum() {
        if (!isNetTimeValid()) {
            return
        }
        if (isSameDay(
                getLongFlowByKey(KEY_PK_UPDATE_TIME_IN_MILLIS),
                getCurrentTime()
            )
        ) {
            if (pkTargetList.isEmpty()) {
                pkTargetList.addAll(getListObject(KEY_PK_TARGET))
            }
        } else {
            pvpScore.value = INIT_PVP_SCORE
            pkNumToday.value = 0
            pkWinToday.value = 0
            pkLoseToday.value = 0
            pkTargetList.clear()
            setLongValueByKey(KEY_PK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_PK_TARGET, emptyList<String>())
        }
    }

    fun pk(rankData: RankData) {
        if (!DebugManager.unlockAll) {
            if (pkWinToday.value + pkLoseToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
                || pkNumToday.value >= MAX_PVP_NUM + VipManager.getExtraPvpNum()
            ) {
                GameApp.instance.getWrapString(R.string.pvp_num_limit).toast()
                return
            }
        }
        if (rankData.pvpData.allyIds.isEmpty()) {
            GameApp.instance.getWrapString(R.string.pvp_ally_error).toast()
            return
        }
        val allies = rankData.pvpData.allyIds.take(6)
        allies.forEach { ally ->
            if (repo.gameCore.getAllyPool().firstOrNull { it.id == ally } == null) {
                GameApp.instance.getWrapString(R.string.pvp_upgrade).toast()
                return
            }
        }
        currentTarget.value = rankData
        repo.gameMode.value = GameMode(MODE_PVP)
        increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + QuestEvent.PVP_BATTLE.id)
        pkNumToday.value += 1
        val roleHashMap = mutableMapOf<Int, Role?>()
        repeat(allies.size) {
            roleHashMap[ENEMY_ROW1_FIRST + it] =
                createPvpRole(
                    repo.gameCore.getAllyById(allies[it]).getRace(),
                    rankData.pvpData.talentIds
                )
        }
        // 竞技场地形
        val haloSkill = repo.gameCore.getSkillById(PVP_HALO_SKILL_ID)
        roleHashMap.values.firstNotNullOf { it }.let {
            it.learnSkill(haloSkill, it)
        }

        repo.battleRoles.clear()
        repo.battleRoles.putAll(roleHashMap)
        goto(PVP_BATTLE_SCREEN)
    }

    fun pkFailed(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkLose.value += 1
        pkLoseToday.value += 1
        val index = currentTargets.value.sortedByDescending { it.pvpScore }
            .indexOfFirst { it.userId == currentTarget.value.userId }
        val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool().size) {
            repo.gameCore.getPvpPool().last()
        } else {
            repo.gameCore.getPvpPool()[index]
        }
        if (lessThanMax()) {
            GameApp.globalScope.launch {
                Dialogs.awardDialog.value =
                    Award(pvpDiamond = pvpData.loseToken, pvpScore = pvpData.losePoint).apply {
                        AwardManager.gainAward(this)
                    }
            }
            ReportManager.pk(0, pvpScore.value)
        }
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleFailed)
        Dialogs.gameLoseDialog.value = allies + enemies
        restartEffect(dialogEffectState, loseBattleEffect)
        increaseIntValueByKey(KEY_DIED_IN_PVP)
        GiftManager.getDisplayGifts(isBattle = true).firstOrNull { !it.dialogShowed }?.let {
            Dialogs.giftDetailDialog.value = it
        }
    }

    private fun lessThanMax(): Boolean {
        return (pkLoseToday.value + pkWinToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()) &&
                pkNumToday.value <= MAX_PVP_NUM + VipManager.getExtraPvpNum()
    }

    fun pkWined(allies: List<Role>, enemies: List<Role>) {
        // 需要根据时间确认是否刷新pk数据
        initPkNum()

        pkWin.value += 1
        pkWinToday.value += 1
        pkTargetList.add(currentTarget.value.userId)
        val index = currentTargets.value.sortedByDescending { it.pvpScore }
            .indexOfFirst { it.userId == currentTarget.value.userId }
        val pvpData = if (index == -1 || index >= repo.gameCore.getPvpPool().size) {
            repo.gameCore.getPvpPool().last()
        } else {
            repo.gameCore.getPvpPool()[index]
        }
        if (lessThanMax()) {
            GameApp.globalScope.launch(Dispatchers.Main) {
                setListObject(KEY_PK_TARGET, pkTargetList)
                Dialogs.awardDialog.value =
                    Award(pvpDiamond = pvpData.winToken, pvpScore = pvpData.winPoint).apply {
                        AwardManager.gainAward(this)
                    }
                EndingManager.uploadRank(isPvp = true)
                ReportManager.pk(1, pvpScore.value)
            }
        }
        repo.inBattle.value = false
        GameCore.instance.onBattleEffect(SoundEffect.BattleWin)
        Dialogs.gameWinDialog.value = allies + enemies
        restartEffect(dialogEffectState, winBattleEffect)
    }

    fun getMockPvpData(): List<RankData> {
        val allyList =
            repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                .shuffled(RANDOM)
                .take(6)
        return List(20) {
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value),
                    (pvpScore.value * 1.1f).toInt()
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = allyList.map { it.id })
            )
        } + List(30) {
            val id = UUID.generateUUID().toString().take(5)
            RankData(
                time = getCurrentTime(),
                userId = id,
                userPic = GameApp.instance.getAvatarUrl() ?: "",
                userName = GameApp.instance.getWrapString(R.string.pvp_mock) + id,
                pvpScore = RANDOM.nextIntClosure(
                    (pvpScore.value * 0.9f).toInt(),
                    (pvpScore.value),
                ),
                pvpData = PvpData(
                    talentIds = TalentManager.getPvpTalents(),
                    allyIds = allyList.map { it.id })
            )
        }
    }

    fun getPvpData(): PvpData {
        return PvpData(
            talentIds = TalentManager.getPvpTalents(),
            allyIds = lastPvpAllyIds.ifEmpty {
                repo.gameCore.getAllyPool().filter { it.star == 0 && it.quality == 3 }
                    .shuffled(RANDOM).take(6).map { it.id }
            },
            win = pkWinToday.value,
            lose = pkLoseToday.value
        )
    }

    fun refreshTargets() {
        val listAbove = targetsFromServer.value.filter { it.pvpScore > pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        val listBelow = targetsFromServer.value.filter { it.pvpScore <= pvpScore.value }
            .filter { it.userId !in pkTargetList }.shuffled()
        if (listAbove.size < 2) {
            currentTargets.value =
                (listAbove + listBelow.take(5 - listAbove.size)).sortedByDescending { it.pvpScore }
        } else if (listBelow.size < 3) {
            currentTargets.value =
                (listAbove.take(5 - listBelow.size) + listBelow).sortedByDescending { it.pvpScore }
        } else {
            currentTargets.value =
                (listAbove.take(2) + listBelow.take(3)).sortedByDescending { it.pvpScore }
        }
    }
}
package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.draw.DrawManager
import com.moyu.chuanqirensheng.feature.router.DRAW_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_DRAW
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.isNetTimeValid

@Composable
fun DrawIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_DRAW)
    if (UnlockManager.getUnlockedFlow(unlock) && isNetTimeValid()) {
        LaunchedEffect(Unit) {
            DrawManager.init()
            DrawManager.refresh()
        }
        if (DrawManager.show()) {
            Box(modifier = modifier.graphicsLayer {
                translationX = -LabelSize.Large.width.toPx() / 2.2f
            }, contentAlignment = Alignment.Center) {
                TextLabel(
                    modifier = Modifier.scale(1.1f),
                    labelSize = LabelSize.Large,
                    flip = true,
                    text = stringResource(R.string.draw_icon),
                    forceTextStyle = MaterialTheme.typography.h3,
                    icon = R.drawable.draw_icon
                ) {
                    goto(DRAW_SCREEN)
                }
                if (DrawManager.getReds().any { it }) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(start = padding10, top = padding2)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
        }
    }
}

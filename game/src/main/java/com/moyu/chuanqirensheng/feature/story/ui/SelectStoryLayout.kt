package com.moyu.chuanqirensheng.feature.story.ui

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.ui.theme.createCountryLabelHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardHeight
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding10


@Composable
fun SelectStoryLayout() {
    val stories = StoryManager.stories
    Box {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(createCountryLabelHeight)
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .horizontalScroll(rememberScrollState()),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Spacer(modifier = Modifier.size(padding10))
            stories.forEach {
                SingleStoryCard(
                    modifier = Modifier
                        .height(eventCardHeight)
                        .graphicsLayer {
                            translationY = padding10.toPx()
                        },
                    story = it,
                )
            }
            Spacer(modifier = Modifier.size(padding10))
        }
        TextLabel(modifier = Modifier.graphicsLayer {
            translationY = -padding16.toPx()
        }, text = stringResource(R.string.select_story_title))
    }
}
package com.moyu.chuanqirensheng.feature.tcg

import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_TCG_CARDS
import com.moyu.chuanqirensheng.sub.datastore.getObject
import com.moyu.chuanqirensheng.sub.datastore.setObject
import com.moyu.core.model.tcg.TcgCard
import com.moyu.core.model.tcg.TcgCardsObject

object TcgManager {
    val tcgCards = mutableListOf<TcgCard>()

    fun init() {
        getObject<TcgCardsObject>(KEY_TCG_CARDS)?.let {
            it.cardIds.forEachIndexed { index, i ->
                val card =
                    repo.gameCore.getTcgCardById(i).copy(count = it.cardCounts[index])
                tcgCards.add(card)
            }
        }
    }

    fun gainCard(card: TcgCard) {
        tcgCards.indexOfFirst { it.id == card.id }.takeIf { it != -1 }?.let {
            tcgCards[it] = tcgCards[it].increaseNum(1)
        } ?: kotlin.run {
            tcgCards.add(card)
        }
        setObject(KEY_TCG_CARDS, tcgCards.toTcgCardsObject())
    }

    fun hasRed(): Boolean {
        return repo.gameCore.getTcgAwardPool().any { tcg ->
            val current = tcgCards.count { tcg.type == it.type }
            !AwardManager.tcgCardRewardRecords.contains(tcg.id) && current >= tcg.num
        }
    }
}
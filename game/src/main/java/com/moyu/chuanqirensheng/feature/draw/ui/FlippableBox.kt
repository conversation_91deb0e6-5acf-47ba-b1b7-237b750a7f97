package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.delay

@Composable
fun FlippableBox(
    duration: Int = 600,
    front: @Composable () -> Unit,
    back: @Composable () -> Unit
) {
    var flipped by remember { mutableStateOf(false) }
    var hasFlipped by remember { mutableStateOf(false) } // 新增的状态变量

    val rotation by animateFloatAsState(
        targetValue = if (flipped) 180f else 0f,
        animationSpec = tween(durationMillis = duration, easing = LinearEasing), label = ""
    )

    LaunchedEffect(Unit) {
        if (!hasFlipped) { // 只有当没有翻转过时才播放动画
            delay(RANDOM.nextIntClosure(10, 1000).toLong())
            flipped = true
            hasFlipped = true // 标记为已翻转
        }
    }

    Box(
        modifier = Modifier
            .graphicsLayer {
                rotationY = rotation
                cameraDistance = 8 * density
            },
        contentAlignment = Alignment.Center
    ) {
        if (rotation <= 90f) {
            Box(modifier = Modifier.fillMaxSize()) {
                front()
            }
        } else {
            Box(modifier = Modifier
                .fillMaxSize()
                .graphicsLayer { rotationY = 180f }) {
                back()
            }
        }
    }
}
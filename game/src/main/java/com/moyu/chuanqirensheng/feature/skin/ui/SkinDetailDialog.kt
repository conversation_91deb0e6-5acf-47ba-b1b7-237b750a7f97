package com.moyu.chuanqirensheng.feature.skin.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.skin.getProperty
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.ui.theme.padding130
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.Skin
import com.moyu.core.model.property.Property


@Composable
fun SkinDetailDialog(show: MutableState<Skin?>) {
    show.value?.let { skin ->
        LaunchedEffect(Unit) {
            SkinManager.setUnNew(skin)
        }
        PanelDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                SkinDetailLayout(skin)
            }
        }
    }
}

@Composable
fun SkinDetailLayout(skin: Skin) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = skin.name,
            style = MaterialTheme.typography.h1,
            color = Color.Black
        )
        Spacer(modifier = Modifier.size(padding26))
        Row(
            modifier = Modifier.weight(1f).fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            Box(contentAlignment = Alignment.Center) {
                Image(
                    modifier = Modifier
                        .size(padding130, padding150)
                        .padding(horizontal = padding4, vertical = padding6),
                    painter = painterResource(id = getImageResourceDrawable(skin.pic)),
                    contentScale = ContentScale.Crop,
                    contentDescription = null
                )
                Image(
                    modifier = Modifier.size(padding130, padding150),
                    painter = painterResource(id = R.drawable.common_frame2),
                    contentDescription = null
                )
            }
            Column {
                skin.getProperty().MainPropertyLine(
                    originProperty = Property(),
                    textStyle = MaterialTheme.typography.h4,
                    showZero = false
                )
            }
        }
    }
}
package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.quest.ui.WarPass2QuestScreen
import com.moyu.chuanqirensheng.feature.router.gotoQuest
import com.moyu.chuanqirensheng.feature.vip.ui.VipAllyView
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.screen.resource.VipLevel
import com.moyu.chuanqirensheng.ui.theme.cheatFrameHeight
import com.moyu.chuanqirensheng.ui.theme.cheatFrameWidth
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding51
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.model.BattlePass
import com.moyu.core.model.toAward
import kotlinx.coroutines.launch

@Composable
fun BattlePass2Screen() {
    val listTabItems = remember {
        mutableStateListOf(
            GameApp.instance.getWrapString(R.string.battle_pass_award),
            GameApp.instance.getWrapString(R.string.battle_pass_quest),
        )
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    GameBackground(title = stringResource(R.string.battle_pass2)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> BattlePass2Page()
                    else -> WarPass2QuestScreen()
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding6.toPx()
                },
                pageState = pagerState,
                titles = listTabItems,
                redIcons = List(listTabItems.size) { index ->
                    if (index == 0) {
                        BattlePass2Manager.hasRed()
                    } else {
                        QuestManager.warPass2Tasks.any {
                            QuestManager.getTaskDoneFlow(it) && !it.opened
                        }
                    }
                }
            )
        }
    }
}


@Composable
fun OnePass2Item(cheat: BattlePass, callback: () -> Unit) {
    EffectButton(
        modifier = Modifier.size(cheatFrameWidth, cheatFrameHeight), onClick = callback
    ) {
        Pass2Awards(cheat, callback = callback)
        VipLevel(
            modifier = Modifier
                .align(Alignment.TopStart)
                .offset(y = -padding10),
            cheatLevel = cheat.level,
        )
    }
}

@Composable
fun Pass2Awards(cheat: BattlePass, callback: () -> Unit) {
    val unlocked = (BattlePass2Manager.getCurrentWarPass()?.id ?: 0) >= cheat.id
    Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxSize()) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding2),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = if (unlocked) R.drawable.equipment_frame else R.drawable.equipment_frame_unlock),
            contentDescription = null
        )
        Column {
            Spacer(modifier = Modifier.size(padding10))
            val award = cheat.toAward()
            if (unlocked) {
                AwardList(
                    Modifier,
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.White,
                        textFrameDrawable = R.drawable.shop_name_frame,
                        textFrameDrawableYPadding = -padding2,
                        callback = callback
                    ),
                    award = award,
                )
            } else {
                AwardList(
                    Modifier,
                    param = defaultParam.copy(
                        peek = true, textColor = Color.White,
                        textFrameDrawable = R.drawable.shop_name_frame,
                        textFrameDrawableYPadding = -padding2,
                    ),
                    award = award
                )
            }
        }
        if (cheat.unlockType == 1) {
            // empty
        } else {
            if (!AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                Image(
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .size(imageLarge),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = null
                )
            }
        }
        if (unlocked) {
            Box(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(padding6),
                contentAlignment = Alignment.Center
            ) {
                if (BattlePass2Manager.isThisLevelGained(cheat)) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        contentScale = ContentScale.FillWidth,
                        painter = painterResource(id = R.drawable.common_choose),
                        contentDescription = null
                    )
                }
            }
        } else {
            // null
        }
    }
}

@Composable
fun CurrentPass2Item(modifier: Modifier) {
    Column(
        modifier = modifier
            .padding(end = padding51)
            .scale(0.9f)
            .graphicsLayer {
                translationX = padding22.toPx()
            },
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.size(padding10))
        val item = repo.gameCore.getBattlePass2Pool()
            .first { it.season == BattlePass2Manager.getPassSeason() }
        VipAllyView(item.themeReward)
        Spacer(modifier = Modifier.size(padding10))
        if (item.isFree == 1) {
            GameButton(
                text = stringResource(R.string.gain_warpass_exp),
                buttonSize = ButtonSize.Big,
                buttonStyle = ButtonStyle.Orange
            ) {
                GameApp.globalScope.launch {
                    gotoQuest()
                }
            }
        } else {
            if (AwardManager.battlePass2Bought[BattlePass2Manager.getPassSeason()]!!.value) {
                Text(
                    text = stringResource(R.string.warpass2_unlocked),
                    style = MaterialTheme.typography.h3
                )
            } else {
                GameButton(
                    text = stringResource(R.string.unlock_warpass2),
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Orange,
                    textColor = Color.White
                ) {
                    Dialogs.warPass2UnlockDialog.value = true
                }
            }
        }
        Spacer(modifier = Modifier.size(padding4))
    }
}
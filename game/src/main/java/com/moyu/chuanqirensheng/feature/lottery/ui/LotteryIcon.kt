package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.feature.router.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.sub.datastore.KEY_FREE_CHEAP_LOTTERY_DONE
import com.moyu.chuanqirensheng.sub.datastore.KEY_FREE_EXPENSIVE_LOTTERY_DONE
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.isNetTimeValid

@Composable
fun LotteryIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_LOTTERY)
    if (UnlockManager.getUnlockedFlow(unlock) && isNetTimeValid()) {
        LaunchedEffect(Unit) {
            LotteryManager.refresh()
        }
        val isCheap = LotteryManager.showCheap()
        Box(modifier = modifier.graphicsLayer {
            translationX = -LabelSize.Large.width.toPx() / 2.2f
        }, contentAlignment = Alignment.Center) {
            TextLabel(
                modifier = Modifier.scale(1.1f),
                labelSize = LabelSize.Large,
                flip = true,
                text = if (isCheap) stringResource(id = R.string.lottery_title1) else stringResource(
                    id = R.string.lottery_title2
                ),
                forceTextStyle = MaterialTheme.typography.h3,
                icon = if (isCheap) R.drawable.lottery_icon1 else R.drawable.lottery_icon2
            ) {
                if (isCheap) {
                    goto(LOTTERY_SCREEN1)
                } else {
                    goto(LOTTERY_SCREEN2)
                }
            }
            if ((isCheap && !getBooleanFlowByKey(KEY_FREE_CHEAP_LOTTERY_DONE)) ||
                (!isCheap && !getBooleanFlowByKey(KEY_FREE_EXPENSIVE_LOTTERY_DONE))
            ) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .padding(start = padding10, top = padding2)
                        .size(imageTinyPlus),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
        }
    }
}

package com.moyu.chuanqirensheng.feature.tcg.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding6


val tcgListTabItems = mutableStateOf(
    listOf(
        GameApp.instance.getWrapString(R.string.tcg_sheet1),
        GameApp.instance.getWrapString(R.string.tcg_sheet2)
    )
)

@Composable
fun TcgScreen() {
    val pagerState = rememberPagerState {
        tcgListTabItems.value.size
    }
    GameBackground(title = stringResource(R.string.tcg_title)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> TcgPage()
                    else -> TcgAwardPage()
                }
            }
            NavigationTab(modifier = Modifier.graphicsLayer {
                translationY = -padding6.toPx()
            }, pagerState, tcgListTabItems.value, listOf(false, TcgManager.hasRed()))
        }
    }
}
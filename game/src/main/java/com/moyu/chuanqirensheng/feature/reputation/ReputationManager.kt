package com.moyu.chuanqirensheng.feature.reputation

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_REPUTATION_GAINED
import com.moyu.chuanqirensheng.datastore.KEY_REPUTATION_SELLS
import com.moyu.chuanqirensheng.datastore.KEY_REPUTATION_TASK
import com.moyu.chuanqirensheng.datastore.KEY_REPUTATION_TASK_UPDATE_TIME_IN_MILLIS
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getListObject
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setListObject
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.feature.lucky.AdManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.AwardManager.toReputationLevelData
import com.moyu.chuanqirensheng.logic.sell.SELL_FOREVER
import com.moyu.chuanqirensheng.logic.task.FOREVER
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.model.award.GuardedList
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.GameCore
import com.moyu.core.model.level.ReputationLevel
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.Sell
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.story.Story
import com.moyu.core.model.task.GameTask
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber
import java.lang.Integer.min

object ReputationManager {
    val awardGainedIds = GuardedList(KEY_REPUTATION_GAINED)
    val reputations = repo.gameCore.getStoryPool().map { it.name + GameApp.instance.getWrapString(R.string.reputation) }
    val packages = mutableStateListOf<Sell>()
    val collectTasks = mutableStateListOf<GameTask>()

    fun init() {
        createCollectTasks()
        createPackages()
    }

    fun createCollectTasks() {
        if (!isNetTimeValid()) {
            // 防止作弊
            return
        }
        // 任务中,启动游戏可以直接标记已完成
        if (isSameDay(
                getLongFlowByKey(KEY_REPUTATION_TASK_UPDATE_TIME_IN_MILLIS), getCurrentTime()
            )
        ) {
            // 任务：仅同一天内更新过，才加载，否则不加载，自动会在Repo刷新
            if (collectTasks.isEmpty()) {
                try {
                    getListObject<GameTask>(KEY_REPUTATION_TASK).let {
                        collectTasks.addAll(it.mapNotNull { task ->
                            repo.gameCore.getGameTaskPool().firstOrNull { it.id == task.id }
                                ?.copy(done = task.done, opened = task.opened)
                        })
                    }
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        } else {
            collectTasks.clear()
        }

        val filteredTasks = repo.gameCore.getGameTaskPool()
        if (collectTasks.isEmpty()) {
            val maxAge = min(
                MAX_AGE, getIntFlowByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                    1
                )
            )
            collectTasks.addAll(filteredTasks.filter { it.isReputationTask() }.filter {
                maxAge >= it.talent.first() && maxAge <= it.talent[1]
            }.shuffled(RANDOM)
                .take(repo.gameCore.getReputationQuestCount())
            )

            setLongValueByKey(KEY_REPUTATION_TASK_UPDATE_TIME_IN_MILLIS, getCurrentTime())
            setListObject(KEY_REPUTATION_TASK, collectTasks)
        }
    }

    fun createPackages() {
        if (!isNetTimeValid()) {
            return
        }
        if (packages.isEmpty()) {
            getListObject(KEY_REPUTATION_SELLS, Sell.serializer()).let {
                packages.addAll(it.mapNotNull { sell ->
                    repo.gameCore.getSellPool().firstOrNull { it.id == sell.id }
                        ?.copy(opened = sell.opened, num = sell.num, storage = sell.storage)
                })
            }
        }
        val shouldShowPackages = repo.gameCore.getSellPool().filter {
            it.isReputation()
        }.sortedBy {
            it.order
        }

        val currentPackageIds = packages.map { it.id }
        val tobeAdded = shouldShowPackages.filter {
            !currentPackageIds.contains(it.id)
        }.map { sell ->
            sell
        }

        packages.addAll(tobeAdded)
        packages.removeAll { sell ->
            shouldShowPackages.none { it.id == sell.id }
        }
        setListObject(KEY_REPUTATION_SELLS, packages, Sell.serializer())
    }
    
    fun isReputationAwardGained(reputation: ReputationLevel, typeIndex: Int): Boolean {
        return awardGainedIds.contains(reputation.storeId(typeIndex))
    }

    fun gainAward(reputation: ReputationLevel, typeIndex: Int) {
        if (!isReputationAwardGained(reputation, typeIndex)) {
            awardGainedIds.add(reputation.storeId(typeIndex))
            GameApp.globalScope.launch(Dispatchers.Main) {
                reputation.toAward(typeIndex).apply {
                    Dialogs.awardDialog.value = this
                    AwardManager.gainAward(this)
                }
            }
        }
    }

    fun hasRed(reputationTypeIndex: Int): Boolean {
        val levels = toReputationLevelData()
        val pool = repo.gameCore.getReputationLevelPool().filter { it.level > 0 }
        val myLevel = levels[reputationTypeIndex]
        val new = pool.any { reputation ->
            val levelEnough = reputation.level <= myLevel.level
            val gained = isReputationAwardGained(reputation, reputationTypeIndex)
            levelEnough && !gained
        }
        return new
    }

    fun hasRed(): Boolean {
        return levelHasRed() || taskHasRed() || shopHasRed()
    }

    fun shopHasRed(): Boolean {
//        return packages.any {
//            val unlocked = UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(it.unlock))
//            unlocked && it.storage > 0
//        }
        return false
    }

    fun shopHasRed(item: Story): Boolean {
//        return packages.filter { it.isReputation(item.id) }.any {
//            val unlocked = UnlockManager.getUnlockedFlow(repo.gameCore.getUnlockById(it.unlock))
//            unlocked && it.storage > 0
//        }
//        reputations.forEachIndexed { reputationTypeIndex, _ ->
//            if (reputationTypeIndex == item.id - 1 && hasRed(reputationTypeIndex)) {
//                return true
//            }
//        }
        return false
    }

    fun levelHasRed(): Boolean {
        reputations.forEachIndexed { reputationTypeIndex, _ ->
            if (hasRed(reputationTypeIndex)) {
                return true
            }
        }
        return false
    }

    fun taskHasRed(): Boolean {
        return collectTasks.any {
            TaskManager.getTaskDoneFlow(it) && !it.opened
        }
    }

    fun getReputationLevel(targetReputation: Int): Int {
        return getReputationLevelData(targetReputation).level
    }

    fun getReputationLevelData(targetReputation: Int): ReputationLevel {
        return GameCore.instance.getReputationLevelPool().reversed()
            .firstOrNull { it.expTotal <= targetReputation }
            ?: GameCore.instance.getReputationLevelPool().first()
    }

    fun markGoogleSellItem(sell: Sell) {
        packages.indexOfFirst { it.id == sell.id }.takeIf { it >= 0 }?.let {
            packages[it] = packages[it].copy(storage = packages[it].storage - 1)
            setListObject(KEY_REPUTATION_SELLS, packages, Sell.serializer())
        }
    }

    suspend fun openPackage(sell: Sell) {
        if (sell.isDiamondMoney()) {
            AwardManager.gainDiamond(-sell.price)
        } else if (sell.isKeyMoney()) {
            AwardManager.gainKey(-sell.price)
        } else if (sell.isReputationMoney()) {
            AwardManager.gainReputationMoney(-sell.price)
        } else if (sell.isAifadian() && !GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
            AwardManager.gainRealMoney(-sell.price)
        }
        ReportManager.onShopPurchase(
            sellId = sell.id,
            price = sell.price,
            priceType = sell.priceType
        )

        val realAward = sell.toAward() + Award(adMoney = if (sell.isAd()) AdManager.getRandomAdMoney() else 0)
        AwardManager.gainAward(realAward)
        Dialogs.awardDialog.value = realAward
        setBooleanValueByKey(SELL_FOREVER + sell.id, true)
        markGoogleSellItem(sell)
    }
}
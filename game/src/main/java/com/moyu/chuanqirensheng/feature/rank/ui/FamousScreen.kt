package com.moyu.chuanqirensheng.feature.rank.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.RetrofitModel.uploadLikedData
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_MAX_ONE_DAY
import com.moyu.chuanqirensheng.feature.rank.FAMOUS_TYPE
import com.moyu.chuanqirensheng.feature.rank.LikedData
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.RankManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.core.model.Award
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val famousRanks = mutableStateOf(emptyList<RankData>())

@Composable
fun FamousScreen() {
    val likedData = remember {
        mutableStateOf(LikedData())
    }
    LaunchedEffect(Unit) {
        RankManager.init()
        famousRanks.value = emptyList()
    }
    DisposableEffect(Unit) {
        onDispose {
            if (likedData.value.isNotEmpty()) {
                GameApp.globalScope.launch {
                    uploadLikedData(likedData.value)
                }
            }
        }
    }
    GameBackground(title = stringResource(R.string.famous_rank)) {
        RankPage(FAMOUS_TYPE, famousRanks) { rankData, rankIndex ->
            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                EffectButton(onClick = {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        if (RankManager.famousLiked.value >= FAMOUS_MAX_ONE_DAY) {
                            GameApp.instance.getWrapString(R.string.top_like_count_one_day).toast()
                        } else {
                            RankManager.famousLiked.value += 1
                            likedData.value = likedData.value.addLiked(rankData.userId)
                            val award = Award(diamond = repo.gameCore.getFamousDiamond())
                            Dialogs.awardDialog.value = award
                            AwardManager.gainAward(award)
                        }
                    }
                }) {
                    Image(
                        modifier = Modifier.size(imageLarge),
                        painter = painterResource(id = R.drawable.common_menu_vote),
                        contentDescription = stringResource(R.string.like)
                    )
                }
                Text(
                    text = stringResource(R.string.like_num) + (rankData.liked + likedData.value.getExtraNum(rankData.userId)),
                    style = MaterialTheme.typography.h3
                )
            }
        }
    }
}
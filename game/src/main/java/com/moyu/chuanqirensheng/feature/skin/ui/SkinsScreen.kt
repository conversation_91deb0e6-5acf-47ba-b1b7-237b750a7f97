package com.moyu.chuanqirensheng.feature.skin.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.story.ui.SingleStoryCard
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding16

@Composable
fun SkinsScreen() {
    GameBackground(title = stringResource(R.string.skin)) {
        DisposableEffect(Unit) {
            onDispose {
                SkinManager.setAllUnNew()
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_big_frame),
                    contentScale = ContentScale.FillBounds
                )
                .verticalScroll(rememberScrollState())
        ) {
            StoryManager.stories.forEach { story ->
                SingleStoryCard(Modifier, story, showButton = false)
                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(padding16),
                    overflow = FlowRowOverflow.Visible,
                ) {
                    repo.gameCore.getSkinPool().filter { it.type == story.id }
                        .sortedByDescending { it.quality }.forEach { skin ->
                            SingleSkinCard(
                                Modifier.width(padding120),
                                SkinManager.gainedSkins.firstOrNull { it.id == skin.id } ?: skin)
                        }
                }
            }
        }
    }
}





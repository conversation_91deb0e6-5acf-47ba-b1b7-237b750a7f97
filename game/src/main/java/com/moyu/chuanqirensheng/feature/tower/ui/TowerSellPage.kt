package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.resource.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.resource.CurrentKeyPoint
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6


@Composable
fun TowerSellPage() {
    val towers = repo.gameCore.getTowerPool().filter { it.reward != 0 && it.id <= TowerManager.maxLevel.value + 50 }.filter {
        it.id !in TowerManager.gainedLevels
    }
    val listState = rememberLazyListState()

    LaunchedEffect(Unit) {
        val targetIndex = towers.indexOfFirst { it.id !in TowerManager.gainedLevels } - 1
        // Smooth scroll to the target index
        if (targetIndex >= 3) {
            listState.animateScrollToItem(targetIndex)
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(padding12))
        Row(
            Modifier
                .align(Alignment.End)
                .padding(end = padding6)
        ) {
            CurrentDiamondPoint(showPlus = true, showFrame = true)
            CurrentKeyPoint(showPlus = true, showFrame = true)
        }
        Spacer(modifier = Modifier.size(padding4))
        // 临时
        LazyColumn(
            state = listState,
            modifier = Modifier,
            verticalArrangement = Arrangement.spacedBy(padding4),
            contentPadding = PaddingValues(horizontal = padding16)
        ) {
            items(towers) { tower ->
                TowerSellItem(tower = tower)
            }
        }
        Spacer(modifier = Modifier.size(gapLarge))
    }
}
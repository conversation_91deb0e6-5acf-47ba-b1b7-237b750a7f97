package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Arrangement.Absolute.spacedBy
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.KEY_WAR_PASS2_UNLOCK_EVIDENCE
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.getPriceTextWithUnit
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.model.toAward
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun WarPass2UnlockDialog(show: MutableState<Boolean>) {
    show.value.takeIf { it }?.let {
        val sell = repo.gameCore.getSellPool().first { it.id == KEY_WAR_PASS2_UNLOCK_EVIDENCE }
        PanelDialog(onDismissRequest = {
            show.value = false
        }, contentBelow = {
            GameButton(
                buttonSize = ButtonSize.Medium,
                enabled = sell.price == 0 || sell.storage > 0,
                buttonStyle = ButtonStyle.Orange,
                text = sell.getPriceTextWithUnit()
            ) {
                // 是付费商品
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BillingManager.prepay(sell) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            sell.toAward().apply {
                                AwardManager.gainAward(this)
                                Dialogs.awardDialog.value = this
                            }
                            show.value = false
                        }
                    }
                }
            }
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
            ) {
                Text(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    text = stringResource(R.string.unlock_warpass2),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                Text(
                    text = stringResource(R.string.unlock_all_gain),
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                val warPassLocked = repo.gameCore.getBattlePass2Pool().filter { it.unlockType == 2 }
                AwardList(
                    Modifier,
                    award = warPassLocked.map { it.toAward() }.toAward(),
                    mainAxisAlignment = spacedBy(padding10),
                    param = defaultParam.copy(
                        peek = true,
                        textColor = Color.Black,
                        itemSize = ItemSize.LargePlus,
                    ),
                )
                if (warPassLocked.filter {
                        it.level <= (BattlePass2Manager.getCurrentWarPass()?.level ?: 0)
                    }.isNotEmpty()) {
                    Text(
                        text = stringResource(R.string.unlock_gain_now),
                        style = MaterialTheme.typography.h3,
                        color = Color.Black
                    )
                    AwardList(
                        Modifier,
                        award = warPassLocked.filter {
                            it.level <= (BattlePass2Manager.getCurrentWarPass()?.level ?: 0)
                        }.map { it.toAward() }.toAward(),
                        mainAxisAlignment = spacedBy(padding10),
                        param = defaultParam.copy(
                            peek = true,
                            textColor = Color.Black,
                            itemSize = ItemSize.LargePlus,
                        ),
                    )
                }
            }
        }
    }
}
package com.moyu.chuanqirensheng.feature.newTask.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.router.SEVEN_DAY_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.screen.common.LabelSize
import com.moyu.chuanqirensheng.screen.common.TextLabel
import com.moyu.chuanqirensheng.sub.datastore.KEY_INIT_GAME_TIME
import com.moyu.chuanqirensheng.sub.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding2
import com.moyu.chuanqirensheng.util.getCurrentDay
import com.moyu.chuanqirensheng.util.isNetTimeValid

@Composable
fun SevenDayIcon(modifier: Modifier = Modifier) {
    if (isNetTimeValid() && SevenDayManager.unlocked()) {
        LaunchedEffect(Unit) {
            SevenDayManager.init()
        }
        val day = getCurrentDay(getLongFlowByKey(KEY_INIT_GAME_TIME))
        // 有可能KEY_INIT_GAME_TIME==0，也表明是新用户，可以显示
        if (day <= 7 || getLongFlowByKey(KEY_INIT_GAME_TIME) == 0L) {
            Box(modifier = modifier.graphicsLayer {
                translationX = LabelSize.Large.width.toPx() / 2.2f
            }, contentAlignment = Alignment.Center) {
                TextLabel(
                    modifier = Modifier.scale(1.1f),
                    labelSize = LabelSize.Large,
                    text = stringResource(R.string.seven_day_title),
                    icon = R.drawable.seven_day_icon
                ) {
                    goto(SEVEN_DAY_SCREEN)
                }
                if (SevenDayManager.hasRed()) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopCenter).padding(end = padding10, top = padding2)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
        }
    }
}

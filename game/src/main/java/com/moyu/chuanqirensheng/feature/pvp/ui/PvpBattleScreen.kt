package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.sub.datastore.KEY_PK_ALLY_IDS
import com.moyu.chuanqirensheng.sub.datastore.setListObject
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.logic.role.ALLY_ROW1_FIRST

@Composable
fun PvpBattleScreen() {
    LaunchedEffect(Unit) {
        // 将所有角色加入到局内
        BattleManager.selectAllToGame()
        PvpManager.lastPvpAllyIds.forEachIndexed { index, savedId ->
            BattleManager.allyGameData.firstOrNull { it.id == savedId }?.let {
                BattleManager.selectAllyToBattle(
                    it, index + ALLY_ROW1_FIRST
                )
            }
        }
    }
    GameBackground(
        title = stringResource(R.string.pvp), bgMask = B65, background = R.drawable.bg_pvp
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceEvenly,
            horizontalAlignment = Alignment.CenterHorizontally) {
            if (repo.inBattle.value) {
                Spacer(modifier = Modifier.size(padding36))
                BattleFieldLayout(repo.battleRoles)
            } else {
                PvpPrepareBattleLayout {
                    if (BattleManager.getBattleAllies().values.isEmpty()) {
                        Dialogs.alertDialog.value =
                            CommonAlert(content = GameApp.instance.getWrapString(R.string.no_role_to_battle_tips),
                                onConfirm = {
                                    repo.battle.value.terminate()
                                    repo.inBattle.value = false
                                    PvpManager.pkFailed(emptyList(), repo.battleRoles.values.mapNotNull { it })
                                })
                        true
                    } else {
                        val battleAllies = BattleManager.getPvpBattleRoles()
                        PvpManager.lastPvpAllyIds.clear()
                        PvpManager.lastPvpAllyIds.addAll(battleAllies.toSortedMap().values.map { it.getAlly().id })
                        setListObject(KEY_PK_ALLY_IDS, PvpManager.lastPvpAllyIds)
                        repo.setCurrentAllies(battleAllies)
                        repo.startBattle()
                        true
                    }
                }
            }
        }
        SettingColumn(
            Modifier
                .align(Alignment.CenterStart)
                .padding(start = padding6), settingBattleItems
        )
    }
}
package com.moyu.chuanqirensheng.feature.reputation.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.reputation.ReputationManager
import com.moyu.chuanqirensheng.feature.story.toReputationName
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding66
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.reputationItemHeight


@Composable
fun ReputationLayout() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AwardManager.toReputationLevelData()
            .forEachIndexed { index, reputationLevel ->
                val name = (index + 1).toReputationName()
                val currentExp =
                    AwardManager.reputations[index] - (reputationLevel.expTotal)
                Row(
                    modifier = Modifier
                        .height(reputationItemHeight)
                        .fillMaxWidth()
                        .paint(
                            painterResource(R.drawable.common_frame_long),
                            contentScale = ContentScale.FillBounds,
                        )
                        .padding(padding10),
                    verticalAlignment = Alignment.CenterVertically,
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
                            Image(
                                painter = painterResource(ReputationManager.getImageByIndex(index)),
                                modifier = Modifier
                                    .size(padding66).clip(RoundedCornerShape(50)),
                                contentDescription = null
                            )
                            Image(
                                modifier = Modifier.size(padding80),
                                contentScale = ContentScale.FillHeight,
                                painter = painterResource(id = R.drawable.hero_frame2),
                                contentDescription = null
                            )
                        }
                        Text(
                            text = name,
                            style = MaterialTheme.typography.h2,
                        )
                    }

                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight(),
                        verticalArrangement = Arrangement.SpaceEvenly,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Text(
                            text = reputationLevel.name,
                            color = Color.White,
                            style = MaterialTheme.typography.h2
                        )
                        Box(contentAlignment = Alignment.Center) {
                            CommonBar(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(horizontal = padding8)
                                    .height(padding36),
                                currentValue = currentExp,
                                maxValue = reputationLevel.exp,
                                fullRes = R.drawable.common_card_line,
                                emptyRes = R.drawable.common_card_empty,
                                textColor = Color.White,
                                style = MaterialTheme.typography.h4
                            )
                        }
                    }
                    Spacer(modifier = Modifier.size(padding10))
                    GameButton(
                        text = stringResource(R.string.reputation_award),
                        buttonSize = ButtonSize.Medium,
                        enabled = ReputationManager.hasRed(index),
                        buttonStyle = ButtonStyle.Orange,
                        onClick = {
                            Dialogs.reputationRewardDialog.value = index
                        })
                }
            }
    }
}
package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getPvpByScore
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager.targetsFromServer
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.rank.RankManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.padding110
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding45
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber


@Composable
fun PvpChooseEnemyScreen() {
    LaunchedEffect(Unit) {
        RankManager.init()
    }
    GameBackground(
        title = stringResource(R.string.pvp_choose_enemy), bgMask = B65, background = R.drawable.bg_pvp
    ) {
        LaunchedEffect(PvpManager.pvpScore.value) {
            try {
                delay(200)
                // 拉取pk对手列表
                getPvpByScore(
                    GameApp.instance.resources.getString(
                        R.string.platform_channel
                    ), PvpManager.pvpScore.value
                ).let {
                    targetsFromServer.value =
                        json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                            .filter { it.userId != GameApp.instance.getObjectId() }.filter {
                                it.userId !in PvpManager.pkTargetList
                            }
                    if (targetsFromServer.value.size < 5) {
                        targetsFromServer.value += (PvpManager.getMockPvpData().shuffled(RANDOM)
                            .filter {
                                it.userId !in PvpManager.pkTargetList
                            })
                    }
                    PvpManager.refreshTargets()
                }
            } catch (e: Exception) {
                Timber.e(e)
                GameApp.instance.getWrapString(R.string.net_error_retry).toast()
            }
        }
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = padding6)
        ) {
            PvpTopDataRow(
                Modifier
                    .fillMaxWidth()
                    .padding(top = padding6)
                    .background(W30))
            Spacer(modifier = Modifier.size(padding12))
            PvpManager.currentTargets.value.forEach {
                SinglePvpRecord(it)
            }
            Spacer(modifier = Modifier.size(padding45))
            if (targetsFromServer.value.isNotEmpty()) {
                GameButton(
                    buttonSize = ButtonSize.Big,
                    buttonStyle = ButtonStyle.Orange,
                    text = stringResource(id = R.string.refresh)
                ) {
                    PvpManager.refreshTargets()
                }
                Spacer(modifier = Modifier.size(padding8))
                Text(
                    text = stringResource(
                        R.string.today_pvp_num,
                        minOf(PvpManager.pkNumToday.value, MAX_PVP_NUM + VipManager.getExtraPvpNum()),
                        MAX_PVP_NUM + VipManager.getExtraPvpNum()
                    ),
                    style = MaterialTheme.typography.h3,
                    color = Color.White
                )
            }
        }
    }
}


@Composable
fun SinglePvpRecord(deathRole: RankData) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(padding110)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(R.drawable.common_frame_long),
            contentDescription = null
        )
        Row(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding19),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
            ) {
                Box(modifier = Modifier.size(padding80)) {
                    Image(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(padding8)
                            .clip(RoundedCornerShape(50)),
                        painter = if (deathRole.userPic.startsWith("http")) rememberAsyncImagePainter(
                            deathRole.userPic
                        ) else painterResource(
                            id = getImageResourceDrawable(deathRole.userPic)
                        ),
                        contentDescription = null
                    )
                    Image(
                        modifier = Modifier.fillMaxSize(),
                        painter = painterResource(id = R.drawable.hero_frame2),
                        contentDescription = null
                    )
                }
                Column(
                    modifier = Modifier
                        .weight(1f)
                        .fillMaxHeight(),
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    Text(
                        text = deathRole.userName, style = MaterialTheme.typography.h2
                    )
                    Text(
                        text = stringResource(R.string.pvp_score) + (deathRole.pvpScore),
                        style = MaterialTheme.typography.h4
                    )
                }
                GameButton(
                    text = stringResource(R.string.challenge),
                    enabled = !PvpManager.pkTargetList.contains(deathRole.userId)
                ) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                    } else {
                        if (!PvpManager.pkTargetList.contains(deathRole.userId)) {
                            PvpManager.pk(deathRole)
                        } else {
                            GameApp.instance.getWrapString(R.string.duplicated_pk_target_tips).toast()
                        }
                    }
                }
            }
        }
    }
}

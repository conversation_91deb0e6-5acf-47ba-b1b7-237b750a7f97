package com.moyu.chuanqirensheng.util

import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.moyu.chuanqirensheng.application.GameApp

const val designHeightInDp = 900
val screenWidthInPixel = GameApp.instance.resources.displayMetrics.widthPixels
val screenHeightInDp by lazy { GameApp.instance.resources.displayMetrics.heightPixels.pixelToDp().value }

val immersionBarHeightInDp by lazy {
    statusBarHeightInPixel.pixelToDp()
}

fun Int.pixelToDp(): Dp {
    val m: Float = GameApp.instance.resources.displayMetrics.density
    return (this / m + 0.5f).dp
}

fun Dp.toPixel(): Int {
    val m: Float = GameApp.instance.resources.displayMetrics.density
    return (this.value * m + 0.5f).toInt()
}

fun Number.composeDp() = Dp(toFloat() * screenHeightInDp / designHeightInDp)

fun Number.composeSp() = (toFloat() * screenHeightInDp / designHeightInDp).sp

val statusBarHeightInPixel by lazy {
    val insets = ViewCompat.getRootWindowInsets(GameApp.instance.activity.window.decorView)
    (insets?.getInsets(WindowInsetsCompat.Type.statusBars())?.top ?: 0)
}
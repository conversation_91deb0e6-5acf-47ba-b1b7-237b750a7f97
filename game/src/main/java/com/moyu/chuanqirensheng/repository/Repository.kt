package com.moyu.chuanqirensheng.repository

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.feature.battlepass.BattlePass2Manager
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.feature.continuegame.ContinueManager
import com.moyu.chuanqirensheng.feature.continuegame.DetailProgressManager
import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.chuanqirensheng.feature.ending.EndingManager
import com.moyu.chuanqirensheng.feature.gamemode.GameMode
import com.moyu.chuanqirensheng.feature.gamemode.MODE_NORMAL
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.illustration.GameIllustrationManager
import com.moyu.chuanqirensheng.feature.limit.GameDataManager
import com.moyu.chuanqirensheng.feature.mission.MissionManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.router.CREATE_GAME_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.sign.SignManager
import com.moyu.chuanqirensheng.feature.skin.SkinManager
import com.moyu.chuanqirensheng.feature.story.StoryManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.feature.tcg.TcgManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.feature.vip.VipManager
import com.moyu.chuanqirensheng.logic.ally.AllyManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.judge.GameJudge
import com.moyu.chuanqirensheng.logic.judge.GameJudgeManager
import com.moyu.chuanqirensheng.logic.setting.SettingManager
import com.moyu.chuanqirensheng.logic.skill.SkillManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.cardEffects
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager
import com.moyu.chuanqirensheng.sub.datastore.KEY_FIRST_ENDING_AWARD
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.sub.share.ShareManager
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.logic.role.positionListAllies
import com.moyu.core.logic.role.positionListEnemy
import com.moyu.core.model.environment.Normal
import com.moyu.core.model.info.BattleInfo
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.role.Role
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

val repo = Repo()

class Repo(private val judge: GameJudge = GameJudgeManager()) : GameJudge by judge {
    val gameCore = GameCore(battleCallback)
    val battle = mutableStateOf(GameCore.EMPTY)
    val battleEnvironment = mutableStateOf(Normal)
    val battleRoles = mutableStateMapOf<Int, Role?>()
    val battleInfo = mutableStateListOf<BattleInfo>()
    val lifeInfo = mutableStateListOf<BattleInfo>()
    val inGame = mutableStateOf(false)
    val gameMode = mutableStateOf(GameMode())

    val battleTurn = mutableIntStateOf(0)

    val skillManager = SkillManager()
    val allyManager = AllyManager()

    suspend fun doInit() {
        allyManager.init()
        skillManager.init()
        AwardManager.init()
        SkinManager.init()
        TcgManager.init()
        SellManager.init()
        DifficultManager.init()
        QuestManager.init()
        TalentManager.init()
        UnlockManager.init()
        StoryManager.init()
        EndingManager.init()
        CloudSaverManager.init()
        AntiCheatManager.init()
        VipManager.init()
        BattlePassManager.init()
        BattlePass2Manager.init()
        AntiCheatManager.init()
        GameDataManager.init()
        SettingManager.init()
        SignManager.init()
        MissionManager.init()
        ShareManager.init()
        GameIllustrationManager.init()
        ReportManager.init()
        GameReviewManager.init()
        GiftManager.init()
        TowerManager.init()
    }

    suspend fun doInitAfterLogin() {
        QuestManager.init()
        SellManager.init()
        PvpManager.init()
        SevenDayManager.init()
        MonthCardManager.init()
    }

    fun startGame() {
        if (!repo.inGame.value) {
            GameDataManager.tryPlay {
                gameMode.value = GameMode(MODE_NORMAL)
                repo.inGame.value = true
                lifeInfo.clear()
                cardEffects.value = null
                ReportManager.onNewGame(MODE_NORMAL)
                EventManager.onNewGame()
                BattleManager.onNewGame()
                DetailProgressManager.onNewGame()
                GameApp.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(null, true)
                }
            }
        }
    }

    fun continueGame() {
        gameMode.value = GameMode(MODE_NORMAL)
        ReportManager.onContinueGame(MODE_NORMAL)
        repo.inGame.value = true
        lifeInfo.clear()
    }

    fun startBattle() {
        gameOver.value = false
        // 保护，防止用户作弊
        if (gameOver.value || inBattle.value) return

        GameCore.instance.onBattleEffect(SoundEffect.ExploreFinish)
        battleInfo.clear()
        battleEnvironment.value = Normal
        inBattle.value = true
        playerMusicByScreen() // 音乐
        GameApp.globalScope.launch(gameDispatcher) {
            battleRoles.values.mapNotNull { it }.forEach {
                it.setBuffList(emptyList())
                if (gameMode.value.isTowerMode()) {
                    // todo https://xkff20230903033446466.pingcode.com/pjm/workitems/TyMWL_l9?
                    //#YXW-871 爬塔，战败的时候点击+号上阵框，就可以继续上阵兵种，继续打，一直重复操作可以打到敌方死亡为止
                    it.setPropertyToDefault()
                }
            }
            battle.value = gameCore.createBattleField(battleRoles.toMap())
            battle.value.startBattle()
        }
    }

    fun onBattleInfo(string: String, type: BattleInfoType) {
        if (lifeInfo.size >= 300) {
            val temp = lifeInfo.takeLast(100)
            lifeInfo.clear()
            lifeInfo.addAll(temp)
        }
        lifeInfo.add(BattleInfo(content = string, type = type))
    }

    suspend fun onBattleUpdate(battleField: BattleField) {
        withContext(Dispatchers.Main) {
            battleRoles.clear()
            battleRoles.putAll(battleField.getRoleMap())
            battleTurn.intValue = battleField.getTurn()
        }
    }

    fun setCurrentEnemies(enemies: Map<Int, Role?>) {
        positionListEnemy.forEach {
            battleRoles[it] = null
        }
        battleRoles.putAll(enemies)
    }

    fun setCurrentAllies(allies: Map<Int, Role?>) {
        positionListAllies.forEach {
            battleRoles[it] = null
        }
        battleRoles.putAll(allies)
    }

    fun clickStart() {
        // 如果有存档，弹窗
        if (ContinueManager.haveSaver()) {
            Dialogs.alertDialog.value = CommonAlert(
                title = GameApp.instance.getWrapString(R.string.continue_or_not),
                content = GameApp.instance.getWrapString(R.string.continue_contents),
                confirmText = GameApp.instance.getWrapString(R.string.continue_game),
                cancelText = GameApp.instance.getWrapString(R.string.new_game),
                onConfirm = {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        ContinueManager.recreateGame()
                        repo.continueGame()
                    }
                }, onCancel = {
                    ContinueManager.clearSave()
                    BattleManager.init()
                    if (getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
                        goto(CREATE_GAME_SCREEN)
                    } else {
                        repo.startGame()
                        GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                    }
                }
            )
        } else {
            BattleManager.init()
            if (getBooleanFlowByKey(KEY_FIRST_ENDING_AWARD)) {
                goto(CREATE_GAME_SCREEN)
            } else {
                repo.startGame()
                GameCore.instance.onBattleEffect(SoundEffect.StartGame)
            }
        }
    }

    fun isCurrentEnemyEmptyOrDead(): Boolean {
        return positionListEnemy.none { battleRoles[it] != null && !battleRoles[it]!!.isDeath() }
    }
}
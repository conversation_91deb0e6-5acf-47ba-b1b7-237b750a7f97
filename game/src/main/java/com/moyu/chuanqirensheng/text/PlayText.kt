package com.moyu.chuanqirensheng.text

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


val playNameMap = hashMapOf(
    1 to GameApp.instance.getWrapString(R.string.play_name1),
    2 to GameApp.instance.getWrapString(R.string.play_name2),
    3 to GameApp.instance.getWrapString(
        R.string.play_name3
    ),
    4 to GameApp.instance.getWrapString(R.string.play_name4),
    5 to GameApp.instance.getWrapString(R.string.play_name5),
    6 to GameApp.instance.getWrapString(R.string.play_name6),
    7 to GameApp.instance.getWrapString(
        R.string.play_name7
    ),
    8 to GameApp.instance.getWrapString(R.string.play_name8),
    9 to GameApp.instance.getWrapString(R.string.play_name9),
    10 to GameApp.instance.getWrapString(R.string.play_name10),
    11 to GameApp.instance.getWrapString(
        R.string.play_name11
    ),
    12 to GameApp.instance.getWrapString(R.string.play_name12),
    13 to GameApp.instance.getWrapString(R.string.play_name13),
    14 to GameApp.instance.getWrapString(R.string.play_name14),
    15 to GameApp.instance.getWrapString(R.string.play_name15),
    16 to GameApp.instance.getWrapString(R.string.play_name16),
    21 to GameApp.instance.getWrapString(R.string.play_name21),
    22 to GameApp.instance.getWrapString(R.string.play_name22),
    23 to GameApp.instance.getWrapString(
        R.string.play_name23
    ),
    24 to GameApp.instance.getWrapString(R.string.play_name24),
    25 to GameApp.instance.getWrapString(R.string.play_name25),
    26 to GameApp.instance.getWrapString(R.string.play_name26),
    101 to GameApp.instance.getWrapString(R.string.play_name101),
    102 to GameApp.instance.getWrapString(R.string.play_name102),
    103 to GameApp.instance.getWrapString(R.string.play_name103),
)

val playRuleMap = hashMapOf(
    1 to GameApp.instance.getWrapString(R.string.play_text1),
    2 to GameApp.instance.getWrapString(R.string.play_text2),
    3 to GameApp.instance.getWrapString(R.string.play_text3),
    4 to GameApp.instance.getWrapString(R.string.play_text4),
    5 to GameApp.instance.getWrapString(R.string.play_text5),
    6 to GameApp.instance.getWrapString(R.string.play_text6),
    7 to GameApp.instance.getWrapString(R.string.play_text7),
    8 to GameApp.instance.getWrapString(R.string.play_text8),
    9 to GameApp.instance.getWrapString(R.string.play_text9),
    10 to GameApp.instance.getWrapString(R.string.play_text10),
    11 to GameApp.instance.getWrapString(R.string.play_text11),
    12 to GameApp.instance.getWrapString(R.string.play_text12),
    13 to GameApp.instance.getWrapString(R.string.play_text13),
    14 to GameApp.instance.getWrapString(R.string.play_text14),
    15 to GameApp.instance.getWrapString(R.string.play_text15),
    16 to GameApp.instance.getWrapString(R.string.play_text16),
    21 to GameApp.instance.getWrapString(R.string.play_text21),
    22 to GameApp.instance.getWrapString(R.string.play_text22),
    23 to GameApp.instance.getWrapString(R.string.play_text23),
    24 to GameApp.instance.getWrapString(R.string.play_text24),
    25 to GameApp.instance.getWrapString(R.string.play_text25),
    26 to GameApp.instance.getWrapString(R.string.play_text26),
)

import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlinx-serialization'
    id 'kotlin-kapt'
}

if (getGradle().getStartParameter().getTaskRequests()
        .toString().contains("Oversea")) {
    apply plugin: 'com.google.gms.google-services'
    apply plugin: 'com.google.firebase.crashlytics'
}
//apply from: '../gradle/tools/mcimage.gradle'

android {
    compileSdk 35

    defaultConfig {
        applicationId "com.moyu.sanguorensheng"
        minSdk 23
        targetSdk 35
        versionCode 30708
        versionName "3.7.8"

        vectorDrawables {
            useSupportLibrary true
        }

        // Similar to other properties in the defaultConfig block,
        // you can configure the ndk block for each product flavor
        // in your build configuration.
        ndk {
            // Specifies the ABI configurations of your native
            // libraries Grad<PERSON> should build and package with your app.
            abiFilters 'armeabi-v7a', 'arm64-v8a'
        }
    }

    signingConfigs {
        release {
            storeFile file('../wujindeyuansushi.keystore')
            storePassword 'tujiulong123'
            keyAlias 'key0'
            keyPassword 'tujiulong123'
        }
    }
    productFlavors {
        hykb {
            dimension "platform"
            applicationId "com.moyu.sanguorensheng_hykb"
            resValue("string", "main_page_url", "https://www.3839.com/")
            resValue("string", "platform_channel", "hykb")
            resValue("string", "csjCodeId", "963512460")
            resValue("string", "csjAppId", "5655296")
        }
        taptap {
            dimension "platform"
            applicationId "com.moyu.sanguorensheng"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/618147")
            resValue("string", "platform_channel", "taptap")
            resValue("string", "csjCodeId", "956655848")
            resValue("string", "csjAppId", "5509435")
        }
        taiwan {
            dimension "platform"
            applicationId "com.moyu.com.moyu.sanguorensheng_taiwan"
            resValue("string", "main_page_url", "https://www.taptap.cn/app/384236")
            resValue("string", "platform_channel", "googleplay")
            resValue("string", "csjCodeId", "953443412")
            resValue("string", "csjAppId", "5428167")
        }

        china {
            dimension "region"
            resValue("bool", "has_google_service", "false")
            resValue("bool", "has_billing", "true")
            resValue("bool", "need_privacy_check", "true")
            resValue("bool", "need_anti_addict_check", "true")
            resValue("string", "serverUrl", "http://111.229.94.13:9795/yuansuqiu/api/v2/")
//            resValue("string", "serverUrl", "http://192.168.31.30:9795/yuansuqiu/api/v2/")
        }

        oversea {
            dimension "region"
            resValue("bool", "has_google_service", "true")
            resValue("bool", "has_billing", "true")
            resValue("bool", "need_privacy_check", "false")
            resValue("bool", "need_anti_addict_check", "false")
            resValue("string", "serverUrl", "http://43.129.21.152:9795/yuansuqiu/api/v2/")
        }

        lite {
            dimension "debugable"
        }
        product {
            dimension "debugable"
        }
    }
    flavorDimensions "platform", "debugable", "region"
    buildTypes {
        release {
            minifyEnabled true
            shrinkResources false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_19
        targetCompatibility JavaVersion.VERSION_19
    }
    kotlinOptions {
        jvmTarget = '19'
    }
    buildFeatures {
        compose true
        buildConfig = true
    }
    kotlin {
        jvmToolchain(19)
    }
    composeOptions {
        kotlinCompilerExtensionVersion "1.5.14"
    }
    packagingOptions {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    namespace 'com.moyu.chuanqirensheng'
}

dependencies {
    // projects
    implementation project(path: ':job')
    implementation project(path: ':core')
    // androidx
    implementation 'androidx.core:core-ktx:1.12.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    implementation 'androidx.activity:activity-compose:1.8.2'

    // compose
    implementation "androidx.compose.ui:ui:1.7.6"
    implementation "androidx.compose.material:material:1.7.6"
    // navigation
    implementation("androidx.navigation:navigation-compose:2.7.7")

    // data store
    implementation 'androidx.datastore:datastore-preferences:1.0.0'
    // timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // coil with compose 仅taptap版本需要网络图片
    implementation 'io.coil-kt:coil-compose:2.5.0'
    implementation 'io.coil-kt:coil:2.5.0'

    // charts
    implementation('io.github.bytebeats:compose-charts:0.1.2')
    // zip4j
    implementation("net.lingala.zip4j:zip4j:2.11.5")
    // ktor
    implementation("io.ktor:ktor-client-core:$ktor_version")
    implementation("io.ktor:ktor-client-content-negotiation:$ktor_version")
    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktor_version")
    implementation "io.ktor:ktor-client-okhttp:$ktor_version"
    // json
    implementation("org.jetbrains.kotlinx:kotlinx-serialization-json:1.6.3")
    // uuid
    implementation("app.softwork:kotlinx-uuid-core:0.0.12")
    // flip
    implementation "com.wajahatkarim:flippable:1.5.4"
    //exoplayer播放器
    implementation 'androidx.media3:media3-exoplayer:1.7.1'
    implementation 'androidx.media3:media3-ui:1.7.1'

    // bugly
    chinaImplementation 'com.tencent.bugly:crashreport:*******'
    // 穿山甲SDK
    chinaImplementation 'com.pangle.cn:ads-sdk-pro:*******'
    // root
    chinaImplementation 'com.scottyab:rootbeer-lib:0.1.0'

    hykbImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/hykb/libs')

    // taptap
    taptapImplementation 'com.taptap:lc-storage-android:8.2.24'
    taptapImplementation 'com.taptap:lc-realtime-android:8.2.24'
    // taptap升级sdk
    taptapImplementation 'com.squareup.okhttp3:okhttp:3.12.1'
    taptapImplementation 'com.google.android.flexbox:flexbox:3.0.0'

    chinaImplementation 'io.reactivex.rxjava2:rxandroid:2.1.1'
    taptapImplementation fileTree(include: ['*.jar', '*.aar'], dir: 'src/taptap/libs')

    //wx 支付依赖(包含统计功能)
    chinaApi 'com.tencent.mm.opensdk:wechat-sdk-android:+'

    // Add the Crashlytics Gradle plugin
    // This dependency is downloaded from the Google’s Maven repository.
    // Make sure you also include that repository in your project's build.gradle file.
    overseaImplementation 'com.google.android.play:review:2.0.2'
    // For Kotlin users, also add the Kotlin extensions library for Play In-App Review:
    overseaImplementation 'com.google.android.play:review-ktx:2.0.2'

    overseaImplementation platform('com.google.firebase:firebase-bom:32.8.0')
    overseaImplementation('com.google.firebase:firebase-crashlytics-ktx')
    overseaImplementation('com.google.firebase:firebase-analytics-ktx')
    overseaImplementation 'com.android.billingclient:billing-ktx:7.0.0'
    overseaImplementation 'com.google.android.gms:play-services-auth:21.0.0'

    overseaImplementation 'com.google.android.gms:play-services-ads:23.3.0'
    overseaImplementation("com.google.ads.mediation:mintegral:16.7.81.0")
    overseaImplementation("com.google.ads.mediation:pangle:6.1.0.9.0")
    overseaImplementation("com.google.ads.mediation:applovin:13.0.0.1")
    overseaImplementation("com.google.ads.mediation:facebook:6.18.0.0")

    overseaImplementation("com.facebook.android:facebook-android-sdk:latest.release")
    overseaImplementation "com.android.installreferrer:installreferrer:2.2"
    overseaImplementation 'com.adjust.sdk:adjust-android:5.4.1'
    overseaImplementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'
}

allprojects {
    tasks.withType(KotlinCompile).configureEach {
        kotlinOptions {
            allWarningsAsErrors = false
            freeCompilerArgs += [
                    "-opt-in=androidx.compose.animation.ExperimentalAnimationApi",
                    "-opt-in=androidx.compose.ui.ExperimentalComposeUiApi",
                    "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
                    "-opt-in=androidx.media3.common.util.UnstableApi",
                    "-opt-in=androidx.compose.foundation.layout.ExperimentalLayoutApi",
            ]
        }
    }
}